import React from 'react';

interface LoadingIndicatorProps {
  message?: string;
  size?: 'sm' | 'md' | 'lg';
  fullScreen?: boolean;
}

const LoadingIndicator: React.FC<LoadingIndicatorProps> = ({ 
  message = 'Loading...', 
  size = 'md',
  fullScreen = false
}) => {
  // Size mappings
  const sizeClasses = {
    sm: 'h-6 w-6 border-2',
    md: 'h-10 w-10 border-2',
    lg: 'h-16 w-16 border-3'
  };

  // Text size mappings
  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg'
  };

  const containerClasses = fullScreen 
    ? 'fixed inset-0 flex justify-center items-center bg-white/80 dark:bg-gray-900/80 z-50' 
    : 'flex justify-center items-center py-4';

  return (
    <div className={containerClasses}>
      <div className="flex flex-col items-center">
        <div 
          className={`animate-spin rounded-full ${sizeClasses[size]} border-t-blue-500 border-blue-200`}
        />
        {message && (
          <span className={`mt-3 text-gray-600 dark:text-gray-300 ${textSizeClasses[size]}`}>
            {message}
          </span>
        )}
      </div>
    </div>
  );
};

export default LoadingIndicator;
