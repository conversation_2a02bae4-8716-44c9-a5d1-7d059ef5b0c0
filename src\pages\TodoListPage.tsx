import React, { useState, useEffect, useRef } from 'react';
import { Link } from 'react-router-dom';
import { TodoItem } from '@/types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { format } from 'date-fns';
import { nl } from 'date-fns/locale';
import { toast } from 'sonner';
import { useAuth } from '@/context/AuthContext';
import { useUserTeam } from '@/hooks/use-user-team';
import { supabase } from '@/lib/supabase-client';
import { createTodoCompletedNotification, createTeamNotifications, checkForExpiredTodos, sendDailyTodoReminders } from '@/lib/notification-utils';
import { compressImage } from '@/lib/image-utils';
import { SimpleImageViewer } from '@/components/ui/simple-image-viewer';
import { ClipboardList, Plus, Calendar, CheckCircle, Clock, MessageSquare, History } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';

// Team colors for badges
const teamColors: Record<string, string> = {
  'Blauw': 'bg-blue-100 text-blue-800 border-blue-200',
  'Geel': 'bg-yellow-100 text-yellow-800 border-yellow-200',
  'Groen': 'bg-green-100 text-green-800 border-green-200',
  'Rood': 'bg-red-100 text-red-800 border-red-200',
  'Wit': 'bg-gray-100 text-gray-800 border-gray-200',
};

// Priority colors
const priorityColors: Record<string, string> = {
  'low': 'bg-green-100 text-green-800',
  'medium': 'bg-yellow-100 text-yellow-800',
  'high': 'bg-red-100 text-red-800',
};

const priorityLabels: Record<string, string> = {
  'low': 'Laag',
  'medium': 'Middel',
  'high': 'Hoog',
};

const TodoListPage: React.FC = () => {
  const { user } = useAuth();
  const { team, isManager } = useUserTeam();
  const [todos, setTodos] = useState<TodoItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [todoCount, setTodoCount] = useState({ total: 0, completed: 0 });
  const [commentDialogOpen, setCommentDialogOpen] = useState(false);
  const [addCommentDialogOpen, setAddCommentDialogOpen] = useState(false);
  const [currentTodo, setCurrentTodo] = useState<TodoItem | null>(null);
  const [comment, setComment] = useState('');

  // New todo form state
  const [newTodo, setNewTodo] = useState({
    title: '',
    description: '',
    priority: 'medium' as 'low' | 'medium' | 'high',
    due_date: '',
    assigned_teams: [] as string[],
  });

  // State for image uploads
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [uploadedImageUrls, setUploadedImageUrls] = useState<string[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [viewerOpen, setViewerOpen] = useState(false);
  const [viewerImages, setViewerImages] = useState<string[]>([]);
  const [viewerIndex, setViewerIndex] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Available teams
  const availableTeams = ['Blauw', 'Wit', 'Geel', 'Groen', 'Rood'];

  // Helper function to get user email from user ID
  const getUserEmail = async (userId: string | null): Promise<string> => {
    if (!userId) return 'Onbekend';

    try {
      // Try to get user from auth_users_view (which should have email)
      const { data, error } = await supabase
        .from('auth_users_view')
        .select('email')
        .eq('id', userId)
        .maybeSingle();

      if (error) {
        console.error('Error fetching user email:', error);
        return userId; // Return the ID if we can't get the email
      }

      return data?.email || userId;
    } catch (error) {
      console.error('Unexpected error fetching user email:', error);
      return userId;
    }
  };

  // Fetch todos from Supabase
  const fetchTodos = async () => {
    setIsLoading(true);
    try {
      const { data, error } = await supabase
        .from('todos')
        .select('*') // Select all columns
        .eq('completed', false) // Only show incomplete todos on the main page
        .order('created_at', { ascending: false }); // Order by creation date, newest first

      if (error) {
        console.error('Error fetching todos:', error);
        toast.error(`Fout bij ophalen van taken: ${error.message}`);
        setTodos([]); // Set to empty array on error
        setTodoCount({ total: 0, completed: 0 });
        return;
      }

      // Fetch user emails for created_by and completed_by fields
      const todosWithUserInfo = await Promise.all(
        (data as TodoItem[]).map(async (todo) => {
          const [createdByEmail, completedByEmail] = await Promise.all([
            getUserEmail(todo.created_by),
            getUserEmail(todo.completed_by)
          ]);

          return {
            ...todo,
            created_by_name: createdByEmail,
            completed_by_name: completedByEmail
          };
        })
      );

      setTodos(todosWithUserInfo);
      setTodoCount({
        total: todosWithUserInfo.length,
        completed: 0 // We're only showing incomplete todos
      });

    } catch (error) {
      console.error('Unexpected error fetching todos:', error);
      toast.error('Onverwachte fout bij ophalen van taken');
      setTodos([]);
      setTodoCount({ total: 0, completed: 0 });
    } finally {
      setIsLoading(false);
    }
  };

  // Load todos on component mount
  useEffect(() => {
    fetchTodos();

    // Check for expired todos when the component mounts
    checkForExpiredTodos();

    // Set up interval to check for expired todos
    const checkInterval = setInterval(() => {
      checkForExpiredTodos();
    }, 60 * 60 * 1000); // 1 hour in milliseconds

    return () => {
      clearInterval(checkInterval);
    };
  }, []);

  // Separate effect for daily reminders to avoid multiple calls
  useEffect(() => {
    // Only run this in the main app, not during development hot reloads
    const isMainApp = window.location.port !== '8081';

    if (isMainApp) {
      // Check if we've already sent reminders today
      const lastReminderDate = localStorage.getItem('lastTodoReminderDate');
      const today = new Date().toDateString();

      if (lastReminderDate !== today) {
        // Send daily reminders for open todos
        sendDailyTodoReminders().then(() => {
          // Store the date we sent reminders
          localStorage.setItem('lastTodoReminderDate', today);
        });
      }

      // Set up interval to send daily reminders at a specific time (e.g., 8 AM)
      const now = new Date();
      const tomorrow = new Date(now);
      tomorrow.setDate(tomorrow.getDate() + 1);
      tomorrow.setHours(8, 0, 0, 0); // 8 AM tomorrow

      // Calculate milliseconds until 8 AM tomorrow
      const msUntilTomorrow8AM = tomorrow.getTime() - now.getTime();

      // Set timeout to start the daily reminder interval
      const reminderTimeout = setTimeout(() => {
        // Send reminders immediately when the time comes
        sendDailyTodoReminders().then(() => {
          // Store the date we sent reminders
          localStorage.setItem('lastTodoReminderDate', new Date().toDateString());
        });

        // Then set up daily interval (24 hours)
        const reminderInterval = setInterval(() => {
          sendDailyTodoReminders().then(() => {
            // Store the date we sent reminders
            localStorage.setItem('lastTodoReminderDate', new Date().toDateString());
          });
        }, 24 * 60 * 60 * 1000); // 24 hours in milliseconds

        // Clean up the reminder interval when component unmounts
        return () => clearInterval(reminderInterval);
      }, msUntilTomorrow8AM);

      return () => {
        clearTimeout(reminderTimeout);
      };
    }
  }, []);

  // Set up Realtime subscription for todos
  useEffect(() => {
    // Don't set up subscription if user isn't authenticated
    if (!user) return;

    console.log('Setting up Realtime subscription for todos...');

    const channel = supabase
      .channel('todos_changes')
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'todos' },
        (payload) => {
          console.log('Realtime todo change received:', payload);

          // Refresh todos from the database to ensure we have the latest data
          // This is simpler than trying to update the local state based on the payload
          fetchTodos();
        }
      )
      .subscribe((status, err) => {
        if (status === 'SUBSCRIBED') {
          console.log('Successfully subscribed to todos changes!');
        } else if (status === 'CHANNEL_ERROR' || status === 'TIMED_OUT') {
          console.error(`Subscription error: ${status}`, err);
          toast.error('Realtime connection error');
        } else {
          console.log(`Subscription status: ${status}`);
        }
      });

    // Clean up subscription when component unmounts
    return () => {
      console.log('Cleaning up Realtime subscription for todos...');
      supabase.removeChannel(channel);
    };
  }, [user]);

  // Upload images to Supabase Storage with minimal compression
  const uploadImages = async (files: File[]): Promise<string[]> => {
    if (files.length === 0) return [];

    const imageUrls: string[] = [];
    setIsUploading(true);

    try {
      // Upload each file to Supabase Storage
      for (const file of files) {
        // Check file size before upload
        const fileSizeMB = file.size / (1024 * 1024);
        const fileSizeKB = file.size / 1024;
        console.log(`Processing image: ${file.name} (${fileSizeMB.toFixed(2)}MB / ${fileSizeKB.toFixed(2)}KB)`);
        console.log(`Image type: ${file.type}`);

        // We'll always use the original file without compression
        // This is to diagnose why images are being reduced in size
        let fileToUpload = file;

        // Log the file details
        console.log(`Using original file without compression: ${file.name}`);

        // Check if file is larger than 3MB (bucket limit)
        if (fileSizeMB > 3) {
          console.warn(`Image larger than 3MB: ${fileSizeMB.toFixed(2)}MB. Supabase may reject it.`);
          toast.warning(`Afbeelding groter dan 3MB: ${file.name}. Supabase kan deze mogelijk weigeren.`);
        }

        const fileExt = file.name.split('.').pop();
        const fileName = `${Math.random().toString(36).substring(2, 15)}_${Date.now()}.${fileExt}`;
        const filePath = `todos/${fileName}`;

        // Upload the file to Supabase Storage
        const { data, error } = await supabase.storage
          .from('images')
          .upload(filePath, fileToUpload);

        if (error) {
          console.error('Error uploading image:', error);
          toast.error(`Fout bij uploaden van afbeelding: ${error.message}`);
          continue;
        }

        // Get signed URL for the uploaded image (expires after 7 days)
        const { data: urlData, error: urlError } = await supabase.storage
          .from('images')
          .createSignedUrl(`todos/${fileName}`, 60 * 60 * 24 * 7); // 7 days expiration

        if (urlError) {
          console.error('Error creating signed URL:', urlError);
          toast.error(`Fout bij maken van URL: ${urlError.message}`);
          continue;
        }

        if (urlData?.signedUrl) {
          imageUrls.push(urlData.signedUrl);
          console.log(`Created signed URL: ${urlData.signedUrl}`);

          // Check the size of the uploaded file in Supabase
          const { data: fileInfo, error: fileInfoError } = await supabase.storage
            .from('images')
            .list('todos', {
              search: fileName
            });

          if (fileInfoError) {
            console.error('Error getting file info:', fileInfoError);
          } else if (fileInfo && fileInfo.length > 0) {
            const uploadedFile = fileInfo[0];
            console.log(`Uploaded file info:`, uploadedFile);
            console.log(`Uploaded size: ${(uploadedFile.metadata.size / 1024).toFixed(2)}KB`);
          }

          console.log(`Successfully uploaded image: ${file.name}`);
        }
      }

      return imageUrls;
    } catch (error) {
      console.error('Unexpected error uploading images:', error);
      toast.error('Fout bij uploaden van afbeeldingen');
      return [];
    } finally {
      setIsUploading(false);
    }
  };

  // Handle adding a new todo
  const handleAddTodo = async () => {
    if (!newTodo.title.trim()) {
      toast.error('Vul een titel in voor de taak');
      return;
    }

    if (newTodo.assigned_teams.length === 0) {
      toast.error('Selecteer ten minste één ploeg');
      return;
    }

    try {
      // Upload images if any are selected
      let imageUrls: string[] = [];
      if (selectedFiles.length > 0) {
        imageUrls = await uploadImages(selectedFiles);
      }

      // Prepare data for Supabase insertion
      const todoToInsert = {
        title: newTodo.title,
        description: newTodo.description,
        priority: newTodo.priority,
        assigned_teams: newTodo.assigned_teams,
        due_date: newTodo.due_date || null, // Use null if empty
        images: imageUrls.length > 0 ? imageUrls : null, // Add images if any were uploaded
        // created_by will be set by the default value in the database (auth.uid())
        // completed defaults to false in the database
      };

      const { error } = await supabase
        .from('todos')
        .insert([todoToInsert]); // Insert the prepared object

      if (error) {
        console.error('Error adding todo:', error);
        toast.error(`Fout bij toevoegen van taak: ${error.message}`);
        return; // Stop execution if insertion failed
      }

      // Refresh the list from the database instead of updating local state
      fetchTodos();

      // Reset form and close dialog
      setNewTodo({
        title: '',
        description: '',
        priority: 'medium',
        due_date: '',
        assigned_teams: [],
      });
      setSelectedFiles([]);
      setUploadedImageUrls([]);
      setIsAddDialogOpen(false);

      // Get the ID of the newly created todo
      const { data: newTodoData, error: fetchError } = await supabase
        .from('todos')
        .select('id')
        .eq('title', newTodo.title)
        .eq('created_by', user?.id)
        .order('created_at', { ascending: false })
        .limit(1);

      if (fetchError) {
        console.error('Error fetching new todo ID:', fetchError);
      }

      const todoId = newTodoData && newTodoData.length > 0 ? newTodoData[0].id : null;

      // Send notifications to assigned teams
      const dueDateText = newTodo.due_date ? ` (deadline: ${format(new Date(newTodo.due_date), 'dd-MM-yyyy', { locale: nl })})` : '';
      await createTeamNotifications(
        newTodo.assigned_teams,
        `Nieuwe taak "${newTodo.title}"${dueDateText} is aan je ploeg toegewezen.`,
        'todo_assigned',
        todoId, // Now we have the ID to use as related_id
        {
          todoTitle: newTodo.title,
          createdBy: user?.id,
          createdByName: user?.email,
          dueDate: newTodo.due_date,
          priority: newTodo.priority
        }
      );

      // Show success message
      toast.success('Taak toegevoegd');
      toast.info(`Notificatie verzonden naar ${newTodo.assigned_teams.join(', ')}`);
    } catch (error) {
      console.error('Error adding todo:', error);
      toast.error('Fout bij toevoegen van taak');
    }
  };

  // Open comment dialog for a todo
  const openCommentDialog = (todo: TodoItem) => {
    setCurrentTodo(todo);
    setComment(todo.comments || '');
    setCommentDialogOpen(true);
  };

  // Open add comment dialog for a todo
  const openAddCommentDialog = (todo: TodoItem) => {
    setCurrentTodo(todo);
    setComment(todo.comments || '');
    setAddCommentDialogOpen(true);
  };

  // Handle completing a todo with comment
  const handleCompleteTodo = async () => {
    if (!currentTodo) return;

    try {
      // Prepare the update object for Supabase
      const updates = {
        completed: true,
        completed_at: new Date().toISOString(),
        completed_by: user?.id, // Use user ID (UUID)
        comments: comment
      };

      const { error } = await supabase
        .from('todos')
        .update(updates)
        .eq('id', currentTodo.id); // Match the specific todo ID

      if (error) {
        console.error('Error completing todo:', error);
        toast.error(`Fout bij afronden van taak: ${error.message}`);
        return; // Stop execution if update failed
      }

      // Refresh the list from the database to reflect the change
      fetchTodos();

      // Close dialog and reset state
      setCommentDialogOpen(false);
      setCurrentTodo(null);
      setComment('');

      // Send notification to the creator of the todo
      if (currentTodo.created_by && currentTodo.created_by !== user?.id) {
        const completedByName = user?.email || 'Onbekend';
        await createTodoCompletedNotification(
          {
            ...currentTodo,
            completed: true,
            completed_at: new Date().toISOString(),
            completed_by: user?.id,
            comments: comment
          },
          completedByName
        );
      }

      // Show success message
      toast.success('Taak afgerond');
    } catch (error) {
      console.error('Error completing todo:', error);
      toast.error('Fout bij afronden van taak');
    }
  };

  // Handle adding a comment to a todo without completing it
  const handleAddComment = async () => {
    if (!currentTodo) return;

    try {
      // Prepare the update object for Supabase
      const updates = {
        comments: comment
      };

      const { error } = await supabase
        .from('todos')
        .update(updates)
        .eq('id', currentTodo.id); // Match the specific todo ID

      if (error) {
        console.error('Error adding comment to todo:', error);
        toast.error(`Fout bij toevoegen van opmerking: ${error.message}`);
        return; // Stop execution if update failed
      }

      // Refresh the list from the database to reflect the change
      fetchTodos();

      // Close dialog and reset state
      setAddCommentDialogOpen(false);
      setCurrentTodo(null);
      setComment('');

      // Show success message
      toast.success('Opmerking toegevoegd');
    } catch (error) {
      console.error('Error adding comment to todo:', error);
      toast.error('Fout bij toevoegen van opmerking');
    }
  };


  // Filter todos based on user's team if not a manager
  const filteredTodos = isManager
    ? todos
    : todos.filter(todo => !team || todo.assigned_teams.includes(team));

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Todo Lijst</h1>
        <div className="flex gap-3">
          {isManager && (
            <Button onClick={() => setIsAddDialogOpen(true)}>
              <Plus className="h-4 w-4 mr-2" /> Nieuwe Taak
            </Button>
          )}
        </div>
      </div>

      {/* Image viewer for todo images */}
      <SimpleImageViewer
        images={viewerImages}
        initialIndex={viewerIndex}
        open={viewerOpen}
        onOpenChange={setViewerOpen}
      />

      <div className="bg-white rounded-lg shadow">
        <div className="p-4 border-b">
          <div className="flex justify-between items-center">
            <h2 className="text-lg font-medium">Openstaande Taken</h2>
            <div className="text-sm text-gray-500">
              Totaal: {todoCount.total} taken
            </div>
          </div>
        </div>

        <div className="p-4">
          {isLoading ? (
            <div className="flex justify-center items-center h-32">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
            </div>
          ) : filteredTodos.length > 0 ? (
            <div className="space-y-4">
              {filteredTodos.map(todo => (
                <div
                  key={todo.id}
                  className="p-4 border rounded-md hover:shadow-sm transition-shadow"
                >
                  <div className="flex items-start gap-4">
                    <div className="flex-grow">
                      <div className="flex justify-between items-start">
                        <h3 className="font-medium">{todo.title}</h3>
                        <Badge className={priorityColors[todo.priority]}>
                          {priorityLabels[todo.priority]}
                        </Badge>
                      </div>

                      {todo.description && (
                        <p className="text-sm text-gray-600 mt-2 mb-3">
                          {todo.description}
                        </p>
                      )}

                      {todo.comments && (
                        <div className="mt-2 mb-3 p-3 bg-gray-50 border rounded-md">
                          <div className="flex items-center gap-2 mb-1">
                            <MessageSquare className="h-3.5 w-3.5 text-blue-500" />
                            <span className="text-xs font-medium text-blue-600">Opmerkingen:</span>
                          </div>
                          <p className="text-sm text-gray-700 whitespace-pre-wrap">
                            {todo.comments}
                          </p>
                        </div>
                      )}

                      {/* Display images if any */}
                      {todo.images && todo.images.length > 0 && (
                        <div className="mt-2 mb-3">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="text-xs font-medium text-blue-600">Afbeeldingen:</span>
                          </div>
                          <div className="flex flex-wrap gap-2">
                            {todo.images.map((imageUrl, index) => (
                              <div key={index} className="relative group">
                                <img
                                  src={imageUrl}
                                  alt={`Afbeelding ${index + 1}`}
                                  className="h-16 w-16 object-cover rounded-md border cursor-pointer transition-transform group-hover:scale-105"
                                  onClick={() => {
                                    // Use the existing URLs for the viewer
                                    setViewerImages(todo.images || []);
                                    setViewerIndex(index);
                                    setViewerOpen(true);
                                  }}
                                />
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      <div className="flex flex-wrap gap-2 mt-3">
                        {todo.assigned_teams.map(teamName => (
                          <Badge
                            key={teamName}
                            variant="outline"
                            className={teamColors[teamName]}
                          >
                            {teamName}
                          </Badge>
                        ))}
                      </div>

                      <div className="flex items-center gap-4 mt-3 text-xs text-gray-500">
                        <div className="flex items-center">
                          <Calendar className="h-3 w-3 mr-1" />
                          Aangemaakt: {format(new Date(todo.created_at), 'dd-MM-yyyy HH:mm', { locale: nl })}
                        </div>

                        {todo.due_date && (
                          <div className="flex items-center">
                            <Clock className="h-3 w-3 mr-1" />
                            Deadline: {format(new Date(todo.due_date), 'dd-MM-yyyy', { locale: nl })}
                          </div>
                        )}

                        <div>
                          Door: {todo.created_by_name || todo.created_by}
                        </div>
                      </div>
                    </div>

                    <div>
                      {/* Only show complete button if user has permission */}
                      {(isManager || (team && todo.assigned_teams.includes(team))) && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => openCommentDialog(todo)}
                        >
                          <CheckCircle className="h-4 w-4 mr-1" />
                          Afronden
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              Geen openstaande taken gevonden
            </div>
          )}
        </div>
      </div>

      {/* Add Todo Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Nieuwe Taak Toevoegen</DialogTitle>
            <DialogDescription>
              Voeg een nieuwe taak toe en wijs deze toe aan één of meerdere ploegen.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="title">Titel</Label>
              <Input
                id="title"
                placeholder="Taak titel"
                value={newTodo.title}
                onChange={(e) => setNewTodo(prev => ({ ...prev, title: e.target.value }))}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Beschrijving (optioneel)</Label>
              <Textarea
                id="description"
                placeholder="Beschrijving van de taak"
                value={newTodo.description}
                onChange={(e) => setNewTodo(prev => ({ ...prev, description: e.target.value }))}
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label>Prioriteit</Label>
              <div className="flex gap-4">
                {Object.entries(priorityLabels).map(([value, label]) => (
                  <div key={value} className="flex items-center gap-2">
                    <input
                      type="radio"
                      id={`priority-${value}`}
                      name="priority"
                      value={value}
                      checked={newTodo.priority === value}
                      onChange={() => setNewTodo(prev => ({ ...prev, priority: value as 'low' | 'medium' | 'high' }))}
                      className="h-4 w-4 text-blue-600"
                      aria-label={`Prioriteit: ${label}`}
                    />
                    <Label htmlFor={`priority-${value}`} className="cursor-pointer">
                      {label}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="due_date">Deadline (optioneel)</Label>
              <div className="flex items-center gap-2">
                <div className="relative">
                  <input
                    id="due_date"
                    type="date"
                    value={newTodo.due_date}
                    onChange={(e) => setNewTodo(prev => ({ ...prev, due_date: e.target.value }))}
                    className="opacity-0 absolute inset-0 w-10 h-10 cursor-pointer z-10"
                    aria-label="Selecteer deadline datum"
                    title="Selecteer deadline datum"
                  />
                  <button
                    type="button"
                    className="flex items-center justify-center h-10 w-10 rounded-md border border-input bg-background"
                    onClick={() => document.getElementById('due_date')?.showPicker()}
                    aria-label="Open kalender"
                    title="Open kalender"
                  >
                    <Calendar className="h-5 w-5 text-gray-500" />
                  </button>
                </div>
                {newTodo.due_date && (
                  <div className="text-sm text-gray-600">
                    Geselecteerd: {format(new Date(newTodo.due_date), 'dd-MM-yyyy', { locale: nl })}
                  </div>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label>Toewijzen aan ploegen</Label>
              <div className="grid grid-cols-2 gap-2">
                {availableTeams.map(teamName => (
                  <div key={teamName} className="flex items-center gap-2">
                    <Checkbox
                      id={`team-${teamName}`}
                      checked={newTodo.assigned_teams.includes(teamName)}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setNewTodo(prev => ({
                            ...prev,
                            assigned_teams: [...prev.assigned_teams, teamName]
                          }));
                        } else {
                          setNewTodo(prev => ({
                            ...prev,
                            assigned_teams: prev.assigned_teams.filter(t => t !== teamName)
                          }));
                        }
                      }}
                    />
                    <Label htmlFor={`team-${teamName}`} className="cursor-pointer">
                      {teamName}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="images">Foto's toevoegen (optioneel)</Label>
              <div className="flex flex-col gap-2">
                <Input
                  ref={fileInputRef}
                  id="images"
                  type="file"
                  accept="image/*"
                  multiple
                  onChange={(e) => {
                    if (e.target.files) {
                      const files = Array.from(e.target.files);
                      setSelectedFiles(prev => [...prev, ...files]);

                      // Reset the input so the same file can be selected again
                      if (fileInputRef.current) {
                        fileInputRef.current.value = '';
                      }
                    }
                  }}
                  className="cursor-pointer"
                />
                <p className="text-xs text-gray-500">Selecteer één of meerdere afbeeldingen om toe te voegen aan deze taak.</p>
              </div>

              {/* Preview selected images */}
              {selectedFiles.length > 0 && (
                <div className="mt-2">
                  <div className="flex justify-between items-center mb-2">
                    <p className="text-sm font-medium">{selectedFiles.length} bestand(en) geselecteerd:</p>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setSelectedFiles([])}
                      className="text-xs h-7"
                    >
                      Alles verwijderen
                    </Button>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {selectedFiles.map((file, index) => (
                      <div key={index} className="relative group">
                        <img
                          src={URL.createObjectURL(file)}
                          alt={`Preview ${index + 1}`}
                          className="h-20 w-20 object-cover rounded-md border cursor-pointer transition-transform group-hover:scale-105"
                          onClick={() => {
                            // For previews, we use the local object URLs
                            const previewUrls = selectedFiles.map(f => URL.createObjectURL(f));
                            setViewerImages(previewUrls);
                            setViewerIndex(index);
                            setViewerOpen(true);
                          }}
                        />
                        <button
                          type="button"
                          className="absolute -top-1 -right-1 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs opacity-0 group-hover:opacity-100 transition-opacity"
                          onClick={() => {
                            setSelectedFiles(prev => prev.filter((_, i) => i !== index));
                          }}
                        >
                          ×
                        </button>
                        <div className="absolute bottom-0 inset-x-0 bg-black/50 text-white text-[10px] truncate px-1 opacity-0 group-hover:opacity-100 transition-opacity">
                          {file.name}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {isUploading && (
                <div className="flex items-center gap-2 text-sm text-blue-600">
                  <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-blue-600"></div>
                  Bezig met uploaden...
                </div>
              )}

              {/* Image viewer for previews */}
              <SimpleImageViewer
                images={viewerImages}
                initialIndex={viewerIndex}
                open={viewerOpen}
                onOpenChange={setViewerOpen}
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
              Annuleren
            </Button>
            <Button onClick={handleAddTodo}>
              Taak Toevoegen
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Complete Todo Dialog with Comment */}
      <Dialog open={commentDialogOpen} onOpenChange={setCommentDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Taak Afronden</DialogTitle>
            <DialogDescription>
              Voeg een opmerking toe bij het afronden van deze taak.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            {currentTodo && (
              <div className="p-3 border rounded-md bg-gray-50">
                <h3 className="font-medium">{currentTodo.title}</h3>
                {currentTodo.description && (
                  <p className="text-sm text-gray-600 mt-1">{currentTodo.description}</p>
                )}
              </div>
            )}

            <div className="space-y-2">
              <Label htmlFor="comment">Opmerking (optioneel)</Label>
              <Textarea
                id="comment"
                placeholder="Voeg een opmerking toe over de voltooiing van deze taak"
                value={comment}
                onChange={(e) => setComment(e.target.value)}
                rows={3}
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setCommentDialogOpen(false)}>
              Annuleren
            </Button>
            <Button onClick={handleCompleteTodo}>
              Taak Afronden
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Add Comment Dialog */}
      <Dialog open={addCommentDialogOpen} onOpenChange={setAddCommentDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Opmerking Toevoegen</DialogTitle>
            <DialogDescription>
              Voeg een opmerking toe aan deze taak zonder deze af te ronden.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            {currentTodo && (
              <div className="p-3 border rounded-md bg-gray-50">
                <h3 className="font-medium">{currentTodo.title}</h3>
                {currentTodo.description && (
                  <p className="text-sm text-gray-600 mt-1">{currentTodo.description}</p>
                )}
              </div>
            )}

            <div className="space-y-2">
              <Label htmlFor="add-comment">Opmerking</Label>
              <Textarea
                id="add-comment"
                placeholder="Voeg een opmerking toe over deze taak"
                value={comment}
                onChange={(e) => setComment(e.target.value)}
                rows={3}
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setAddCommentDialogOpen(false)}>
              Annuleren
            </Button>
            <Button onClick={handleAddComment}>
              Opmerking Opslaan
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default TodoListPage;
