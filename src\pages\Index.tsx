
import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";

const Index = () => {
  return (
    <div className="min-h-screen w-full">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card className="dashboard-card col-span-full">
          <CardHeader>
            <CardTitle className="text-2xl font-bold text-primary">
              Welkom bij Cirrec Productiedashboard
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">
              Selecteer een menu-item om te beginnen of bekijk hieronder de recente productiegegevens.
            </p>
          </CardContent>
        </Card>
        
        <Card className="dashboard-card col-span-full md:col-span-2">
          <CardHeader>
            <CardTitle className="text-xl font-semibold">
              Productieoverzicht
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="mb-2">Huidige shift productievoortgang:</p>
            <div className="h-40 w-full bg-gray-100 rounded-md flex items-center justify-center">
              Grafiek wordt geladen...
            </div>
          </CardContent>
        </Card>
        
        <Card className="dashboard-card">
          <CardHeader>
            <CardTitle className="text-xl font-semibold">
              Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <p className="text-sm font-medium">Huidige productie</p>
                <p className="text-2xl font-bold text-green-600">Actief</p>
              </div>
              <div>
                <p className="text-sm font-medium">Laatste update</p>
                <p className="text-gray-600">5 minuten geleden</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Index;
