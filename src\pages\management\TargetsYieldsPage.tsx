// src/pages/management/TargetsYieldsPage.tsx
import React, { useState, useEffect, useCallback } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger, TabsContent } from '@/components/ui/tabs';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Edit2, Save, X, Pencil } from 'lucide-react';
import { Label } from "@/components/ui/label";
import { cn } from '@/lib/utils';
import { useProduction } from '@/context/ProductionContext';
import toast from '@/hooks/use-toast';
import { ProductionLine } from '@/types';

// Types needed for this component
type TargetState = {
  production: string;
  yield: string;
};

type TargetsState = {
  tl1: TargetState;
  tl2: TargetState;
  p1: TargetState;
  p2: TargetState;
  p3: TargetState;
};

const TargetsYieldsPage: React.FC = () => {
  const {
    updateTargets,
    updateYieldTargets,
    TARGETS,
    YIELD_TARGETS,
    isFlyLocked, // Assuming fly lock should still apply here
  } = useProduction();

  const [activeLineTab, setActiveLineTab] = useState<ProductionLine>('tl1');
  const [editMode, setEditMode] = useState(false);

  const [targets, setTargets] = useState<TargetsState>(() => {
    const initialTargets: TargetsState = {
      tl1: { production: (TARGETS.tl1 * 3).toString(), yield: YIELD_TARGETS.tl1.toString() },
      tl2: { production: (TARGETS.tl2 * 3).toString(), yield: YIELD_TARGETS.tl2.toString() },
      p1: { production: (TARGETS.p1 * 3).toString(), yield: YIELD_TARGETS.p1.toString() },
      p2: { production: (TARGETS.p2 * 3).toString(), yield: YIELD_TARGETS.p2.toString() },
      p3: { production: (TARGETS.p3 * 3).toString(), yield: YIELD_TARGETS.p3.toString() }
    };
    return initialTargets;
  });

  const [tempTargets, setTempTargets] = useState<TargetsState>(targets);

  useEffect(() => {
    const updatedTargets: TargetsState = {
      tl1: { production: (TARGETS.tl1 * 3).toString(), yield: YIELD_TARGETS.tl1.toString() },
      tl2: { production: (TARGETS.tl2 * 3).toString(), yield: YIELD_TARGETS.tl2.toString() },
      p1: { production: (TARGETS.p1 * 3).toString(), yield: YIELD_TARGETS.p1.toString() },
      p2: { production: (TARGETS.p2 * 3).toString(), yield: YIELD_TARGETS.p2.toString() },
      p3: { production: (TARGETS.p3 * 3).toString(), yield: YIELD_TARGETS.p3.toString() }
    };
    setTargets(updatedTargets);
    if (!editMode) {
      setTempTargets(updatedTargets);
    }
  }, [TARGETS, YIELD_TARGETS, editMode]);

  const productionLines: ProductionLine[] = ['tl1', 'tl2', 'p1', 'p2', 'p3'];

  const handleEdit = () => {
    setTempTargets(targets);
    setEditMode(true);
  };

  const handleCancel = () => {
    setTempTargets(targets);
    setEditMode(false);
  };

  const handleSave = () => {
    const newProductionTargets: Record<ProductionLine, number> = {} as Record<ProductionLine, number>;
    const newYieldTargets: Record<ProductionLine, number> = {} as Record<ProductionLine, number>;
    let hasError = false;

    productionLines.forEach(line => {
      const prodValue = parseFloat(tempTargets[line].production);
      const yieldValue = parseFloat(tempTargets[line].yield);

      if (isNaN(prodValue) || prodValue < 0) {
        toast.error(`Ongeldige productie target voor ${line.toUpperCase()}`);
        hasError = true;
      }
      if (isNaN(yieldValue) || yieldValue < 0 || yieldValue > 100) {
        toast.error(`Ongeldige yield target voor ${line.toUpperCase()} (moet tussen 0-100 zijn)`);
        hasError = true;
      }

      newProductionTargets[line] = prodValue / 3; // Store per shift
      newYieldTargets[line] = yieldValue;
    });

    if (!hasError) {
      updateTargets(newProductionTargets);
      updateYieldTargets(newYieldTargets);
      setTargets(tempTargets); // Update main state after successful save
      setEditMode(false);
      toast.success("Targets opgeslagen");
    }
  };

  const handleActiveLineChange = (value: string) => {
     setActiveLineTab(value as ProductionLine);
   };

  return (
    <Card className="p-6">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold">Productie Targets & Yields</h2>
        <div className="space-x-2">
          {editMode ? (
            <>
              <Button variant="outline" size="sm" onClick={handleCancel} disabled={isFlyLocked}>
                <X className="mr-1 h-4 w-4" /> Annuleren
              </Button>
              <Button size="sm" onClick={handleSave} disabled={isFlyLocked}>
                <Save className="mr-1 h-4 w-4" /> Opslaan
              </Button>
            </>
          ) : (
            <Button size="sm" onClick={handleEdit} disabled={isFlyLocked}>
              <Pencil className="mr-1 h-4 w-4" /> Bewerken
            </Button>
          )}
        </div>
      </div>

      <Tabs value={activeLineTab} onValueChange={handleActiveLineChange} className="space-y-4">
        <TabsList className="grid grid-cols-5 gap-4">
          {productionLines.map(line => (
            <TabsTrigger
              key={line}
              value={line}
              className="flex-1"
              disabled={isFlyLocked && editMode}
            >
              {line.toUpperCase()}
            </TabsTrigger>
          ))}
        </TabsList>

        {productionLines.map(line => (
          <TabsContent key={line} value={line}>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor={`${line}-production`}>Productie Target (per dag)</Label>
                <Input
                  type="number"
                  id={`${line}-production`}
                  value={editMode ? tempTargets[line].production : targets[line].production}
                  onChange={(e) => editMode && setTempTargets(prev => ({
                    ...prev,
                    [line]: { ...prev[line], production: e.target.value }
                  }))}
                  readOnly={!editMode}
                  className={cn("md:text-sm", !editMode && "bg-gray-100")}
                />
              </div>
              <div>
                <Label htmlFor={`${line}-yield`}>Yield Target (%)</Label>
                <Input
                  type="number"
                  id={`${line}-yield`}
                  min="0"
                  max="100"
                  value={editMode ? tempTargets[line].yield : targets[line].yield}
                  onChange={(e) => editMode && setTempTargets(prev => ({
                    ...prev,
                    [line]: { ...prev[line], yield: e.target.value }
                  }))}
                  readOnly={!editMode}
                  className={cn("md:text-sm", !editMode && "bg-gray-100")}
                />
              </div>
            </div>
          </TabsContent>
        ))}
      </Tabs>
    </Card>
  );
};

export default TargetsYieldsPage;