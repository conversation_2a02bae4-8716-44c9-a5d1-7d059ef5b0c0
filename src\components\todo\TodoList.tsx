import React, { useState, useEffect } from 'react';
import { TodoItem } from '@/types';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { format } from 'date-fns';
import { nl } from 'date-fns/locale';
import { toast } from 'sonner';
import { useAuth } from '@/context/AuthContext';
import { useUserTeam } from '@/hooks/use-user-team';
import { supabase } from '@/lib/supabase-client';
import { ClipboardList, Plus, Calendar, CheckCircle, Clock } from 'lucide-react';

// Team colors for badges
const teamColors: Record<string, string> = {
  'Blauw': 'bg-blue-100 text-blue-800 border-blue-200',
  'Geel': 'bg-yellow-100 text-yellow-800 border-yellow-200',
  'Groen': 'bg-green-100 text-green-800 border-green-200',
  'Rood': 'bg-red-100 text-red-800 border-red-200',
  'Wit': 'bg-gray-100 text-gray-800 border-gray-200',
};

// Priority colors
const priorityColors: Record<string, string> = {
  'low': 'bg-green-100 text-green-800',
  'medium': 'bg-yellow-100 text-yellow-800',
  'high': 'bg-red-100 text-red-800',
};

const priorityLabels: Record<string, string> = {
  'low': 'Laag',
  'medium': 'Middel',
  'high': 'Hoog',
};

interface TodoListProps {
  className?: string;
}

const TodoList: React.FC<TodoListProps> = ({ className }) => {
  const { user } = useAuth();
  const { team, isManager } = useUserTeam();
  const [todos, setTodos] = useState<TodoItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [todoCount, setTodoCount] = useState({ total: 0, completed: 0 });

  // New todo form state
  const [newTodo, setNewTodo] = useState({
    title: '',
    description: '',
    priority: 'medium' as 'low' | 'medium' | 'high',
    due_date: '',
    assigned_teams: [] as string[],
  });

  // Available teams
  const availableTeams = ['Blauw', 'Wit', 'Geel', 'Groen', 'Rood'];

  // Helper function to get user email from user ID
  const getUserEmail = async (userId: string | null): Promise<string> => {
    if (!userId) return 'Onbekend';

    try {
      // Try to get user from auth_users_view (which should have email)
      const { data, error } = await supabase
        .from('auth_users_view')
        .select('email')
        .eq('id', userId)
        .maybeSingle();

      if (error) {
        console.error('Error fetching user email:', error);
        return userId; // Return the ID if we can't get the email
      }

      return data?.email || userId;
    } catch (error) {
      console.error('Unexpected error fetching user email:', error);
      return userId;
    }
  };

  // Fetch todos from Supabase
  const fetchTodos = async () => {
    setIsLoading(true);
    try {
      const { data, error } = await supabase
        .from('todos')
        .select('*') // Select all columns
        .order('created_at', { ascending: false }); // Order by creation date, newest first

      if (error) {
        console.error('Error fetching todos:', error);
        toast.error(`Fout bij ophalen van taken: ${error.message}`);
        setTodos([]); // Set to empty array on error
        setTodoCount({ total: 0, completed: 0 });
        return;
      }

      // Fetch user emails for created_by and completed_by fields
      const todosWithUserInfo = await Promise.all(
        (data as TodoItem[]).map(async (todo) => {
          const [createdByEmail, completedByEmail] = await Promise.all([
            getUserEmail(todo.created_by),
            getUserEmail(todo.completed_by)
          ]);

          return {
            ...todo,
            created_by_name: createdByEmail,
            completed_by_name: completedByEmail
          };
        })
      );

      setTodos(todosWithUserInfo);
      setTodoCount({
        total: todosWithUserInfo.length,
        completed: todosWithUserInfo.filter(todo => todo.completed).length
      });

    } catch (error) {
      console.error('Unexpected error fetching todos:', error);
      toast.error('Onverwachte fout bij ophalen van taken');
      setTodos([]);
      setTodoCount({ total: 0, completed: 0 });
    } finally {
      setIsLoading(false);
    }
  };

  // Load todos on component mount
  useEffect(() => {
    fetchTodos();
  }, []);

  // Set up Realtime subscription for todos
  useEffect(() => {
    // Don't set up subscription if user isn't authenticated
    if (!user) return;

    console.log('Setting up Realtime subscription for todos...');

    const channel = supabase
      .channel('todos_changes')
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'todos' },
        (payload) => {
          console.log('Realtime todo change received:', payload);

          // Refresh todos from the database to ensure we have the latest data
          // This is simpler than trying to update the local state based on the payload
          fetchTodos();
        }
      )
      .subscribe((status, err) => {
        if (status === 'SUBSCRIBED') {
          console.log('Successfully subscribed to todos changes!');
        } else if (status === 'CHANNEL_ERROR' || status === 'TIMED_OUT') {
          console.error(`Subscription error: ${status}`, err);
          toast.error('Realtime connection error');
        } else {
          console.log(`Subscription status: ${status}`);
        }
      });

    // Clean up subscription when component unmounts
    return () => {
      console.log('Cleaning up Realtime subscription for todos...');
      supabase.removeChannel(channel);
    };
  }, [user]);

  // Handle adding a new todo
  const handleAddTodo = async () => {
    if (!newTodo.title.trim()) {
      toast.error('Vul een titel in voor de taak');
      return;
    }

    if (newTodo.assigned_teams.length === 0) {
      toast.error('Selecteer ten minste één ploeg');
      return;
    }

    try {
      // Prepare data for Supabase insertion
      const todoToInsert = {
        title: newTodo.title,
        description: newTodo.description,
        priority: newTodo.priority,
        assigned_teams: newTodo.assigned_teams,
        due_date: newTodo.due_date || null, // Use null if empty
        // created_by will be set by the default value in the database (auth.uid())
        // completed defaults to false in the database
      };

      const { error } = await supabase
        .from('todos')
        .insert([todoToInsert]); // Insert the prepared object

      if (error) {
        console.error('Error adding todo:', error);
        toast.error(`Fout bij toevoegen van taak: ${error.message}`);
        return; // Stop execution if insertion failed
      }

      // Refresh the list from the database instead of updating local state
      fetchTodos();

      // Reset form and close dialog
      setNewTodo({
        title: '',
        description: '',
        priority: 'medium',
        due_date: '',
        assigned_teams: [],
      });
      setIsAddDialogOpen(false);

      // Show success message
      toast.success('Taak toegevoegd');

      // In a real implementation, you would send emails to the assigned teams
      toast.info(`Notificatie verzonden naar ${newTodo.assigned_teams.join(', ')}`);
    } catch (error) {
      console.error('Error adding todo:', error);
      toast.error('Fout bij toevoegen van taak');
    }
  };

  // Handle toggling a todo's completion status
  const handleToggleTodo = async (todoId: string, currentStatus: boolean) => {
    try {
      // Find the todo locally first to check permissions
      const todoToToggle = todos.find(todo => todo.id === todoId);

      if (!todoToToggle) {
        toast.error('Taak niet gevonden');
        return;
      }

      // Check if user has permission (is in the assigned team or is a manager) - RLS should also enforce this, but good to check client-side too
      const hasPermission = isManager ||
        (team && todoToToggle.assigned_teams.includes(team));

      if (!hasPermission) {
        toast.error(`U heeft geen rechten om taken voor ploeg ${todoToToggle.assigned_teams.join(', ')} af te vinken`);
        return;
      }

      // Prepare the update object for Supabase
      const updates = {
        completed: !currentStatus,
        completed_at: !currentStatus ? new Date().toISOString() : null,
        completed_by: !currentStatus ? user?.id : null, // Use user ID (UUID)
      };

      const { error } = await supabase
        .from('todos')
        .update(updates)
        .eq('id', todoId); // Match the specific todo ID

      if (error) {
        console.error('Error toggling todo:', error);
        toast.error(`Fout bij bijwerken van taak: ${error.message}`);
        return; // Stop execution if update failed
      }

      // Refresh the list from the database to reflect the change
      // This also implicitly updates the count
      fetchTodos();

      // Show success message
      toast.success(!currentStatus ? 'Taak afgerond' : 'Taak heropend');
    } catch (error) {
      console.error('Error toggling todo:', error);
      toast.error('Fout bij bijwerken van taak');
    }
  };

  // Filter todos based on user's team if not a manager
  const filteredTodos = isManager
    ? todos
    : todos.filter(todo => !team || todo.assigned_teams.includes(team));

  return (
    <div className={className}>
      {/* Todo List Dialog */}
      <Dialog>
        <DialogTrigger asChild>
          <Button variant="outline" size="sm" className="ml-2">
            <ClipboardList className="h-4 w-4 mr-1" />
            Todo List {todoCount.total > 0 && <Badge variant="secondary" className="ml-1">{todoCount.total - todoCount.completed}</Badge>}
          </Button>
        </DialogTrigger>
        <DialogContent className="max-w-4xl h-[80vh] flex flex-col">
          <DialogHeader>
            <DialogTitle>Todo List</DialogTitle>
            <DialogDescription>
              Beheer taken voor alle ploegen.
            </DialogDescription>
          </DialogHeader>

          <div className="flex justify-between items-center mb-4">
            <div className="text-sm text-gray-500">
              {todoCount.completed} van {todoCount.total} taken afgerond
            </div>
            {isManager && (
              <Button size="sm" onClick={() => setIsAddDialogOpen(true)}>
                <Plus className="h-4 w-4 mr-1" /> Nieuwe Taak
              </Button>
            )}
          </div>

          <div className="flex-grow overflow-y-auto pr-2">
            {isLoading ? (
              <div className="flex justify-center items-center h-32">
                <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
              </div>
            ) : filteredTodos.length > 0 ? (
              <div className="space-y-3">
                {filteredTodos.map(todo => (
                  <div
                    key={todo.id}
                    className={`p-3 border rounded-md ${todo.completed ? 'bg-gray-50' : 'bg-white'}`}
                  >
                    <div className="flex items-start gap-3">
                      <Checkbox
                        checked={todo.completed}
                        onCheckedChange={() => handleToggleTodo(todo.id, todo.completed)}
                        className="mt-1"
                        disabled={!isManager && (!team || !todo.assigned_teams.includes(team))}
                      />
                      <div className="flex-grow">
                        <div className="flex justify-between items-start">
                          <h3 className={`font-medium ${todo.completed ? 'line-through text-gray-500' : ''}`}>
                            {todo.title}
                          </h3>
                          <Badge className={priorityColors[todo.priority]}>
                            {priorityLabels[todo.priority]}
                          </Badge>
                        </div>

                        {todo.description && (
                          <p className="text-sm text-gray-600 mt-1 mb-2">
                            {todo.description}
                          </p>
                        )}

                        <div className="flex flex-wrap gap-2 mt-2">
                          {todo.assigned_teams.map(teamName => (
                            <Badge
                              key={teamName}
                              variant="outline"
                              className={teamColors[teamName]}
                            >
                              {teamName}
                            </Badge>
                          ))}
                        </div>

                        <div className="flex items-center gap-4 mt-3 text-xs text-gray-500">
                          <div className="flex items-center">
                            <Calendar className="h-3 w-3 mr-1" />
                            Aangemaakt: {format(new Date(todo.created_at), 'dd-MM-yyyy HH:mm', { locale: nl })}
                          </div>

                          {todo.due_date && (
                            <div className="flex items-center">
                              <Clock className="h-3 w-3 mr-1" />
                              Deadline: {format(new Date(todo.due_date), 'dd-MM-yyyy', { locale: nl })}
                            </div>
                          )}

                          <div>
                            Door: {todo.created_by_name || todo.created_by}
                          </div>
                        </div>

                        {todo.completed && todo.completed_at && (
                          <div className="mt-2 text-xs text-green-600 flex items-center">
                            <CheckCircle className="h-3 w-3 mr-1" />
                            Afgerond op {format(new Date(todo.completed_at), 'dd-MM-yyyy HH:mm', { locale: nl })}
                            {todo.completed_by && ` door ${todo.completed_by_name || todo.completed_by}`}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                Geen taken gevonden
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Add Todo Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Nieuwe Taak Toevoegen</DialogTitle>
            <DialogDescription>
              Voeg een nieuwe taak toe en wijs deze toe aan één of meerdere ploegen.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="title">Titel</Label>
              <Input
                id="title"
                placeholder="Taak titel"
                value={newTodo.title}
                onChange={(e) => setNewTodo(prev => ({ ...prev, title: e.target.value }))}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Beschrijving (optioneel)</Label>
              <Textarea
                id="description"
                placeholder="Beschrijving van de taak"
                value={newTodo.description}
                onChange={(e) => setNewTodo(prev => ({ ...prev, description: e.target.value }))}
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label>Prioriteit</Label>
              <div className="flex gap-4">
                {Object.entries(priorityLabels).map(([value, label]) => (
                  <div key={value} className="flex items-center gap-2">
                    <input
                      type="radio"
                      id={`priority-${value}`}
                      name="priority"
                      value={value}
                      checked={newTodo.priority === value}
                      onChange={() => setNewTodo(prev => ({ ...prev, priority: value as 'low' | 'medium' | 'high' }))}
                      className="h-4 w-4 text-blue-600"
                      aria-label={`Prioriteit: ${label}`} // Add aria-label
                    />
                    <Label htmlFor={`priority-${value}`} className="cursor-pointer">
                      {label}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="due_date">Deadline (optioneel)</Label>
              <Input
                id="due_date"
                type="date"
                value={newTodo.due_date}
                onChange={(e) => setNewTodo(prev => ({ ...prev, due_date: e.target.value }))}
              />
            </div>

            <div className="space-y-2">
              <Label>Toewijzen aan ploegen</Label>
              <div className="grid grid-cols-2 gap-2">
                {availableTeams.map(teamName => (
                  <div key={teamName} className="flex items-center gap-2">
                    <Checkbox
                      id={`team-${teamName}`}
                      checked={newTodo.assigned_teams.includes(teamName)}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setNewTodo(prev => ({
                            ...prev,
                            assigned_teams: [...prev.assigned_teams, teamName]
                          }));
                        } else {
                          setNewTodo(prev => ({
                            ...prev,
                            assigned_teams: prev.assigned_teams.filter(t => t !== teamName)
                          }));
                        }
                      }}
                    />
                    <Label htmlFor={`team-${teamName}`} className="cursor-pointer">
                      {teamName}
                    </Label>
                  </div>
                ))}
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
              Annuleren
            </Button>
            <Button onClick={handleAddTodo}>
              Taak Toevoegen
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default TodoList;
