/**
 * SQL script om de benodigde tabel voor localStorage data in Supabase aan te maken.
 * Dit moet worden uitgevoerd als stored procedure in Supabase.
 */
CREATE OR REPLACE FUNCTION create_local_storage_table()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Controleer of de tabel al bestaat
  IF NOT EXISTS (
    SELECT FROM pg_tables 
    WHERE schemaname = 'public' 
    AND tablename = 'local_storage_data'
  ) THEN
    -- Maak de tabel aan
    CREATE TABLE public.local_storage_data (
      id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
      key TEXT NOT NULL UNIQUE,
      value JSONB NOT NULL,
      created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
      updated_at TIMESTAMPTZ
    );

    -- Voeg index toe voor sneller zoeken op key
    CREATE INDEX idx_local_storage_key ON public.local_storage_data(key);

    -- Voeg RLS policies toe (optioneel)
    ALTER TABLE public.local_storage_data ENABLE ROW LEVEL SECURITY;
    
    -- Standaard policy: authenticated users kunnen alles
    CREATE POLICY "Authenticated users can do everything" 
    ON public.local_storage_data
    FOR ALL 
    TO authenticated
    USING (true)
    WITH CHECK (true);
    
    -- Anon users kunnen alleen lezen
    CREATE POLICY "Anon users can only read" 
    ON public.local_storage_data
    FOR SELECT 
    TO anon
    USING (true);
    
    RAISE NOTICE 'Tabel local_storage_data succesvol aangemaakt';
  ELSE
    RAISE NOTICE 'Tabel local_storage_data bestaat al';
  END IF;
END;
$$;
