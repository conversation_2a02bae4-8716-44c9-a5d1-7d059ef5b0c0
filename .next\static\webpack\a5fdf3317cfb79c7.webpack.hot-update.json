{"c": ["main", "webpack"], "r": ["app/report/page", "app/error", "app/not-found", "app/global-error"], "m": ["(app-pages-browser)/./node_modules/@floating-ui/core/dist/floating-ui.core.mjs", "(app-pages-browser)/./node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs", "(app-pages-browser)/./node_modules/@floating-ui/react-dom/dist/floating-ui.react-dom.mjs", "(app-pages-browser)/./node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.mjs", "(app-pages-browser)/./node_modules/@floating-ui/utils/dist/floating-ui.utils.mjs", "(app-pages-browser)/./node_modules/@radix-ui/number/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/primitive/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-arrow/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-collection/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-collection/node_modules/@radix-ui/react-context/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-context/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-dialog/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-dialog/node_modules/@radix-ui/primitive/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-dialog/node_modules/@radix-ui/react-compose-refs/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-dialog/node_modules/@radix-ui/react-presence/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-dialog/node_modules/@radix-ui/react-primitive/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-dialog/node_modules/@radix-ui/react-slot/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-dialog/node_modules/react-remove-scroll/dist/es2015/Combination.js", "(app-pages-browser)/./node_modules/@radix-ui/react-dialog/node_modules/react-remove-scroll/dist/es2015/SideEffect.js", "(app-pages-browser)/./node_modules/@radix-ui/react-dialog/node_modules/react-remove-scroll/dist/es2015/UI.js", "(app-pages-browser)/./node_modules/@radix-ui/react-dialog/node_modules/react-remove-scroll/dist/es2015/aggresiveCapture.js", "(app-pages-browser)/./node_modules/@radix-ui/react-dialog/node_modules/react-remove-scroll/dist/es2015/handleScroll.js", "(app-pages-browser)/./node_modules/@radix-ui/react-dialog/node_modules/react-remove-scroll/dist/es2015/medium.js", "(app-pages-browser)/./node_modules/@radix-ui/react-dialog/node_modules/react-remove-scroll/dist/es2015/sidecar.js", "(app-pages-browser)/./node_modules/@radix-ui/react-direction/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-dismissable-layer/node_modules/@radix-ui/primitive/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-dismissable-layer/node_modules/@radix-ui/react-compose-refs/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-dismissable-layer/node_modules/@radix-ui/react-primitive/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-dismissable-layer/node_modules/@radix-ui/react-slot/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-focus-guards/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-focus-scope/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-focus-scope/node_modules/@radix-ui/react-compose-refs/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-focus-scope/node_modules/@radix-ui/react-primitive/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-focus-scope/node_modules/@radix-ui/react-slot/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-id/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-label/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-popper/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-popper/node_modules/@radix-ui/react-context/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-portal/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-portal/node_modules/@radix-ui/react-compose-refs/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-portal/node_modules/@radix-ui/react-primitive/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-portal/node_modules/@radix-ui/react-slot/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-primitive/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-select/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-select/node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-select/node_modules/@radix-ui/react-focus-scope/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-select/node_modules/@radix-ui/react-portal/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-slot/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-use-previous/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-use-size/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-visually-hidden/dist/index.mjs", "(app-pages-browser)/./node_modules/aria-hidden/dist/es2015/index.js", "(app-pages-browser)/./node_modules/class-variance-authority/dist/index.mjs", "(app-pages-browser)/./node_modules/clsx/dist/clsx.mjs", "(app-pages-browser)/./node_modules/get-nonce/dist/es2015/index.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/defaultAttributes.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js", "(app-pages-browser)/./node_modules/next/dist/api/link.js", "(app-pages-browser)/./node_modules/next/dist/api/navigation.js", "(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CClaudeStorage%5Cproduction-dashboard-universe%5Csrc%5Capp%5Creport%5Cpage.tsx&server=false!", "(app-pages-browser)/./node_modules/next/dist/client/add-locale.js", "(app-pages-browser)/./node_modules/next/dist/client/get-domain-locale.js", "(app-pages-browser)/./node_modules/next/dist/client/link.js", "(app-pages-browser)/./node_modules/next/dist/client/request-idle-callback.js", "(app-pages-browser)/./node_modules/next/dist/client/resolve-href.js", "(app-pages-browser)/./node_modules/next/dist/client/use-intersection.js", "(app-pages-browser)/./node_modules/next/dist/compiled/process/browser.js", "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js", "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/escape-regexp.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/router-context.shared-runtime.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/format-url.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/index.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/interpolate-as.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/is-dynamic.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/is-local-url.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/omit.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/querystring.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/route-matcher.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/route-regex.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/router/utils/sorted-routes.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/utils.js", "(app-pages-browser)/./node_modules/react-remove-scroll-bar/dist/es2015/component.js", "(app-pages-browser)/./node_modules/react-remove-scroll-bar/dist/es2015/constants.js", "(app-pages-browser)/./node_modules/react-remove-scroll-bar/dist/es2015/index.js", "(app-pages-browser)/./node_modules/react-remove-scroll-bar/dist/es2015/utils.js", "(app-pages-browser)/./node_modules/react-remove-scroll/dist/es2015/Combination.js", "(app-pages-browser)/./node_modules/react-remove-scroll/dist/es2015/SideEffect.js", "(app-pages-browser)/./node_modules/react-remove-scroll/dist/es2015/UI.js", "(app-pages-browser)/./node_modules/react-remove-scroll/dist/es2015/aggresiveCapture.js", "(app-pages-browser)/./node_modules/react-remove-scroll/dist/es2015/handleScroll.js", "(app-pages-browser)/./node_modules/react-remove-scroll/dist/es2015/medium.js", "(app-pages-browser)/./node_modules/react-remove-scroll/dist/es2015/sidecar.js", "(app-pages-browser)/./node_modules/react-style-singleton/dist/es2015/component.js", "(app-pages-browser)/./node_modules/react-style-singleton/dist/es2015/hook.js", "(app-pages-browser)/./node_modules/react-style-singleton/dist/es2015/index.js", "(app-pages-browser)/./node_modules/react-style-singleton/dist/es2015/singleton.js", "(app-pages-browser)/./node_modules/sonner/dist/index.mjs", "(app-pages-browser)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs", "(app-pages-browser)/./node_modules/tslib/tslib.es6.mjs", "(app-pages-browser)/./node_modules/use-callback-ref/dist/es2015/assignRef.js", "(app-pages-browser)/./node_modules/use-callback-ref/dist/es2015/useMergeRef.js", "(app-pages-browser)/./node_modules/use-callback-ref/dist/es2015/useRef.js", "(app-pages-browser)/./node_modules/use-sidecar/dist/es2015/exports.js", "(app-pages-browser)/./node_modules/use-sidecar/dist/es2015/medium.js", "(app-pages-browser)/./node_modules/uuid/dist/esm-browser/native.js", "(app-pages-browser)/./node_modules/uuid/dist/esm-browser/regex.js", "(app-pages-browser)/./node_modules/uuid/dist/esm-browser/rng.js", "(app-pages-browser)/./node_modules/uuid/dist/esm-browser/stringify.js", "(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js", "(app-pages-browser)/./node_modules/uuid/dist/esm-browser/validate.js", "(app-pages-browser)/./src/app/report/page.tsx", "(app-pages-browser)/./src/components/Header.tsx", "(app-pages-browser)/./src/components/PageLayout.tsx", "(app-pages-browser)/./src/components/common/LineSelector.tsx", "(app-pages-browser)/./src/components/ui/button.tsx", "(app-pages-browser)/./src/components/ui/dialog.tsx", "(app-pages-browser)/./src/components/ui/input.tsx", "(app-pages-browser)/./src/components/ui/label.tsx", "(app-pages-browser)/./src/components/ui/select.tsx", "(app-pages-browser)/./src/components/ui/textarea.tsx", "(app-pages-browser)/./src/context/ProductionContext.tsx", "(app-pages-browser)/./src/lib/utils.ts", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CClaudeStorage%5Cproduction-dashboard-universe%5Csrc%5Capp%5Cerror.tsx&server=false!", "(app-pages-browser)/./src/app/error.tsx", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CClaudeStorage%5Cproduction-dashboard-universe%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&server=false!", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CClaudeStorage%5Cproduction-dashboard-universe%5Csrc%5Capp%5Cglobal-error.tsx&server=false!", "(app-pages-browser)/./src/app/global-error.tsx"]}