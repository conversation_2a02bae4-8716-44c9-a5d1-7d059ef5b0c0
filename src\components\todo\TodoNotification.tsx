import React, { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase-client';
import { useUserTeam } from '@/hooks/use-user-team';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/context/AuthContext'; // Needed to check if user is logged in

const TodoNotification: React.FC = () => {
  const { user } = useAuth(); // Get user status
  const { team, isManager } = useUserTeam();
  const [todoCount, setTodoCount] = useState(0);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch todos from Supabase
  const fetchTodoCount = async () => {
    // Don't fetch if user isn't logged in or if they have no team and aren't a manager
    if (!user || (!team && !isManager)) {
      setIsLoading(false);
      setTodoCount(0);
      return;
    }

    setIsLoading(true);
    try {
      // Different query logic for managers vs regular users
      if (isManager) {
        // For managers, only show notifications for:
        // 1. Recently completed todos (completed within the last 24 hours)
        // 2. Overdue todos (due date is in the past)
        const today = new Date();
        const yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);

        // Format dates for Supabase query
        const todayStr = today.toISOString();
        const yesterdayStr = yesterday.toISOString();

        // Query for recently completed todos
        const { count: completedCount, error: completedError } = await supabase
          .from('todos')
          .select('*', { count: 'exact', head: true })
          .eq('completed', true)
          .gte('completed_at', yesterdayStr);

        // Query for overdue todos
        const { count: overdueCount, error: overdueError } = await supabase
          .from('todos')
          .select('*', { count: 'exact', head: true })
          .eq('completed', false)
          .lt('due_date', todayStr.split('T')[0]); // Compare only the date part

        if (completedError) {
          console.error('Error fetching completed todos:', completedError);
        }

        if (overdueError) {
          console.error('Error fetching overdue todos:', overdueError);
        }

        // Sum up the counts
        setTodoCount((completedCount || 0) + (overdueCount || 0));
      } else if (team) {
        // For regular users, show:
        // 1. Incomplete todos for their team
        // 2. Unread notifications of type todo_assigned for their team

        // First, get incomplete todos for their team
        const { count: todoCount, error: todoError } = await supabase
          .from('todos')
          .select('*', { count: 'exact', head: true })
          .eq('completed', false)
          .contains('assigned_teams', [team]);

        if (todoError) {
          console.error('Error fetching todos for team:', todoError);
        }

        // Set the count
        setTodoCount(todoCount || 0);
      } else {
        // User has no team and is not a manager
        setTodoCount(0);
      }
    } catch (error) {
      console.error('Unexpected error fetching todo count:', error);
      setTodoCount(0);
    } finally {
      setIsLoading(false);
    }
  };

  // Load todo count on component mount and set up realtime subscription
  useEffect(() => {
    fetchTodoCount();

    // Set up realtime subscription for todos table
    if (user) {
      const channel = supabase
        .channel('todos_changes')
        .on('postgres_changes',
          { event: '*', schema: 'public', table: 'todos' },
          () => {
            // Refetch todo count whenever there's a change to the todos table
            fetchTodoCount();
          }
        )
        .subscribe();

      // Clean up subscription when component unmounts
      return () => {
        supabase.removeChannel(channel);
      };
    }
  }, [user, team, isManager]); // Re-fetch if user, team or manager status changes

  // Don't render if loading, no user, or count is zero
  if (isLoading || !user || todoCount === 0) {
    return null;
  }

  // Render only the badge, positioning will be handled in Layout.tsx
  // Use different badge colors for managers vs regular users
  return (
    <Badge
      variant={isManager ? "secondary" : "destructive"}
      className="absolute -bottom-1 -right-1 h-4 w-4 min-w-[1rem] min-h-[1rem] p-0 flex items-center justify-center text-[10px] pointer-events-none"
    >
      {todoCount}
    </Badge>
  );
};

export default TodoNotification;
