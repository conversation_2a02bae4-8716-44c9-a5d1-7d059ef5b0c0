
import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import { useProduction } from '@/context/ProductionContext';
import LoadingIndicator from '@/components/ui/LoadingIndicator';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { user, loading: authLoading } = useAuth();
  const { isLoading: dataLoading } = useProduction();
  const location = useLocation();

  // Show loading spinner if either auth or data is loading
  if (authLoading || dataLoading) {
    return (
      <LoadingIndicator
        message={authLoading ? 'Authenticating...' : 'Loading data...'}
        size="lg"
        fullScreen
      />
    );
  }

  if (!user) {
    // Redirect to the login page if not authenticated
    return <Navigate to="/auth" state={{ from: location }} replace />;
  }

  return <>{children}</>;
};

export default ProtectedRoute;
