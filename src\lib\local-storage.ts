/**
 * Utility functies voor interactie met localStorage.
 * <PERSON><PERSON>gt voor automatische JSON serialisatie/deserialisatie en foutafhandeling.
 * 
 * AANGEPAST: Nu met Supabase integratie via middleware laag.
 * De originele functies zijn behouden voor backward compatibility,
 * maar ze roepen nu de Supabase versies aan.
 */
import { saveItemToSupabase, loadItemFromSupabase, removeItemFromSupabase } from './supabase-storage';

/**
 * Slaat een item op in localStorage en Supabase.
 * @param key De sleutel waaronder het item wordt opgeslagen.
 * @param value De waarde die moet worden opgeslagen (wordt ge-JSON.stringify'd).
 * @returns true bij succes, false bij een fout.
 */
export function saveItem(key: string, value: any): boolean {
  try {
    // Originele localStorage implementatie behouden voor backward compatibility
    localStorage.setItem(key, JSON.stringify(value));
    
    // Asynchroon opslaan in Supabase
    saveItemToSupabase(key, value).catch(error => {
      console.error(`Fout bij asynchroon opslaan in Supabase voor '${key}':`, error);
    });
    
    return true;
  } catch (error) {
    console.error(`Fout bij opslaan item '${key}' in localStorage:`, error);
    
    // Probeer alsnog in Supabase op te slaan
    saveItemToSupabase(key, value).catch(supabaseError => {
      console.error(`Ook opslaan in Supabase mislukt voor '${key}':`, supabaseError);
    });
    
    return false;
  }
}

/**
 * Laadt een item uit localStorage of Supabase.
 * @param key De sleutel van het item om te laden.
 * @param defaultValue De waarde die wordt teruggegeven als de sleutel niet bestaat of parsen mislukt.
 * @returns Het geparsede item of de defaultValue.
 */
export function loadItem<T>(key: string, defaultValue: T | null = null): T | null {
  try {
    const item = localStorage.getItem(key);
    if (item === null) {
      // Item niet in localStorage, probeer uit Supabase te laden
      // Dit gebeurt asynchroon, dus we kunnen niet direct het resultaat teruggeven
      loadItemFromSupabase<T>(key, defaultValue).then(supabaseItem => {
        if (supabaseItem !== null && supabaseItem !== defaultValue) {
          // Als gevonden in Supabase, update localStorage
          localStorage.setItem(key, JSON.stringify(supabaseItem));
        }
      }).catch(error => {
        console.error(`Fout bij asynchroon laden uit Supabase voor '${key}':`, error);
      });
      
      return defaultValue;
    }
    
    return JSON.parse(item) as T;
  } catch (error) {
    console.error(`Fout bij laden of parsen item '${key}' uit localStorage:`, error);
    
    // Probeer uit Supabase te laden
    loadItemFromSupabase<T>(key, defaultValue).then(supabaseItem => {
      if (supabaseItem !== null && supabaseItem !== defaultValue) {
        // Als gevonden in Supabase, update localStorage
        try {
          localStorage.setItem(key, JSON.stringify(supabaseItem));
        } catch (localError) {
          console.error(`Kan item niet in localStorage opslaan na laden uit Supabase:`, localError);
        }
      }
    }).catch(supabaseError => {
      console.error(`Ook laden uit Supabase mislukt voor '${key}':`, supabaseError);
    });
    
    return defaultValue;
  }
}

/**
 * Verwijdert een item uit localStorage en Supabase.
 * @param key De sleutel van het item om te verwijderen.
 * @returns true bij succes, false bij een fout.
 */
export function removeItem(key: string): boolean {
  try {
    localStorage.removeItem(key);
    
    // Asynchroon verwijderen uit Supabase
    removeItemFromSupabase(key).catch(error => {
      console.error(`Fout bij asynchroon verwijderen uit Supabase voor '${key}':`, error);
    });
    
    return true;
  } catch (error) {
    console.error(`Fout bij verwijderen item '${key}' uit localStorage:`, error);
    
    // Probeer alsnog uit Supabase te verwijderen
    removeItemFromSupabase(key).catch(supabaseError => {
      console.error(`Ook verwijderen uit Supabase mislukt voor '${key}':`, supabaseError);
    });
    
    return false;
  }
}

// Voorbeeld van een gestructureerde sleutel-aanpak (optioneel)
// export const STORAGE_KEYS = {
//   AUTH_USER: 'authUser', // Al gebruikt in AuthContext
//   PRODUCTION_REPORTS: (line: string, date: string) => `production_${line}_${date}`,
//   DISRUPTIONS: (line: string) => `disruptions_${line}`,
//   TARGETS: 'targets',
//   EQUIPMENT_OPTIONS: 'equipmentOptions',
//   MATERIAL_OPTIONS: 'materialOptions',
//   // ... andere sleutels
