-- Add a function to properly delete users
CREATE OR REPLACE FUNCTION public.delete_user(p_user_email text)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_user_id UUID;
  result JSON;
BEGIN
  -- Get the user ID from the email
  SELECT id INTO v_user_id FROM auth.users WHERE email = p_user_email;

  IF v_user_id IS NULL THEN
    RETURN json_build_object('error', 'User not found');
  END IF;

  -- Delete from authorized_managers
  DELETE FROM authorized_managers WHERE user_id = v_user_id;

  -- Delete from user_teams
  DELETE FROM user_teams WHERE user_id = v_user_id;

  -- Delete from user_profiles if it exists
  DELETE FROM user_profiles WHERE user_id = v_user_id;

  -- Delete from auth.identities
  DELETE FROM auth.identities WHERE user_id = v_user_id;

  -- Delete from auth.sessions
  DELETE FROM auth.sessions WHERE user_id = v_user_id;

  -- Delete from auth.mfa_factors
  DELETE FROM auth.mfa_factors WHERE user_id = v_user_id;

  -- Delete from auth.one_time_tokens
  DELETE FROM auth.one_time_tokens WHERE user_id = v_user_id;

  -- Finally, delete the user
  DELETE FROM auth.users WHERE id = v_user_id;

  -- Return success
  RETURN json_build_object('success', true, 'user_id', v_user_id, 'email', p_user_email);
EXCEPTION WHEN OTHERS THEN
  RETURN json_build_object('error', SQLERRM);
END;
$$;
