// src/components/logbook/LogbookNotification.tsx
import React, { useState, useEffect } from 'react';
import { useUserTeam } from '@/hooks/use-user-team';
import { Badge } from '@/components/ui/badge';
import { BookText } from 'lucide-react'; // Using BookText icon

const LogbookNotification: React.FC = () => {
  const { team } = useUserTeam();
  const [logbookCount, setLogbookCount] = useState(0);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch logbook count from Supabase
  const fetchLogbookCount = async () => {
    // If the user has no specific team assigned, don't show a count
    if (!team) {
      setIsLoading(false);
      setLogbookCount(0);
      return;
    }

    setIsLoading(true);
    try {
      // Temporarily set a fixed count until we have a proper logbook table
      // This avoids the 404 error while we wait for the proper table to be created
      setLogbookCount(0);
    } catch (error) {
      console.error('Unexpected error fetching logbook count:', error);
      setLogbookCount(0); // Default to 0 on unexpected error
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch count on mount and whenever the user's team changes
  useEffect(() => {
    fetchLogbookCount();
  }, [team]);

  // Don't render anything if loading or if the count is zero
  if (isLoading || logbookCount === 0) {
    return null;
  }

  // Render the indicator
  return (
    <div className="flex items-center text-sm text-gray-600 mr-4"> {/* Added margin-right */}
      <BookText className="h-4 w-4 mr-1" />
      <span className="font-medium hidden sm:inline">Logboek:</span> {/* Hide text on small screens */}
      <Badge variant="secondary" className="ml-1">{logbookCount}</Badge>
    </div>
  );
};

export default LogbookNotification;