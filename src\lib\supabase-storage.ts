/**
 * Middleware laag die localStorage aanroepen omleidt naar Supabase.
 * Deze laag zorgt ervoor dat de frontend code niet gewijzigd hoeft te worden.
 */
import { supabase } from './supabase-client';

// Tabel waarin we de localStorage data opslaan
const STORAGE_TABLE = 'local_storage_data';

/**
 * Slaat een item op in Supabase in plaats van localStorage.
 * @param key De sleutel waaronder het item wordt opgeslagen.
 * @param value De waarde die moet worden opgeslagen (wordt ge-JSON.stringify'd).
 * @returns true bij succes, false bij een fout.
 */
export async function saveItemToSupabase(key: string, value: any): Promise<boolean> {
  try {
    // Controleer of het item al bestaat (haal alleen ID op, max 1)
    const { data: existingItems, error: selectError } = await supabase
      .from(STORAGE_TABLE)
      .select('id') // Selecteer alleen 'id' om data overdracht te minimaliseren
      .eq('key', key)
      .limit(1); // Beperk tot 1 resultaat

    if (selectError) {
        console.error(`[supabase-storage] Error checking existence for key '${key}':`, selectError);
        throw selectError; // Gooi de fout opnieuw om de catch-block te triggeren
    }

    const itemExists = existingItems && existingItems.length > 0;

    if (itemExists) {
      // Update bestaand item
      const { error } = await supabase
        .from(STORAGE_TABLE)
        .update({ value: value, updated_at: new Date().toISOString() })
        .eq('key', key);

      if (error) throw error;
    } else {
      // Voeg nieuw item toe
      const { error } = await supabase
        .from(STORAGE_TABLE)
        .insert({ key, value, created_at: new Date().toISOString() });

      if (error) throw error;
    }

    // Bewaar ook in localStorage voor offline gebruik en snelle toegang
    // localStorage wordt nu bijgewerkt via de Realtime subscription
    // localStorage.setItem(key, JSON.stringify(value));
    return true;
  } catch (error) {
    console.error(`Fout bij opslaan item '${key}' in Supabase:`, error);
    // Fallback naar localStorage als Supabase niet beschikbaar is
    try {
      localStorage.setItem(key, JSON.stringify(value));
      return true;
    } catch (localError) {
      console.error(`Fallback naar localStorage mislukt voor '${key}':`, localError);
      return false;
    }
  }
}

/**
 * Laadt een item uit Supabase in plaats van localStorage.
 * @param key De sleutel van het item om te laden.
 * @param defaultValue De waarde die wordt teruggegeven als de sleutel niet bestaat of parsen mislukt.
 * @returns Het item of de defaultValue.
 */
export async function loadItemFromSupabase<T>(key: string, defaultValue: T | null = null): Promise<T | null> {
  // console.log(`[supabase-storage] Attempting to load key: ${key}`);
  try {
    // Probeer eerst uit localStorage te laden voor snelle toegang
    const localItem = localStorage.getItem(key);
    
    if (localItem !== null) {
      // console.log(`[supabase-storage] Loaded key '${key}' from localStorage cache.`);
      return JSON.parse(localItem) as T;
    }
    
    // Als niet in localStorage, haal uit Supabase
    // Gebruik maybeSingle() om 0 of 1 rij te accepteren, maar fout bij >1 rij
    const { data, error } = await supabase
      .from(STORAGE_TABLE)
      .select('value')
      .eq('key', key)
      .maybeSingle();

    // Handel fouten af (inclusief >1 rij gevonden door maybeSingle)
    if (error) {
      // console.error(`[supabase-storage] Error loading key '${key}' from Supabase with maybeSingle():`, error.message); // Keep commented out
      // Probeer fallback naar localStorage voordat defaultValue wordt geretourneerd
      // (Fallback logica zit al in de catch block hieronder)
      throw error; // Gooi de fout om de catch-block te triggeren voor fallback
    }

    // Als data null is (0 rijen gevonden door maybeSingle), retourneer defaultValue
    if (data === null) {
       // console.log(`[supabase-storage] Key '${key}' not found in Supabase (maybeSingle returned null).`);
       return defaultValue;
    }

    // Sla op in localStorage voor toekomstige snelle toegang
    // localStorage wordt nu bijgewerkt via de Realtime subscription (of initiele load)
    // localStorage.setItem(key, JSON.stringify(data.value));
    // console.log(`[supabase-storage] Loaded key '${key}' from Supabase and cached locally.`);
    return data.value as T;
  } catch (error) {
    console.error(`Fout bij laden item '${key}' uit Supabase:`, error); // Revert to original
    // Fallback naar localStorage
    try {
      const item = localStorage.getItem(key);
      if (item === null) {
        // console.log(`[supabase-storage] Key '${key}' not found in localStorage fallback.`);
        return defaultValue;
      }
      // console.warn(`[supabase-storage] Loaded key '${key}' from localStorage as fallback.`);
      return JSON.parse(item) as T;
    } catch (localError) {
      console.error(`Fallback naar localStorage mislukt voor '${key}':`, localError); // Revert to original
      return defaultValue;
    }
  }
}

/**
 * Verwijdert een item uit Supabase in plaats van localStorage.
 * @param key De sleutel van het item om te verwijderen.
 * @returns true bij succes, false bij een fout.
 */
export async function removeItemFromSupabase(key: string): Promise<boolean> {
  // console.log(`[supabase-storage] Attempting to remove key: ${key}`);
  try {
    const { error } = await supabase
      .from(STORAGE_TABLE)
      .delete()
      .eq('key', key);

    if (error) throw error;
    
    // localStorage wordt nu bijgewerkt via de Realtime subscription
    // localStorage.removeItem(key);
    // console.log(`[supabase-storage] Successfully removed key '${key}' from Supabase.`);
    return true;
  } catch (error) {
    console.error(`Fout bij verwijderen item '${key}' uit Supabase:`, error); // Revert to original
    
    // Fallback naar localStorage
    try {
      localStorage.removeItem(key);
      // console.warn(`[supabase-storage] Removed key '${key}' from localStorage as fallback.`);
      return true;
    } catch (localError) {
      console.error(`Fallback naar localStorage mislukt voor '${key}':`, localError); // Revert to original
      return false;
    }
  }
}
