import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { TooltipProvider, Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { CircleCheck, CircleX, FileText, Settings, History, Activity, Home, BarChart } from 'lucide-react';
import UserMenu from '@/components/auth/UserMenu';
import { cn } from '@/lib/utils';

const Navigation: React.FC = () => {
  const location = useLocation();

  return (
    <nav className="flex flex-wrap justify-between items-center py-4 px-6 bg-white border-b sticky top-0 z-10">
      <div className="flex items-center gap-4">
        <Link to="/" className="text-xl font-bold text-faerch-blue">
          Faerch Dashboard
        </Link>
      </div>
      
      <div className="hidden md:flex items-center gap-2">
        <NavigationMenuItem to="/" label="Home" icon={Home} />
        <NavigationMenuItem to="/report" label="Rapport" icon={FileText} />
        <NavigationMenuItem to="/disruptions" label="Storingen" icon={Activity} />
        <NavigationMenuItem to="/materials" label="Materialen" icon={BarChart} />
        <NavigationMenuItem to="/history" label="Geschiedenis" icon={History} />
        <NavigationMenuItem to="/settings" label="Instellingen" icon={Settings} />
      </div>

      <div className="flex items-center gap-4">
        <UserMenu />
      </div>
    </nav>
  );
};

interface NavigationMenuItemProps {
  to: string;
  label: string;
  icon: React.ElementType;
}

const NavigationMenuItem: React.FC<NavigationMenuItemProps> = ({ to, label, icon: Icon }) => {
  const location = useLocation();
  return (
    <Link
      to={to}
      className={cn(
        "group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50",
        location.pathname === to || (to !== '/' && location.pathname.startsWith(to)) ? 'bg-accent text-accent-foreground' : ''
      )}
    >
      <Icon className="mr-2 h-4 w-4" />
      {label}
    </Link>
  );
}

export default Navigation;