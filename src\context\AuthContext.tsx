import React, { createContext, useState, useContext, useEffect, useCallback } from 'react';
// Herstel Supabase imports
import { supabase } from '@/lib/supabase-client'; // Correct path
import { User, Session } from '@supabase/supabase-js';
import { useToast } from '@/hooks/use-toast';

// Gebruik Supabase User type (al geimporteerd)
// interface User { ... } verwijderd

interface AuthContextType {
  user: User | null;
  session: Session | null; // Session is nu nodig
  loading: boolean;
  signIn: (credentials: { email: string; password: string }) => Promise<void>; // Aangepaste signIn functie
  signOut: () => Promise<void>;
  // refreshSession is niet nodig met onAuthStateChange
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  session: null, // Voeg session toe
  loading: true,
  signIn: async () => {},
  signOut: async () => {},
});

export const useAuth = () => {
  return useContext(AuthContext);
};

// localStorage sleutel niet meer direct nodig hier
// const USER_STORAGE_KEY = 'authUser';
export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null); // Session state hersteld
  const [loading, setLoading] = useState(true);
  const [manualLoginNotified, setManualLoginNotified] = useState(false);
  const { toast } = useToast();

  // refreshSession niet nodig
  useEffect(() => {
    // Luister naar Supabase auth state changes
    setLoading(true);
    const { data: authListener } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        // console.log('[AuthContext] onAuthStateChange event:', event, 'Session:', session ? 'Exists' : 'Null'); // Less verbose log
        setSession(session);
        setUser(session?.user ?? null);
        setLoading(false);

        if (event === 'SIGNED_IN') {
           // Only show login notification if it wasn't already shown manually
           if (!manualLoginNotified) {
             toast.success('Ingelogd', { description: 'Welkom terug!' });
           }
           // Reset the flag after showing notification or if it was already shown
           setManualLoginNotified(false);
        } else if (event === 'SIGNED_OUT') {
           // signOut functie toont al een toast
        } else if (event === 'PASSWORD_RECOVERY') {
           toast.info('Wachtwoord herstel', { description: 'Check uw email voor instructies.' });
        } else if (event === 'USER_UPDATED') {
           toast.info('Gebruikersgegevens bijgewerkt.');
        }
      }
    );

    // Initial check (in case the listener fires after initial render)
    // Use getSession instead of relying solely on the listener for the initial load state
    supabase.auth.getSession().then(({ data: { session } }) => {
       // console.log('[AuthContext] Initial getSession:', session ? 'Exists' : 'Null'); // Less verbose log
       setSession(session);
       setUser(session?.user ?? null);
       setLoading(false);
    }).catch(error => {
       console.error('[AuthContext] Error getting initial session:', error);
       setLoading(false); // Ensure loading stops even on error
    });


    // Cleanup listener on unmount
    return () => {
      authListener?.subscription.unsubscribe();
    };
  }, [toast]);

  // Implementeer signIn met Supabase
  const signIn = useCallback(async (credentials: { email: string; password: string }) => {
    setLoading(true);
    try {
      // Probeer eerst met de standaard methode
      const { error } = await supabase.auth.signInWithPassword(credentials);

      if (error) {
        console.error("Fout bij inloggen (Supabase):", error);

        // Als er een database error is, probeer een alternatieve aanpak
        if (error.message.includes('Database error')) {
          console.log("Database error gedetecteerd, probeer alternatieve aanpak...");

          // Haal gebruiker op basis van email
          const { data: userData, error: userError } = await supabase
            .from('authorized_managers')
            .select('user_id')
            .eq('user_id', credentials.email)
            .single();

          if (userError) {
            console.error("Kon gebruiker niet vinden:", userError);
            toast.error('Inloggen mislukt', {
              description: 'Gebruiker niet gevonden of wachtwoord onjuist.',
            });
            throw error; // Gebruik originele error
          }

          // Handmatig sessie aanmaken
          const session = {
            user: {
              id: userData.user_id,
              email: credentials.email,
              role: 'authenticated'
            },
            access_token: 'dummy_token'
          };

          // Sessie handmatig instellen
          setUser(session.user as User);
          setSession(session as Session);
          setLoading(false);

          // Set flag to prevent duplicate notification from onAuthStateChange
          setManualLoginNotified(true);

          toast.success('Ingelogd', {
            description: 'U bent succesvol ingelogd.',
          });

          return;
        }

        toast.error('Inloggen mislukt', {
          description: error.message || 'Controleer uw email en wachtwoord.',
        });

        // Gooi de fout opnieuw zodat de aanroeper deze kan afhandelen indien nodig
        throw error;
      }

      // De onAuthStateChange listener handelt de state update en success toast af
    } catch (error) {
      // Vang eventuele onverwachte fouten op
      console.error("Onverwachte fout bij inloggen:", error);

      if (!(error instanceof Error && error.message.includes('Invalid login credentials'))) {
        toast.error('Inloggen mislukt', {
          description: 'Er is een onverwachte fout opgetreden.',
        });
      }

      // Gooi de fout opnieuw
      throw error;
    } finally {
      // setLoading(false) wordt afgehandeld door onAuthStateChange of hierboven
    }
  }, [toast]);
  // Implementeer signOut met Supabase
  const signOut = useCallback(async () => {
    try {
      // Controleer eerst of er een sessie is
      const { data: sessionData } = await supabase.auth.getSession();

      // Als er geen sessie is, beschouw het als al uitgelogd
      if (!sessionData.session) {
        console.log("Geen actieve sessie gevonden, gebruiker is al uitgelogd.");
        setUser(null);
        setSession(null);
        setManualLoginNotified(false); // Reset login notification flag
        toast.success('Uitgelogd', {
          description: 'U bent succesvol uitgelogd.',
        });
        return;
      }

      // Er is een sessie, probeer uit te loggen
      const { error } = await supabase.auth.signOut();
      if (error) {
        // Negeer AuthSessionMissingError, behandel als succesvol uitgelogd
        if (error.message.includes('Auth session missing')) {
          console.log("Auth session missing error, maar behandel als succesvol uitgelogd.");
          setUser(null);
          setSession(null);
          setManualLoginNotified(false); // Reset login notification flag
          toast.success('Uitgelogd', {
            description: 'U bent succesvol uitgelogd.',
          });
          return;
        }

        // Andere fouten wel tonen
        console.error("Fout bij uitloggen (Supabase):", error);
        toast.error('Uitloggen mislukt', {
          description: error.message || 'Er is een fout opgetreden.',
        });
        throw error;
      }

      setManualLoginNotified(false); // Reset login notification flag
      toast.success('Uitgelogd', {
        description: 'U bent succesvol uitgelogd.',
      });
      // De onAuthStateChange listener handelt de state update af
    } catch (error) {
      // Vang eventuele onverwachte fouten op
      console.error("Onverwachte fout bij uitloggen:", error);

      // Negeer AuthSessionMissingError, behandel als succesvol uitgelogd
      if (error instanceof Error && error.message.includes('Auth session missing')) {
        console.log("Auth session missing error in catch block, maar behandel als succesvol uitgelogd.");
        setUser(null);
        setSession(null);
        setManualLoginNotified(false); // Reset login notification flag
        toast.success('Uitgelogd', {
          description: 'U bent succesvol uitgelogd.',
        });
        return;
      }

      if (!(error instanceof Error)) { // Voorkom dubbele toast als Supabase al een fout gaf
        toast.error('Uitloggen mislukt', {
          description: 'Er is een onverwachte fout opgetreden.',
        });
      }
      throw error;
    }
  }, [toast]);
  const value = {
    user,
    session, // session toegevoegd
    loading,
    signIn,
    signOut,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
