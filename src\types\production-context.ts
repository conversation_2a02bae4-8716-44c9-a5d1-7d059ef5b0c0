import { Dispatch, SetStateAction } from 'react';
import {
  ProductionData,
  LineData,
  ProductionLine,
  ProductionRow,
  Disruption,
  DisruptionInput,
  Incident,
  HistoryEntry,
  EquipmentEntry,
  DowntimeReason,
  LogbookEntry,
  LogbookPriority,
  TdLogEntry, TdLogEntryInput,
  TdReporterName, // Add TdReporterName type
  ShiftColor
} from './index';

// Context type definition
export interface ProductionContextType {
  productionData: ProductionData;
  historyData: HistoryEntry[];
  incidents: Incident[];
  vkHistoryData: Incident[];
  disruptionHistory: Disruption[];
  materialOptions: Record<ProductionLine, string[]>;
  equipmentOptions: Record<ProductionLine, Record<string, EquipmentEntry[]>>;
  updateLineData: (line: ProductionLine, data: LineData) => void;
  resetData: () => void;
  addMaterialOption: (line: ProductionLine, material: string) => void;
  removeMaterialOption: (line: ProductionLine, material: string) => void;
  addProductionRow: (line: ProductionLine, date?: string, customRow?: Partial<ProductionRow>) => void;
  updateProductionRow: (line: ProductionLine, date: string, updatedRow: ProductionRow) => void;
  deleteProductionRow: (line: ProductionLine, date: string) => void;
  removeProductionRow: (line: ProductionLine, date: string) => void;
  addDisruption: (line: ProductionLine, disruption: DisruptionInput) => void;
  updateDisruption: (line: ProductionLine, id: string, updates: Partial<Disruption>) => void;
  deleteDisruption: (line: ProductionLine, id: string) => void;
  removeDisruption: (line: ProductionLine, id: string, isResolving?: boolean) => void;
  addIncident: (incident: Omit<Incident, 'id' | 'createdAt'>) => void;
  updateIncident: (updatedIncident: Incident) => void;
  removeIncident: (id: string, type?: 'safety' | 'quality', addToHistory?: boolean) => void;
  archiveAndClearIncidents: () => void;
  addToHistory: (entry: Omit<HistoryEntry, 'timestamp'>) => void;
  addMaterial: (line: ProductionLine, material: string) => void;
  deleteMaterial: (line: ProductionLine, material: string) => void;
  TARGETS: Record<ProductionLine, number>;
  YIELD_TARGETS: Record<ProductionLine, number>;
  updateTargets: (newTargets: Record<ProductionLine, number>) => void;
  updateYieldTargets: (newYieldTargets: Record<ProductionLine, number>) => void;
  setProductionData: Dispatch<SetStateAction<ProductionData>>;
  clearProductionData: (line: ProductionLine) => void;
  addEquipmentOption: (line: ProductionLine, areaCode: string, option: EquipmentEntry) => void;
  removeEquipmentOption: (line: ProductionLine, areaCode: string, optionId: string) => void;
  editEquipmentOption: (line: ProductionLine, areaCode: string, optionId: string, updatedOption: EquipmentEntry) => void;
  addEquipmentArea: (line: ProductionLine, areaCode: string, areaLabel: string) => void;
  removeEquipmentArea: (line: ProductionLine, areaCode: string) => void;
  editEquipmentArea: (line: ProductionLine, oldAreaCode: string, newAreaCode: string, newAreaLabel: string) => void;
  addDowntimeReason: (line: ProductionLine, reason: DowntimeReason) => void;
  updateDowntimeReason: (line: ProductionLine, reasonId: string, updatedReason: DowntimeReason) => void;
  removeDowntimeReason: (line: ProductionLine, reasonId: string) => void;
  isFlyLocked: boolean;
  setIsFlyLocked: (locked: boolean) => void;
  downtimeReasons: Record<ProductionLine, DowntimeReason[]>;
  setDowntimeReasons: (reasons: Record<ProductionLine, DowntimeReason[]>) => void;
  isLoading: boolean;
  isSyncing: boolean;
  lastSyncTime: string | null;
  independentDisruptions: Record<ProductionLine, (DisruptionInput | null)[]>;
  updateIndependentDisruption: (line: ProductionLine, index: number, disruptionData: DisruptionInput | null) => void;

  // Add Logbook state and functions (Now global arrays)
  logbookEntries: LogbookEntry[];
  logbookHistory: LogbookEntry[];
  addLogbookEntry: (entry: Omit<LogbookEntry, 'id' | 'timestamp'>) => void;
  removeLogbookEntry: (entryId: string) => void;
  updateLogbookEntry: (updatedEntry: LogbookEntry) => void;

  // Add TD Logbook state and functions
  tdLogEntries: TdLogEntry[];
  addTdLogEntry: (entryInput: TdLogEntryInput) => Promise<void>;
  updateTdLogEntry: (id: string, updates: Partial<TdLogEntryInput>) => Promise<void>;

  // Add TD Reporter Name state and functions
  tdReporterNames: TdReporterName[];
  addTdReporterName: (name: string) => Promise<boolean>; // Returns true on success
  deleteTdReporterName: (id: string) => Promise<void>;
}

// Default production data
export const defaultProductionData: ProductionData = {
  tl1: {
    rows: [],
    materials: ['Suezpmdx S', 'Prezpmdx S', 'Attewimi S', 'Eurokey S'],
    disruptions: [],
    equipmentOptions: {}
  },
  tl2: {
    rows: [],
    materials: ['Suezpmdx T', 'Prezpmdx T', 'Attewimi T', 'Eurokey T'],
    disruptions: [],
    equipmentOptions: {}
  },
  p1: {
    rows: [],
    materials: ['Suezpmdx S', 'Suezpmdx T', 'Eurokey T', 'Eurokey S'],
    disruptions: [],
    equipmentOptions: {}
  },
  p2: {
    rows: [],
    materials: ['Suezpmdx S', 'Suezpmdx T', 'Eurokey T', 'Eurokey S'],
    disruptions: [],
    equipmentOptions: {}
  },
  p3: {
    rows: [],
    materials: ['Suezpmdx S', 'Suezpmdx T', 'Eurokey T', 'Eurokey S'],
    disruptions: [],
    equipmentOptions: {}
  },
  Yard: { rows: [], materials: [], disruptions: [], equipmentOptions: {} },
  Koeling: { rows: [], materials: [], disruptions: [], equipmentOptions: {} },
  Gebouw: { rows: [], materials: [], disruptions: [], equipmentOptions: {} },
  Overige: { rows: [], materials: [], disruptions: [], equipmentOptions: {} }
};
