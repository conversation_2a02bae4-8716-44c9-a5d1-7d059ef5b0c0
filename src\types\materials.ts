import { ProductionLine } from './index';

export interface LineGroup {
  name: string;
  lines: ProductionLine[];
  id?: string; // Optional ID for groups
}

export interface MaterialState {
  newMaterial: string;
  selectedLine: ProductionLine;
  materialGroups: LineGroup[];
  editingGroup: LineGroup | null;
  showAddGroupForm: boolean;
  isAddMaterialDialogOpen?: boolean;
}

// Permission related types
export interface DatabasePermissions {
  role?: string;
  user?: any;
}

export interface DatabaseError {
  message: string;
  details?: string;
  hint?: string;
  code?: string;
  [key: string]: any; // Allow additional properties
}
