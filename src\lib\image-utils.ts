/**
 * Utility functions for image handling
 */

/**
 * Compresses an image file to reduce its size
 * @param file The image file to compress
 * @param maxWidth Maximum width of the compressed image
 * @param maxHeight Maximum height of the compressed image
 * @param quality Compression quality (0-1)
 * @returns A Promise that resolves to a compressed File object
 */
export const compressImage = async (
  file: File,
  maxWidth = 2500,
  maxHeight = 2500,
  quality = 0.95
): Promise<File> => {
  console.log(`Compressing image with settings: maxWidth=${maxWidth}, maxHeight=${maxHeight}, quality=${quality}`);
  return new Promise((resolve, reject) => {
    // If the file is not an image, return the original file
    if (!file.type.startsWith('image/')) {
      resolve(file);
      return;
    }

    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = (event) => {
      const img = new Image();
      img.src = event.target?.result as string;

      img.onload = () => {
        // Calculate new dimensions while maintaining aspect ratio
        let width = img.width;
        let height = img.height;

        if (width > maxWidth) {
          height = (height * maxWidth) / width;
          width = maxWidth;
        }

        if (height > maxHeight) {
          width = (width * maxHeight) / height;
          height = maxHeight;
        }

        // Create a canvas and draw the resized image
        const canvas = document.createElement('canvas');
        canvas.width = width;
        canvas.height = height;

        const ctx = canvas.getContext('2d');
        if (!ctx) {
          reject(new Error('Could not get canvas context'));
          return;
        }

        ctx.drawImage(img, 0, 0, width, height);

        console.log(`Original dimensions: ${img.width}x${img.height}`);
        console.log(`Resized dimensions: ${width}x${height}`);

        // Convert canvas to blob
        canvas.toBlob(
          (blob) => {
            if (!blob) {
              reject(new Error('Could not create blob'));
              return;
            }

            console.log(`Original file size: ${(file.size / 1024).toFixed(2)}KB`);
            console.log(`Compressed blob size: ${(blob.size / 1024).toFixed(2)}KB`);

            // Create a new File object from the blob
            const compressedFile = new File(
              [blob],
              file.name,
              {
                type: file.type,
                lastModified: Date.now()
              }
            );

            resolve(compressedFile);
          },
          file.type,
          quality
        );
      };

      img.onerror = () => {
        reject(new Error('Error loading image'));
      };
    };

    reader.onerror = () => {
      reject(new Error('Error reading file'));
    };
  });
};

/**
 * Creates a thumbnail from an image file
 * @param file The image file to create a thumbnail from
 * @param maxWidth Maximum width of the thumbnail
 * @param maxHeight Maximum height of the thumbnail
 * @returns A Promise that resolves to a thumbnail URL
 */
export const createThumbnail = async (
  file: File,
  maxWidth = 200,
  maxHeight = 200
): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = (event) => {
      const img = new Image();
      img.src = event.target?.result as string;

      img.onload = () => {
        // Calculate new dimensions while maintaining aspect ratio
        let width = img.width;
        let height = img.height;

        if (width > maxWidth) {
          height = (height * maxWidth) / width;
          width = maxWidth;
        }

        if (height > maxHeight) {
          width = (width * maxHeight) / height;
          height = maxHeight;
        }

        // Create a canvas and draw the resized image
        const canvas = document.createElement('canvas');
        canvas.width = width;
        canvas.height = height;

        const ctx = canvas.getContext('2d');
        if (!ctx) {
          reject(new Error('Could not get canvas context'));
          return;
        }

        ctx.drawImage(img, 0, 0, width, height);

        // Convert canvas to data URL
        const thumbnailUrl = canvas.toDataURL(file.type);
        resolve(thumbnailUrl);
      };

      img.onerror = () => {
        reject(new Error('Error loading image'));
      };
    };

    reader.onerror = () => {
      reject(new Error('Error reading file'));
    };
  });
};
