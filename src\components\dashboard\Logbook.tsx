import React, { useState, useEffect } from 'react'; // Added useEffect
import { LogbookEntry, LogbookPriority } from '@/types';
import { useProduction } from '@/context/ProductionContext';
import { useUserTeam } from '@/hooks/use-user-team';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { format } from 'date-fns';
import { nl } from 'date-fns/locale';
import { Badge } from '@/components/ui/badge';
// Removed AlertDialog imports as deletion is handled in LogbookItem
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogClose } from "@/components/ui/dialog"; // Added Dialog components
import { Trash2, History, PlusCircle, Pencil } from 'lucide-react'; // Added Pencil
import { toast } from 'sonner';
import { Link } from 'react-router-dom';
import { Combobox } from '@/components/ui/combobox';
import { Label } from '@/components/ui/label';
import LogbookItem from './LogbookItem'; // Import the new LogbookItem component

// Define predefined locations
const locations = [
  { value: 'tl1', label: 'TL1' },
  { value: 'tl2', label: 'TL2' },
  { value: 'p1', label: 'P1' },
  { value: 'p2', label: 'P2' },
  { value: 'p3', label: 'P3' },
  { value: 'koeling', label: 'Koeling' },
  { value: 'yard', label: 'Yard' },
  { value: 'overige', label: 'Overige' },
];

interface LogbookProps {
  // No props needed as it uses global context
}

const Logbook: React.FC<LogbookProps> = () => {
  // Use global logbook state and functions
  const { logbookEntries, addLogbookEntry, removeLogbookEntry, updateLogbookEntry } = useProduction(); // Added updateLogbookEntry
  const { team, isManager, hasTeamAccess, isLoading: isTeamLoading } = useUserTeam();

  const [newEntryText, setNewEntryText] = useState('');
  const [newEntryPriority, setNewEntryPriority] = useState<LogbookPriority>('Middel');
  const [newEntryLocation, setNewEntryLocation] = useState('');
  const [newEntryTeam, setNewEntryTeam] = useState<string>('');
  const [isAdding, setIsAdding] = useState(false);
  // Removed entryToDelete state as it's handled in LogbookItem

  // State for editing
  const [isEditing, setIsEditing] = useState(false);
  const [entryToEdit, setEntryToEdit] = useState<LogbookEntry | null>(null);
  const [editText, setEditText] = useState('');
  const [editPriority, setEditPriority] = useState<LogbookPriority>('Middel');
  const [editLocation, setEditLocation] = useState('');
  const [editTeam, setEditTeam] = useState<string>('');

  // Beschikbare ploegen
  const teams = ['Blauw', 'Wit', 'Geel', 'Groen', 'Rood'];

  // Effect om de huidige ploeg in te stellen bij het openen van het formulier
  useEffect(() => {
    if (team && !isTeamLoading) {
      setNewEntryTeam(team);
    } else if (!isTeamLoading && isManager) {
      // Als de gebruiker een beheerder is maar geen team heeft, stel dan het eerste team in
      setNewEntryTeam(teams[0]);
    }
  }, [team, isTeamLoading, isManager, teams]);

  // Show latest 5 global entries (or adjust as needed)
  const entriesToShow = logbookEntries.slice(0, 5);

  const handleAddEntry = () => {
    if (!newEntryText.trim()) {
      toast.warning("Logboek item mag niet leeg zijn.");
      return;
    }
    if (!newEntryLocation.trim()) {
        toast.warning("Locatie mag niet leeg zijn.");
        return;
    }

    // Automatically use the user's team or the first team for managers
    let teamToUse = team;
    if (!teamToUse && isManager && teams.length > 0) {
      teamToUse = teams[0];
    } else if (!teamToUse) {
      toast.warning("Geen ploeg beschikbaar. Neem contact op met een beheerder.");
      return;
    }

    // Controleer of de gebruiker toegang heeft tot de geselecteerde ploeg
    if (!hasTeamAccess(teamToUse)) {
      toast.error(`U heeft geen rechten om items toe te voegen voor ploeg ${teamToUse}.`);
      return;
    }

    addLogbookEntry({
      text: newEntryText,
      priority: newEntryPriority,
      location: newEntryLocation,
      team: teamToUse
    });

    setNewEntryText('');
    setNewEntryPriority('Middel');
    setNewEntryLocation('');
    setIsAdding(false);
  };

  // --- Edit Logic ---
  const openEditDialog = (entry: LogbookEntry) => {
    // Controleer of de gebruiker toegang heeft tot de ploeg van dit item
    if (entry.team && !hasTeamAccess(entry.team)) {
      toast.error(`U heeft geen rechten om items van ploeg ${entry.team} te bewerken.`);
      return;
    }

    setEntryToEdit(entry);
    setEditText(entry.text);
    setEditPriority(entry.priority);
    setEditLocation(entry.location);
    setEditTeam(entry.team || '');
    setIsEditing(true);
  };

  const closeEditDialog = () => {
    setIsEditing(false);
    setEntryToEdit(null);
    // Reset edit form fields
    setEditText('');
    setEditPriority('Middel');
    setEditLocation('');
    setEditTeam('');
  };

  const handleSaveEdit = () => {
    if (!entryToEdit) return;
    if (!editText.trim()) {
      toast.warning("Logboek item mag niet leeg zijn.");
      return;
    }
     if (!editLocation.trim()) {
        toast.warning("Locatie mag niet leeg zijn.");
        return;
    }

    // Keep the original team from the entry
    const teamToUse = entryToEdit.team;

    // Controleer of de gebruiker toegang heeft tot de ploeg
    if (teamToUse && !hasTeamAccess(teamToUse)) {
      toast.error(`U heeft geen rechten om items voor ploeg ${teamToUse} te bewerken.`);
      return;
    }

    const updatedEntry: LogbookEntry = {
      ...entryToEdit,
      text: editText,
      priority: editPriority,
      location: editLocation,
      team: teamToUse,
      // Keep original id and timestamp
    };

    updateLogbookEntry(updatedEntry);
    closeEditDialog();
  };
  // --- End Edit Logic ---


  const getPriorityBadgeVariant = (priority: LogbookPriority): "default" | "secondary" | "destructive" | "outline" | null | undefined => {
    switch (priority) {
      case 'Hoog': return 'destructive';
      case 'Middel': return 'secondary';
      case 'Laag': return 'outline';
      default: return 'default';
    }
  };

  // Removed openDeleteDialog, closeDeleteDialog, and confirmDelete functions
  // Deletion confirmation is now handled within LogbookItem

  // Function to be passed to LogbookItem for final deletion
  const handleDeleteConfirm = (entry: LogbookEntry) => {
    // Controleer of de gebruiker toegang heeft tot de ploeg van dit item
    if (entry.team && !hasTeamAccess(entry.team)) {
      toast.error(`U heeft geen rechten om items van ploeg ${entry.team} te verwijderen.`);
      return;
    }

    if (removeLogbookEntry) {
      removeLogbookEntry(entry.id);
      toast.success("Logboek item verplaatst naar historie.");
    } else {
      console.error("removeLogbookEntry function not available in context.");
      toast.error("Kon item niet verwijderen (functie ontbreekt).");
    }
  };

  return (
    <React.Fragment>
      <div className="dashboard-card p-4">
        {/* Title and Buttons Row */}
        <div className="flex justify-between items-center mb-3 border-b pb-2">
          <h2 className="text-lg font-semibold">Logboek</h2>
          <div className="flex items-center gap-2">
             {/* Link to Logbook History Page */}
             <Button variant="outline" size="sm" className="flex items-center gap-1" asChild>
               <Link to="/logboek-historie">
                 <History className="w-4 h-4" />
                 <span>History</span>
               </Link>
             </Button>
             {!isAdding && (
               <Button variant="outline" size="sm" onClick={() => setIsAdding(true)} className="flex items-center gap-1">
                  <PlusCircle className="w-4 h-4" />
                  <span>Nieuw Item</span>
               </Button>
             )}
          </div>
        </div>

        {/* Add New Entry Form (conditionally rendered) */}
        {isAdding && (
          <div className="space-y-2 mb-4 border-b pb-4">
            <Textarea
              placeholder="Nieuw logboek item..."
              value={newEntryText}
              onChange={(e) => setNewEntryText(e.target.value)}
              rows={2}
              className="text-sm"
            />
             <div className="flex flex-col sm:flex-row justify-between items-stretch sm:items-center gap-2">
               {/* Location Combobox */}
               <div className="w-full sm:w-[180px]">
                 <Combobox
                   options={locations}
                   value={newEntryLocation}
                   onValueChange={setNewEntryLocation}
                   placeholder="Selecteer/typ locatie..."
                   searchPlaceholder="Zoek/typ locatie..."
                   notFoundMessage="Geen locatie gevonden."
                 />
               </div>

               {/* Team is automatically set based on the user's team */}

               {/* Priority Select */}
               <Select value={newEntryPriority} onValueChange={(value: LogbookPriority) => setNewEntryPriority(value)}>
                  <SelectTrigger className="w-full sm:w-[120px] h-8 text-xs">
                      <SelectValue placeholder="Prioriteit" />
                  </SelectTrigger>
                  <SelectContent>
                      <SelectItem value="Laag">Laag</SelectItem>
                      <SelectItem value="Middel">Middel</SelectItem>
                      <SelectItem value="Hoog">Hoog</SelectItem>
                  </SelectContent>
               </Select>
               {/* Action Buttons */}
               <div className="flex gap-2 justify-end sm:justify-start"> {/* Align buttons */}
                   <Button variant="outline" size="sm" onClick={() => setIsAdding(false)}>Annuleren</Button>
                   <Button size="sm" onClick={handleAddEntry}>Toevoegen</Button>
               </div>
            </div>
          </div>
        )}

        {/* Log Entries List */}
        <div className="space-y-3 max-h-60 overflow-y-auto pr-2">
          {entriesToShow.length === 0 && !isAdding && (
            <p className="text-sm text-gray-500 italic">Nog geen logboek items.</p>
          )}
          {entriesToShow.map((entry) => (
            <LogbookItem
              key={entry.id}
              entry={entry}
              onEdit={openEditDialog} // Pass edit handler
              onDeleteConfirm={handleDeleteConfirm} // Pass delete confirmation handler
            />
          ))}
        </div>

        {/* Removed old AlertDialogContent */}
      </div>

      {/* Edit Dialog */}
      <Dialog open={isEditing} onOpenChange={closeEditDialog}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Logboek Item Bewerken</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="edit-logbook-text">Tekst</Label>
              <Textarea
                id="edit-logbook-text"
                value={editText}
                onChange={(e) => setEditText(e.target.value)}
                rows={3}
                className="text-sm"
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
               <div className="space-y-2">
                 <Label htmlFor="edit-logbook-location">Locatie</Label>
                 <Combobox
                   id="edit-logbook-location"
                   options={locations}
                   value={editLocation}
                   onValueChange={setEditLocation}
                   placeholder="Selecteer/typ locatie..."
                   searchPlaceholder="Zoek/typ locatie..."
                   notFoundMessage="Geen locatie gevonden."
                 />
               </div>

               {/* Team is kept from the original entry */}

               <div className="space-y-2">
                 <Label htmlFor="edit-logbook-priority">Prioriteit</Label>
                 <Select value={editPriority} onValueChange={(value: LogbookPriority) => setEditPriority(value)}>
                    <SelectTrigger id="edit-logbook-priority" className="w-full h-10 text-sm"> {/* Adjusted height/text size */}
                        <SelectValue placeholder="Prioriteit" />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="Laag">Laag</SelectItem>
                        <SelectItem value="Middel">Middel</SelectItem>
                        <SelectItem value="Hoog">Hoog</SelectItem>
                    </SelectContent>
                 </Select>
               </div>
            </div>
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button type="button" variant="outline">Annuleren</Button>
            </DialogClose>
            <Button type="button" onClick={handleSaveEdit}>Opslaan</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </React.Fragment>
  ); // Place the closing parenthesis correctly before the semicolon
};

export default Logbook;