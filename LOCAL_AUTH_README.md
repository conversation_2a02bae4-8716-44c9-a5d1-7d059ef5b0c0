# Local Authentication System - TEMPORARY DEVELOPMENT WORKAROUND

This document explains the temporary local authentication system implemented as a workaround for Supabase database issues.

## ⚠️ WARNING: NOT FOR PRODUCTION USE ⚠️

This local authentication system is **ONLY** intended for development and testing purposes. It should **NEVER** be used in a production environment for the following reasons:

1. **Security Risk**: User credentials are stored in plain text in the source code
2. **No Proper Authentication**: No password hashing or secure authentication mechanisms
3. **No Scalability**: Adding users requires code changes
4. **No Team Permissions**: Team-based permissions for checkboxes in the Logbook won't work properly
5. **No Audit Trail**: No logging of authentication events

## Current Implementation

The current implementation includes:

1. A local JSON file (`src/data/users.json`) with hardcoded user credentials
2. A local authentication context (`src/context/LocalAuthContext.tsx`) that manages authentication state
3. A local protected route component (`src/components/auth/LocalProtectedRoute.tsx`) that protects routes
4. A local login page (`src/pages/LocalAuthPage.tsx`) for user login
5. A local user menu component (`src/components/auth/LocalUserMenu.tsx`) for user profile and logout
6. A local layout component (`src/components/layout/LocalLayout.tsx`) that uses the local authentication

## Known Limitations

1. **Team Permissions Don't Work**: Users assigned to specific teams (e.g., "Blauw") won't have proper permissions to check boxes in the Logbook for their team.
2. **No Real Admin Rights**: Admin users don't have proper admin rights in the database.
3. **No Integration with Supabase**: This system bypasses Supabase entirely, so any features that rely on Supabase authentication won't work.

## How to Fix the Supabase Issues

To properly fix the Supabase authentication issues, follow these steps:

1. **Contact Supabase Support**: Provide them with the project reference ID (`dbsztlsxgbheifrpmsaa`) and the error details (`AuthApiError: Database error querying schema`).

2. **Check Database Schema**: Ask Supabase support to check for issues with the `auth` schema in your database.

3. **Verify Migrations**: Ensure all migrations have been properly applied to the database.

4. **Check RLS Policies**: Verify that Row Level Security (RLS) policies are correctly configured for the `auth.users` table.

5. **Restore from Backup**: If necessary, ask Supabase support to restore the database from a backup before the issues occurred.

## Reverting to Supabase Authentication

Once the Supabase issues are resolved, follow these steps to revert to Supabase authentication:

1. Update `App.tsx` to use the original `Layout` component instead of `LocalLayout`
2. Update all routes to use the original `ProtectedRoute` component instead of `LocalProtectedRoute`
3. Remove the `LocalAuthProvider` wrapper and keep only the `AuthProvider`
4. Set the default auth route back to the original auth page or the simple auth page

## Test Accounts

For development and testing purposes only, the following test accounts are available:

1. **Admin Account**
   - Email: <EMAIL>
   - Password: admin123
   - Role: Manager (access to all areas)

2. **Regular User Account**
   - Email: <EMAIL>
   - Password: password123
   - Team: Blauw

**IMPORTANT**: These credentials should be changed before deploying to production.
