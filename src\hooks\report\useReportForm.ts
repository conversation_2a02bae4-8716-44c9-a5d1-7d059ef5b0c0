import { useState, useEffect, useCallback, useRef } from 'react';
import { ProductionLine, ProductionRow, EquipmentEntry, ProductionShiftData } from '@/types';
import { useProduction } from '@/context/ProductionContext';
import { loadItem, saveItem, removeItem } from '@/lib/local-storage';
import { format, addDays, parse } from 'date-fns';
import { toast } from 'sonner';

// Helper functions
export const createInitialShiftData = (): ProductionShiftData => ({
  production: '',
  yield: '',
  material: '',
  target: 0
});

export const formatForDisplay = (value: string | number): string => {
  if (typeof value === 'number') {
    return value.toString().replace('.', ',');
  }
  return value.toString().replace('.', ',');
};

export const parseAndValidateNumber = (value: string | number, field: string): number => {
  if (typeof value === 'number') {
    return value;
  }
  const normalizedValue = value.toString().replace(',', '.');
  const parsedValue = parseFloat(normalizedValue);
  if (isNaN(parsedValue)) {
    console.warn(`Invalid ${field} value: ${value}`);
    return 0;
  }
  return parsedValue;
};

export const useReportForm = () => {
  const { productionData, updateProductionRow, addProductionRow, equipmentOptions, TARGETS, removeProductionRow } = useProduction();

  const [selectedLine, setSelectedLine] = useState<ProductionLine>('tl1');
  const [activeRowIndex, setActiveRowIndex] = useState<number>(0);
  const [activeRow, setActiveRow] = useState<ProductionRow | null>(null);
  const [popoverOpen, setPopoverOpen] = useState<Record<number, boolean>>({});
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [filteredEquipment, setFilteredEquipment] = useState<Record<string, EquipmentEntry[]>>({});
  const [dateDialogOpen, setDateDialogOpen] = useState<boolean>(false);
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date());
  const [dayToDelete, setDayToDelete] = useState<{ line: ProductionLine; date: string } | null>(null);
  const [showDeleteDayDialog, setShowDeleteDayDialog] = useState<boolean>(false);
  const [isShiftInputOpen, setIsShiftInputOpen] = useState(false);
  const [pendingRowDate, setPendingRowDate] = useState<Date | null>(null); // Still needed for edit/add next day
  const [editingRowData, setEditingRowData] = useState<{ od: ProductionShiftData; md: ProductionShiftData; nd: ProductionShiftData } | null>(null);
  const [lastFieldChangeTime, setLastFieldChangeTime] = useState<number | null>(null);
  const autoSaveTimerRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  const lineData = productionData[selectedLine];

  // Calculate total production for the active day (if needed)
  const calculateTotalProduction = useCallback(() => {
      if (!activeRow) return 0;
      return (
        parseAndValidateNumber(activeRow.od.production, 'OD Production') +
        parseAndValidateNumber(activeRow.md.production, 'MD Production') +
        parseAndValidateNumber(activeRow.nd.production, 'ND Production')
      );
  }, [activeRow]);


  useEffect(() => {
    if (lineData?.rows?.length > 0) {
      const validIndex = Math.min(activeRowIndex, lineData.rows.length - 1);
      if (validIndex !== activeRowIndex) {
          setActiveRowIndex(validIndex);
      }
      setActiveRow(lineData.rows[validIndex]);
    } else {
      setActiveRow(null);
      setActiveRowIndex(0);
    }
  }, [lineData?.rows, activeRowIndex, selectedLine]);

  const getStorageKey = (line: ProductionLine): string => `production_${line}`;

  // Autosave timer effect
  useEffect(() => {
    if (lastFieldChangeTime === null || !activeRow) return;
    if (autoSaveTimerRef.current) { clearTimeout(autoSaveTimerRef.current); }
    autoSaveTimerRef.current = setTimeout(() => {
      if (activeRow) {
        const storageKey = getStorageKey(selectedLine);
        const currentLineData = loadItem<ProductionRow[]>(storageKey, []) || [];
        const rowIndex = currentLineData.findIndex(row => row.date === activeRow.date);
        let updatedLineData: ProductionRow[];
        if (rowIndex > -1) { updatedLineData = [...currentLineData.slice(0, rowIndex), activeRow, ...currentLineData.slice(rowIndex + 1)]; }
        else { updatedLineData = [...currentLineData, activeRow]; updatedLineData.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()); }
        const success = saveItem(storageKey, updatedLineData);
        if (success) { console.log(`Autosave uitgevoerd op ${new Date().toLocaleTimeString()}`); }
      }
    }, 30000);
    return () => { if (autoSaveTimerRef.current) { clearTimeout(autoSaveTimerRef.current); } };
  }, [lastFieldChangeTime, activeRow, selectedLine]);

  // BeforeUnload handler
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (lastFieldChangeTime !== null && activeRow) {
        const timeSinceLastChange = Date.now() - lastFieldChangeTime;
        if (timeSinceLastChange < 30000) {
          const storageKey = getStorageKey(selectedLine);
          const currentLineData = loadItem<ProductionRow[]>(storageKey, []) || [];
          const rowIndex = currentLineData.findIndex(row => row.date === activeRow.date);
          let updatedLineData: ProductionRow[];
          if (rowIndex > -1) { updatedLineData = [...currentLineData.slice(0, rowIndex), activeRow, ...currentLineData.slice(rowIndex + 1)]; }
          else { updatedLineData = [...currentLineData, activeRow]; updatedLineData.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()); }
          saveItem(storageKey, updatedLineData);
          const message = "Er zijn onopgeslagen wijzigingen. Weet u zeker dat u de pagina wilt verlaten?";
          e.returnValue = message; return message;
        }
      }
    };
    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [lastFieldChangeTime, activeRow, selectedLine]);

  // Filter equipment options
  useEffect(() => {
    if (!searchQuery) { setFilteredEquipment(equipmentOptions[selectedLine] || {}); return; }
    const lowercaseQuery = searchQuery.toLowerCase(); const filtered: Record<string, EquipmentEntry[]> = {};
    Object.entries(equipmentOptions[selectedLine] || {}).forEach(([area, options]) => {
      const matchingOptions = options.filter(option => option.label_nl.toLowerCase().includes(lowercaseQuery) || option.value.toLowerCase().includes(lowercaseQuery));
      if (matchingOptions.length > 0) { filtered[area] = matchingOptions; }
    });
    setFilteredEquipment(filtered);
  }, [searchQuery, equipmentOptions, selectedLine]);

  const handleLineChange = useCallback((line: ProductionLine) => { setSelectedLine(line); setActiveRowIndex(0); setSearchQuery(''); }, []);
  const handleRowChange = useCallback((index: number) => { setActiveRowIndex(index); }, []);

  const handleShiftChange = useCallback((shift: 'od' | 'md' | 'nd', field: keyof ProductionShiftData, value: string | number | boolean, cursorPosition?: number) => {
    if (!activeRow) return;

    let valueToStore: string | number | boolean = value;
    let newCursorPosition = cursorPosition;

    // --- Validation specifically for Yield and Production ---
    if ((field === 'yield' || field === 'production') && typeof value === 'string') {
        const originalValue = value;

        // 1. Clean the input string (allow digits, max one comma)
        let cleanedValue = value.replace(/[^0-9,]/g, '');

        // If the value was cleaned (characters removed), adjust cursor position
        if (cleanedValue !== originalValue && cursorPosition !== undefined) {
            // Calculate how many characters were removed before the cursor
            let removedBeforeCursor = 0;
            for (let i = 0; i < cursorPosition; i++) {
                if (i >= originalValue.length) break;
                if (!/[0-9,]/.test(originalValue[i])) {
                    removedBeforeCursor++;
                }
            }
            // Adjust cursor position
            newCursorPosition = Math.max(0, cursorPosition - removedBeforeCursor);
        }

        // Handle commas
        const firstCommaIndex = cleanedValue.indexOf(',');
        if (firstCommaIndex !== -1) {
          // If there are multiple commas, remove extras
          if (cleanedValue.lastIndexOf(',') !== firstCommaIndex) {
            const beforeCursor = cursorPosition !== undefined ? cleanedValue.substring(0, cursorPosition) : '';
            const commasBeforeCursor = (beforeCursor.match(/,/g) || []).length;

            cleanedValue = cleanedValue.substring(0, firstCommaIndex + 1) +
                          cleanedValue.substring(firstCommaIndex + 1).replace(/,/g, '');

            // If commas were removed before cursor, adjust position
            if (cursorPosition !== undefined && commasBeforeCursor > 1) {
              const commasRemoved = commasBeforeCursor - 1;
              newCursorPosition = Math.max(0, newCursorPosition! - commasRemoved);
            }
          }
        }

        // Prevent comma as the first character if it's the only character
        if (cleanedValue === ',') {
            cleanedValue = '0,';
            // Adjust cursor position if needed
            if (cursorPosition !== undefined && cursorPosition === 1) {
                newCursorPosition = 2; // After the '0,'
            }
        }

        // 2. Validate range (only for yield) and update cleanedValue if needed
        if (field === 'yield') {
            // Use parseFloat with comma replaced by dot for validation check
            const numericValue = parseFloat(cleanedValue.replace(',', '.'));
            if (!isNaN(numericValue)) {
                if (numericValue > 100) {
                    cleanedValue = '100'; // Cap at 100 string
                    // Reset cursor position to end if value was capped
                    if (cursorPosition !== undefined) {
                        newCursorPosition = 3; // End of '100'
                    }
                } else if (numericValue < 0) {
                    // Optional: Prevent negative numbers if desired
                     cleanedValue = '0'; // Prevent negative string
                     // Reset cursor position to end if value was changed
                     if (cursorPosition !== undefined) {
                         newCursorPosition = 1; // End of '0'
                     }
                }
            } else if (cleanedValue !== '' && cleanedValue !== '0,') {
                 // If cleaning results in something non-numeric (e.g. multiple commas somehow missed)
                 // Default to empty string or previous valid state? Let's default to empty for now.
                 // This case should be rare due to the regex cleaning above.
                 cleanedValue = '';
                 // Reset cursor position
                 if (cursorPosition !== undefined) {
                     newCursorPosition = 0;
                 }
            }
        }
        // Store the cleaned string directly
        valueToStore = cleanedValue;

    } else if (field === 'target' && typeof value !== 'boolean') {
        // For target, parse immediately (assuming target doesn't need comma input)
        valueToStore = parseAndValidateNumber(value, field);
    }
    // --- End Validation ---

    // Update the state with the cleaned string for yield/prod, or parsed/boolean value for others
    const updatedRow = {
      ...activeRow,
      [shift]: { ...activeRow[shift], [field]: valueToStore }
    };
    setActiveRow(updatedRow);
    setLastFieldChangeTime(Date.now()); // Keep tracking changes for autosave/debounce

    // Return the new cursor position if it was provided and modified
    return newCursorPosition;
  }, [activeRow, selectedLine]);

  // Debounced effect for saving activeRow changes to context
  useEffect(() => {
    if (!activeRow || lastFieldChangeTime === null) return; // Only run if activeRow exists and changed

    const handler = setTimeout(() => {
      console.log(`[useReportForm Debug] Debounced save for row date: ${activeRow.date}`);
      // Check if the row in context is actually different before updating
      const currentRowInContext = productionData[selectedLine]?.rows.find(r => r.date === activeRow.date);
      if (JSON.stringify(currentRowInContext) !== JSON.stringify(activeRow)) {
        updateProductionRow(selectedLine, activeRow.date, activeRow);
        // Optionally reset lastFieldChangeTime here if you only want to save once per burst of changes
        // setLastFieldChangeTime(null);
      } else {
        console.log(`[useReportForm Debug] Debounced save skipped, no change detected for ${activeRow.date}`);
      }
    }, 1000); // 1 second debounce delay

    return () => {
      clearTimeout(handler);
    };
  }, [activeRow, lastFieldChangeTime, selectedLine, updateProductionRow, productionData]); // Add productionData to dependencies for comparison

  const handlePopoverOpenChange = useCallback((index: number, isOpen: boolean) => { setPopoverOpen(prev => ({ ...prev, [index]: isOpen })); }, []);
  const handleSearchQueryChange = useCallback((query: string) => { setSearchQuery(query); }, []);

  // Updated handleAddDay
  const handleAddDay = useCallback(() => {
    console.log('[handleAddDay] Triggered for line:', selectedLine);
    const lineRows = productionData[selectedLine]?.rows || [];
    if (lineRows.length === 0) {
      console.log('[handleAddDay] No existing rows, opening date picker.');
      setSelectedDate(new Date()); // Set default date for picker
      setDateDialogOpen(true); // Open date picker dialog
      setIsShiftInputOpen(false); // Ensure shift input is closed
      setPendingRowDate(null);
    } else {
      // Calculate next day based on the latest entry
      let nextDate = new Date();
      const latestDateStr = lineRows[0].date; // Assuming rows are sorted descending
      console.log('[handleAddDay] Latest date string:', latestDateStr);
      try {
        const latestDate = parse(latestDateStr, 'yyyy-MM-dd', new Date());
        nextDate = addDays(latestDate, 1);
      } catch (e) {
        console.error("Kon laatste datum niet parsen:", latestDateStr, e);
        toast.error("Fout bij bepalen volgende datum.");
        return;
      }
      const today = new Date(); today.setHours(0, 0, 0, 0);
      const nextDayCheck = new Date(nextDate); nextDayCheck.setHours(0, 0, 0, 0);
      console.log('[handleAddDay] Calculated next date:', nextDate);
      if (nextDayCheck > today) {
        toast.info("De volgende dag ligt in de toekomst.", { description: "Je kunt pas gegevens invoeren op de dag zelf." });
        return;
      }
      const formattedNextDate = format(nextDate, 'yyyy-MM-dd');
      const exists = lineRows.some(row => row.date === formattedNextDate);
      if (exists) {
        toast.error("De berekende volgende dag bestaat al.", { description: `Probeer dag ${formattedNextDate} te bewerken.` });
        return;
      }
      // Directly add the next day with empty data, don't open shift input dialog
      console.log('[handleAddDay] Adding next day directly:', formattedNextDate);
      try {
        addProductionRow(selectedLine, formattedNextDate); // Add row with default empty shifts
        // Find the index of the newly added row (should be 0 as it's sorted)
        // Need to wait for state update or find index differently
        // setActiveRowIndex(0); // Go to the newly added day
        toast.success(`Dag ${formattedNextDate} toegevoegd.`);
      } catch (error: any) {
        console.error(`Error adding day:`, error);
        toast.error(`Fout bij toevoegen dag`, { description: error.message || 'Er is een fout opgetreden.' });
      }
      setDateDialogOpen(false); // Ensure date dialog is closed
      setIsShiftInputOpen(false); // Ensure shift input is closed
    }
  }, [productionData, selectedLine, addProductionRow]); // Added addProductionRow dependency

  // Updated handleDateSelect
  const handleDateSelect = useCallback((date: Date | undefined) => {
      console.log('[handleDateSelect] Date selected:', date);
      setDateDialogOpen(false); // Close date picker immediately
      if (!date) { return; }

      const today = new Date(); today.setHours(0, 0, 0, 0);
      const selectedDay = new Date(date); selectedDay.setHours(0, 0, 0, 0);

      if (selectedDay > today) {
        toast.error("Datum kan niet in de toekomst liggen.");
        return;
      }

      const formattedDate = format(date, 'yyyy-MM-dd');
      const exists = (productionData[selectedLine]?.rows || []).some(row => row.date === formattedDate);

      if (exists) {
        toast.error("Deze datum bestaat al voor deze lijn.");
        return;
      }

      // Directly add the selected day with empty data
      console.log('[handleDateSelect] Adding selected day directly:', formattedDate);
      try {
        addProductionRow(selectedLine, formattedDate); // Add row with default empty shifts
        // setActiveRowIndex(0); // Go to the newly added day (index might not be 0 if list wasn't empty)
        toast.success(`Dag ${formattedDate} toegevoegd.`);
      } catch (error: any) {
        console.error(`Error adding day:`, error);
        toast.error(`Fout bij toevoegen dag`, { description: error.message || 'Er is een fout opgetreden.' });
      }
      // No longer opening ShiftInputDialog here
      // setPendingRowDate(date);
      // setEditingRowData(null);
      // setIsShiftInputOpen(true);
  }, [productionData, selectedLine, addProductionRow]); // Added addProductionRow dependency

  const handleEditDay = useCallback((index: number) => {
    const lineRows = productionData[selectedLine]?.rows || []; const rowToEdit = lineRows[index];
    if (rowToEdit) {
      try {
        const dateToEdit = parse(rowToEdit.date, 'yyyy-MM-dd', new Date());
        setPendingRowDate(dateToEdit); // Set pending date for context
        setEditingRowData({ od: rowToEdit.od, md: rowToEdit.md, nd: rowToEdit.nd }); // Set initial data for dialog
        setIsShiftInputOpen(true); // Open the ShiftInputDialog for editing
      } catch (e) { console.error("Kon datum niet parsen:", rowToEdit.date, e); toast.error("Fout bij openen bewerkingscherm."); }
    } else { console.error(`Rij ${index} niet gevonden.`); toast.error("Kon dag niet vinden."); }
  }, [productionData, selectedLine]);

  // Updated handleShiftInputSubmit (Only used for EDITING now)
  const handleShiftInputSubmit = useCallback(async (shiftInputData: { od: ProductionShiftData; md: ProductionShiftData; nd: ProductionShiftData }) => {
    console.log('[handleShiftInputSubmit] Submitting for date:', pendingRowDate, 'Editing:', !!editingRowData);
    // This function is now only called when editing (isEditing should be true)
    if (!pendingRowDate || !editingRowData) {
        toast.error("Fout: Bewerkingsdata ontbreekt.");
        setIsShiftInputOpen(false);
        return;
    }
    const formattedDate = format(pendingRowDate, 'yyyy-MM-dd');
    try {
      const rowData: Omit<ProductionRow, 'id' | 'line'> = {
        date: formattedDate,
        od: shiftInputData.od,
        md: shiftInputData.md,
        nd: shiftInputData.nd,
      };
      console.log('[handleShiftInputSubmit] Updating existing row...');
      updateProductionRow(selectedLine, formattedDate, rowData as ProductionRow);

      // Update local storage as well
      const storageKey = getStorageKey(selectedLine);
      let currentProductionData = loadItem<ProductionRow[]>(storageKey, []) || [];
      currentProductionData = currentProductionData.map(row => row.date === formattedDate ? { ...row, ...rowData } : row);
      const saveSuccess = saveItem(storageKey, currentProductionData);

      if (!saveSuccess) {
         console.error("Rollback voor update niet geïmplementeerd");
         throw new Error(`Kon wijzigingen niet opslaan.`);
      }

      // Update active row state after successful save
      const finalProductionData = loadItem<ProductionRow[]>(storageKey, []) || [];
      finalProductionData.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
      const newRowIndex = finalProductionData.findIndex(row => row.date === formattedDate);
      setActiveRowIndex(newRowIndex >= 0 ? newRowIndex : 0); // Update index if needed
      setActiveRow(finalProductionData[newRowIndex >= 0 ? newRowIndex : 0] || null); // Update active row data

      toast.success(`Dag succesvol bijgewerkt`);
      setIsShiftInputOpen(false); setPendingRowDate(null); setEditingRowData(null);
    } catch (error: any) { console.error(`Error updating day:`, error); toast.error(`Fout bij bijwerken dag`, { description: error.message || 'Er is een fout opgetreden.' }); }
  }, [pendingRowDate, selectedLine, editingRowData, TARGETS, updateProductionRow, removeProductionRow]); // Dependencies updated

  const handleDeleteDay = useCallback((line: ProductionLine, date: string, index: number, event: React.MouseEvent) => { event.stopPropagation(); setDayToDelete({ line, date }); setShowDeleteDayDialog(true); }, []);

  const handleConfirmDeleteDay = useCallback(async () => {
    if (!dayToDelete) return; const { line, date } = dayToDelete;
    try {
      const storageKey = getStorageKey(line); let currentLineData = loadItem<ProductionRow[]>(storageKey, []);
      if (!Array.isArray(currentLineData)) { console.error(`Ongeldige data in localStorage:`, currentLineData); currentLineData = []; }
      const updatedLineData = currentLineData.filter(row => row.date !== date);
      const success = saveItem(storageKey, updatedLineData);
      if (!success) { throw new Error("Kon dag niet verwijderen uit localStorage."); }
      removeProductionRow(line, date); // Update context state
      toast.success(`Dag ${format(new Date(date + 'T00:00:00'), 'dd-MM-yyyy')} succesvol lokaal verwijderd`);
    } catch (error: any) { console.error(`Error deleting day:`, error); toast.error('Fout bij verwijderen dag', { description: error.message || 'Er is een fout opgetreden.' }); }
    finally { setShowDeleteDayDialog(false); setDayToDelete(null); }
  }, [dayToDelete, removeProductionRow]);

  return {
    selectedLine,
    activeRow,
    activeRowIndex,
    lineData,
    totalProduction: calculateTotalProduction(),
    popoverOpen,
    searchQuery,
    filteredEquipment,
    dateDialogOpen,
    setDateDialogOpen,
    selectedDate,
    setSelectedDate,
    dayToDelete,
    showDeleteDayDialog,
    setShowDeleteDayDialog,
    handleLineChange,
    handleRowChange,
    handleShiftChange,
    handlePopoverOpenChange,
    handleSearchQueryChange,
    handleAddDay,
    handleDateSelect,
    handleDeleteDay,
    handleConfirmDeleteDay,
    calculateTotalProduction,
    isShiftInputOpen,
    setIsShiftInputOpen,
    pendingRowDate,
    handleShiftInputSubmit,
    handleEditDay,
    editingRowData,
    getStorageKey,
    lastFieldChangeTime
  };
};
