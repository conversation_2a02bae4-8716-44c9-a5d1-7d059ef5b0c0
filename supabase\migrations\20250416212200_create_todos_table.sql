-- Migration script to create the todos table

-- Create the todos table
CREATE TABLE public.todos (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    title text NOT NULL,
    description text,
    priority text CHECK (priority IN ('low', 'medium', 'high')),
    assigned_teams text[], -- Array of team names
    due_date date,
    completed boolean DEFAULT false NOT NULL,
    completed_at timestamp with time zone,
    completed_by uuid REFERENCES auth.users(id) ON DELETE SET NULL,
    created_by uuid REFERENCES auth.users(id) ON DELETE SET NULL DEFAULT auth.uid() -- Defaults to the logged-in user creating the todo
);

-- Add comments to columns for clarity
COMMENT ON COLUMN public.todos.priority IS 'Priority level: low, medium, high';
COMMENT ON COLUMN public.todos.assigned_teams IS 'Array of team names this task is assigned to';
COMMENT ON COLUMN public.todos.completed_by IS 'User who marked the task as completed';
COMMENT ON COLUMN public.todos.created_by IS 'User who created the task';

-- Add indexes for commonly queried columns
CREATE INDEX idx_todos_created_at ON public.todos(created_at);
CREATE INDEX idx_todos_completed ON public.todos(completed);
CREATE INDEX idx_todos_created_by ON public.todos(created_by);
-- Add index on assigned_teams for team-based filtering
CREATE INDEX idx_todos_assigned_teams ON public.todos USING GIN (assigned_teams);

-- Enable Row Level Security (RLS)
ALTER TABLE public.todos ENABLE ROW LEVEL SECURITY;

-- Grant basic permissions
-- authenticated role can perform all actions (permissions will be restricted by RLS)
GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE public.todos TO authenticated;
-- service_role can bypass RLS (useful for server-side operations)
GRANT ALL ON TABLE public.todos TO service_role;

-- RLS Policies for team-based permissions

-- Policy: Allow managers to view all todos
CREATE POLICY "Allow managers to view all todos" ON public.todos
FOR SELECT
USING (EXISTS (SELECT 1 FROM authorized_managers WHERE authorized_managers.user_id = auth.uid()));

-- Policy: Allow users to view todos assigned to their team
CREATE POLICY "Allow users to view todos for their team" ON public.todos
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM user_teams
    WHERE user_teams.user_id = auth.uid()
    AND user_teams.team = ANY(assigned_teams)
  )
);

-- Policy: Allow managers to insert new todos
CREATE POLICY "Allow managers to insert todos" ON public.todos
FOR INSERT
WITH CHECK (EXISTS (SELECT 1 FROM authorized_managers WHERE authorized_managers.user_id = auth.uid()));

-- Policy: Allow managers to update any todo
CREATE POLICY "Allow managers to update any todo" ON public.todos
FOR UPDATE
USING (EXISTS (SELECT 1 FROM authorized_managers WHERE authorized_managers.user_id = auth.uid()));

-- Policy: Allow team members to update completion status of todos assigned to their team
CREATE POLICY "Allow team members to update completion status" ON public.todos
FOR UPDATE
USING (
  EXISTS (
    SELECT 1 FROM user_teams
    WHERE user_teams.user_id = auth.uid()
    AND user_teams.team = ANY(assigned_teams)
  )
)
WITH CHECK (
  -- Only allow updating completion status fields
  (OLD.title = NEW.title) AND
  (OLD.description = NEW.description) AND
  (OLD.priority = NEW.priority) AND
  (OLD.assigned_teams = NEW.assigned_teams) AND
  (OLD.due_date = NEW.due_date) AND
  (OLD.created_by = NEW.created_by) AND
  -- completed, completed_at, and completed_by can be changed
  (NEW.completed_by = auth.uid()) AND
  (NEW.completed_at IS NULL OR NEW.completed_at <= now())
);

-- Policy: Allow managers to delete any todo
CREATE POLICY "Allow managers to delete any todo" ON public.todos
FOR DELETE
USING (EXISTS (SELECT 1 FROM authorized_managers WHERE authorized_managers.user_id = auth.uid()));