// Cleaned src/components/layout/Layout.tsx
import React, { useState, useEffect } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom'; // Added useNavigate
import { supabase } from '@/lib/supabase-client'; // Added supabase import
import { User as SupabaseUser } from '@supabase/supabase-js'; // Renamed to avoid conflict
import { useAuth } from '@/context/AuthContext';
import { NotificationPopover } from '@/components/notifications/NotificationPopover'; // Import NotificationPopover
import { Button } from '@/components/ui/button'; // Added Button import
import {
  LineChart,          // Re-added missing icon
  AlertCircle,        // Re-added missing icon
  ClipboardList,
  FileText,
  History,
  Menu,
  X,
  Settings,
  ShieldCheck,
  LogOut,             // Added for Logout button
  LogIn,              // Added for Login button
  User                // Added for Account link
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useIsMobile } from '@/hooks/use-mobile';
import { NavigationMenu, NavigationMenuItem, NavigationMenuList } from '@/components/ui/navigation-menu';
import { Avatar, AvatarFallback } from '@/components/ui/avatar'; // Added Avatar imports
import GradientDefs from '@/components/ui/GradientDefs'; // Import gradient definitions
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"; // Added DropdownMenu imports
import SyncIndicator from '@/components/ui/SyncIndicator';
import LogbookNotification from '@/components/logbook/LogbookNotification'; // Import Logbook Notif
import TodoNotification from '@/components/todo/TodoNotification'; // Import Todo Notif


// Define a simple Profile type (adjust based on actual 'profiles' table structure)
interface UserProfile {
  user_id: string;
  ploeg?: string | null; // Assuming 'ploeg' is the column name
  // Add other profile fields if needed
}

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const location = useLocation();
  const isMobile = useIsMobile();
  const navigate = useNavigate(); // Added navigate hook
  const { signOut } = useAuth(); // Get signOut from AuthContext
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [isAuthorizedManager, setIsAuthorizedManager] = useState(false); // State for authorization
  const [currentUser, setCurrentUser] = useState<SupabaseUser | null>(null); // State for current user
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null); // State for user profile

  const toggleMobileMenu = () => {
    setMobileMenuOpen(prev => !prev);
  }

  const closeMobileMenu = () => {
    setMobileMenuOpen(false);
  }

  const handleLogout = async () => {
    try {
      await signOut(); // Use signOut from AuthContext
      navigate('/auth'); // Redirect to login page after logout
      closeMobileMenu(); // Close mobile menu if open
    } catch (error) {
      console.error('Error during logout:', error);
    }
  };

  // Effect to check authorization and user status
  useEffect(() => {
    // console.log('Layout useEffect running...'); // Removed debug log

    const checkAuthAndProfile = async (user: SupabaseUser | null) => {
      // console.log('checkAuthAndProfile called. User:', user); // Removed debug log
      setCurrentUser(user); // Always update user state
      setUserProfile(null); // Reset profile on auth change
      setIsAuthorizedManager(false); // Reset manager status

      if (user) {
        // console.log(`Checking authorization and profile for user ID: ${user.id}`); // Removed debug log
        try {
          // Fetch authorization status and profile data in parallel
          const [authResult, profileResult] = await Promise.all([
            supabase
              .from('authorized_managers')
              .select('user_id')
              .eq('user_id', user.id)
              .maybeSingle(),
            supabase
              .from('user_teams') // Fetch from user_teams table
              .select('user_id, team') // Select needed profile fields
              .eq('user_id', user.id)
              .maybeSingle()
          ]);

          // Handle authorization result
          if (authResult.error) {
            console.error('Error checking authorization:', authResult.error); // Keep error log
          } else {
            setIsAuthorizedManager(!!authResult.data);
            // console.log(`Setting isAuthorizedManager to: ${!!authResult.data}`); // Removed debug log
          }

          // Handle profile result
          if (profileResult.error) {
            console.error('Error fetching profile:', profileResult.error); // Keep error log
          } else if (profileResult.data) {
            setUserProfile({
              user_id: profileResult.data.user_id,
              ploeg: profileResult.data.team
            });
            // console.log('Fetched profile:', profileResult.data); // Removed debug log
          }

        } catch (err) {
          console.error('Exception during auth/profile check:', err); // Keep error log
          // Ensure states are reset in case of unexpected error
          setIsAuthorizedManager(false);
          setUserProfile(null);
        }
      }
      // No else needed, states are reset at the beginning of the function if user is null
    };

    // Initial check
    // console.log('Performing initial auth check...'); // Removed debug log
    supabase.auth.getSession().then(({ data: { session } }) => {
      // console.log('Initial session:', session); // Removed debug log
      checkAuthAndProfile(session?.user ?? null);
    });

    // Listen for auth changes
    // console.log('Setting up auth state change listener...'); // Removed debug log
    const { data: authListener } = supabase.auth.onAuthStateChange((_event, session) => {
      // console.log('Auth state changed:', { _event, session }); // Removed debug log
      checkAuthAndProfile(session?.user ?? null);
    });

    // Cleanup listener on component unmount
    return () => {
      // console.log('Cleaning up auth listener.'); // Removed debug log
      authListener?.subscription.unsubscribe();
    };
  }, []); // Empty dependency array ensures this runs once on mount and sets up listener

  // isManagementPage is used in the dropdown menu to highlight the current section
  const isManagementPage = location.pathname.startsWith('/management');
  const isActive = (path: string) => location.pathname === path;

  // Helper function to get initials from email
  const getInitials = (email: string | undefined): string => {
    if (!email) return 'U';
    return email.substring(0, 1).toUpperCase();
  };

  // Helper function to get avatar color based on team
  const getAvatarColor = (profile: UserProfile | null, isManager: boolean): string => {
    if (isManager) {
      return "bg-amber-100 text-amber-600"; // Distinct color for managers
    }

    // Colors for different teams
    switch (profile?.ploeg) {
      case 'Blauw':
        return "bg-blue-100 text-blue-600";
      case 'Wit':
        return "bg-slate-100 text-slate-600";
      case 'Geel':
        return "bg-yellow-100 text-yellow-600";
      case 'Groen':
        return "bg-green-100 text-green-600";
      case 'Rood':
        return "bg-red-100 text-red-600";
      default:
        return "bg-gray-100 text-gray-600"; // Default color
    }
  };

  // console.log(`Rendering Layout. isAuthorizedManager: ${isAuthorizedManager}`); // Removed debug log

  return (
    <div className="relative min-h-screen bg-[url('/background.webp')] bg-cover bg-center bg-no-repeat"> {/* Added relative */}
      {/* Black Overlay */}
      <div className="absolute inset-0 bg-black/70"></div>

      {/* Add SVG gradient definitions */}
      <GradientDefs />
      <div className="relative z-10 container mx-auto py-4 px-4 md:px-6"> {/* Added relative z-10 */}
        <div className="bg-white rounded-2xl shadow-sm overflow-hidden">
          <header className="border-b border-gray-200">
            <div className="flex h-20 items-center justify-between px-6">
              {/* Logo */}
              <div className="flex items-center space-x-4">
                <img
                  src="/logo.png"
                  alt="Cirrec Logo"
                  className="h-12 w-auto drop-shadow-sm"
                />
              </div>

              {/* Desktop Navigation */}
              {!isMobile && (
                <div className="flex-grow flex justify-center">
                  <NavigationMenu className="px-8">
                    <NavigationMenuList className="space-x-16">
                      {/* Standard Links - Improved styling */}
                      <NavigationMenuItem>
                        <Link
                          to="/safety-quality"
                          className={cn(
                            "px-8 py-4 text-lg font-medium rounded-md border border-transparent", // Removed text-blue-700
                            "",
                            isActive('/safety-quality') && "shadow-sm" // Removed border-blue-200
                          )}
                        >
                          {/* Added gradient classes to this div */}
                          {/* Removed icon and gap-3 */}
                          <div className="flex items-center pointer-events-none">
                            {/* Added gradient classes to the text span */}
                            <span className="bg-gradient-to-r from-gray-600 to-gray-900 bg-clip-text text-transparent">Veiligheid & Kwaliteit</span>
                          </div>
                        </Link>
                      </NavigationMenuItem>

                      <NavigationMenuItem>
                        <Link
                          to="/results"
                          className={cn(
                            "px-8 py-4 text-lg font-medium rounded-md border border-transparent", // Removed text-green-700
                            "",
                            isActive('/results') && "shadow-sm" // Removed border-green-200
                          )}
                        >
                          {/* Added gradient classes to this div */}
                          {/* Removed icon and gap-3 */}
                          <div className="flex items-center pointer-events-none">
                            {/* Added gradient classes to the text span */}
                            <span className="bg-gradient-to-r from-gray-600 to-gray-900 bg-clip-text text-transparent">Dagstart</span>
                          </div>
                        </Link>
                      </NavigationMenuItem>

                      <NavigationMenuItem>
                        <Link
                          to="/disruptions"
                          className={cn(
                            "px-8 py-4 text-lg font-medium rounded-md border border-transparent", // Removed text-amber-700
                            "",
                            isActive('/disruptions') && "shadow-sm" // Removed border-amber-200
                          )}
                        >
                          {/* Added gradient classes to this div */}
                          {/* Removed icon and gap-3 */}
                          <div className="flex items-center pointer-events-none">
                            {/* Added gradient classes to the text span */}
                            <span className="bg-gradient-to-r from-gray-600 to-gray-900 bg-clip-text text-transparent">Storingen</span>
                          </div>
                        </Link>
                      </NavigationMenuItem>

                      <NavigationMenuItem>
                        <Link
                          to="/overdracht"
                          className={cn(
                            "px-8 py-4 text-lg font-medium rounded-md border border-transparent", // Removed text-purple-700
                            "",
                            isActive('/overdracht') && "shadow-sm" // Removed border-purple-200
                          )}
                        >
                          {/* Added gradient classes to this div */}
                          {/* Removed icon and gap-3 */}
                          <div className="flex items-center pointer-events-none">
                            {/* Added gradient classes to the text span */}
                            <span className="bg-gradient-to-r from-gray-600 to-gray-900 bg-clip-text text-transparent">Overdracht</span>
                          </div>
                        </Link>
                      </NavigationMenuItem>

                      {/* Settings link removed - now only in avatar dropdown */}
                    </NavigationMenuList>
                  </NavigationMenu>
                </div>
              )}

              {/* Auth Status / Actions */}
              <div className="flex items-center gap-4">
                  <LogbookNotification />

                {!isMobile && <SyncIndicator />}
                {!isMobile && currentUser && (
                  <>
                    <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <button type="button" className="flex items-center gap-2 focus:outline-none">
                        {/* Wrap Avatar in relative div for badge positioning */}
                        <div className="relative">
                          <Avatar className="h-8 w-8 cursor-pointer">
                            <AvatarFallback className={getAvatarColor(userProfile, isAuthorizedManager)}>
                              {getInitials(currentUser.email)}
                            </AvatarFallback>
                          </Avatar>
                        </div>
                      </button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-56">
                      <DropdownMenuLabel>
                        <div className="flex flex-col space-y-1">
                          <p className="text-sm font-medium leading-none">{currentUser.email}</p>
                          {isAuthorizedManager && (
                            <p className="text-xs text-muted-foreground flex items-center">
                              <ShieldCheck className="h-3 w-3 mr-1 !min-h-3 !min-w-3" /> Beheerder
                            </p>
                          )}
                          {userProfile?.ploeg && (
                            <p className="text-xs text-muted-foreground">
                              Ploeg: {userProfile.ploeg}
                            </p>
                          )}
                        </div>
                      </DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem asChild>
                        <Link to="/account" className="flex w-full cursor-pointer items-center">
                          <User className="mr-2 h-4 w-4 !min-h-4 !min-w-4" />
                          Mijn account
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link to="/settings/toggles" className="flex w-full cursor-pointer items-center">
                          <Settings className="mr-2 h-4 w-4 !min-h-4 !min-w-4" />
                          Instellingen
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link to="/todos" className="flex w-full cursor-pointer items-center">
                          <ClipboardList className="mr-2 h-4 w-4 !min-h-4 !min-w-4" />
                          Todo Lijst
                        </Link>
                      </DropdownMenuItem>
                      <DropdownMenuItem asChild>
                        <Link to="/history" className="flex w-full cursor-pointer items-center">
                          <History className="mr-2 h-4 w-4 !min-h-4 !min-w-4" />
                          Geschiedenis
                        </Link>
                      </DropdownMenuItem>
                      {isAuthorizedManager && (
                        <>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem asChild>
                            <Link to="/management" className={`flex w-full cursor-pointer items-center ${isManagementPage ? 'bg-accent text-accent-foreground' : ''}`}>
                              <ShieldCheck className="mr-2 h-4 w-4 !min-h-4 !min-w-4" />
                              Beheer
                            </Link>
                          </DropdownMenuItem>
                        </>
                      )}
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={handleLogout} className="text-red-600 cursor-pointer">
                        <LogOut className="mr-2 h-4 w-4 !min-h-4 !min-w-4" />
                        Uitloggen
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                    <NotificationPopover />
                  </>
                )}
                {!currentUser && (
                  <Button variant="ghost" size="icon" onClick={() => navigate('/auth')} title="Login">
                    <LogIn className="h-5 w-5 !min-h-5 !min-w-5" />
                  </Button>
                )}

                {/* Mobile Menu Toggle */}
                {isMobile && (
                  <Button variant="ghost" size="icon" onClick={toggleMobileMenu}>
                    {mobileMenuOpen ? <X size={24} className="!min-h-6 !min-w-6" /> : <Menu size={24} className="!min-h-6 !min-w-6" />}
                  </Button>
                )}
              </div>
            </div>
          </header>

          {/* Mobile Menu */}
          {isMobile && mobileMenuOpen && (
            <div className="fixed inset-0 z-40 bg-black bg-opacity-50" onClick={closeMobileMenu}> {/* Close on overlay click */}
              <div className="fixed top-20 left-0 right-0 bg-white shadow-lg" onClick={(e) => e.stopPropagation()}> {/* Prevent closing when clicking inside menu */}
                <nav className="flex flex-col p-5">
                  {/* Improved Mobile Links with better styling */}
                  <div className="space-y-4 py-3">
                    <Link
                      to="/safety-quality"
                      className={cn(
                        "flex items-center p-3 rounded-lg text-base font-medium text-blue-700",
                        "border-l-4 border-transparent",
                        isActive('/safety-quality') ? "bg-blue-50 border-blue-500 text-blue-700" : "text-gray-700"
                      )}
                      onClick={closeMobileMenu}
                    >
                      <span className="flex items-center pointer-events-none">
                        <AlertCircle className="w-5 h-5 mr-3 !min-h-5 !min-w-5" />
                        <span>Veiligheid & Kwaliteit</span>
                      </span>
                    </Link>

                    <Link
                      to="/results"
                      className={cn(
                        "flex items-center p-3 rounded-lg text-base font-medium text-green-700",
                        "border-l-4 border-transparent",
                        isActive('/results') ? "bg-green-50 border-green-500 text-green-700" : "text-gray-700"
                      )}
                      onClick={closeMobileMenu}
                    >
                      <span className="flex items-center pointer-events-none">
                        <LineChart className="w-5 h-5 mr-3 !min-h-5 !min-w-5" />
                        <span>Dagstart</span>
                      </span>
                    </Link>

                    <Link
                      to="/disruptions"
                      className={cn(
                        "flex items-center p-3 rounded-lg text-base font-medium text-amber-700",
                        "border-l-4 border-transparent",
                        isActive('/disruptions') ? "bg-amber-50 border-amber-500 text-amber-700" : "text-gray-700"
                      )}
                      onClick={closeMobileMenu}
                    >
                      <span className="flex items-center pointer-events-none">
                        <ClipboardList className="w-5 h-5 mr-3 !min-h-5 !min-w-5" />
                        <span>Storingen</span>
                      </span>
                    </Link>

                    <Link
                      to="/overdracht"
                      className={cn(
                        "flex items-center p-3 rounded-lg text-base font-medium text-purple-700",
                        "border-l-4 border-transparent",
                        isActive('/overdracht') ? "bg-purple-50 border-purple-500 text-purple-700" : "text-gray-700"
                      )}
                      onClick={closeMobileMenu}
                    >
                      <span className="flex items-center pointer-events-none">
                        <FileText className="w-5 h-5 mr-3 !min-h-5 !min-w-5" />
                        <span>Overdracht</span>
                      </span>
                    </Link>
                  </div>

                  {/* Settings moved to avatar dropdown, but keeping history link */}
                  <div className="mt-6 pt-4 border-t border-gray-100">
                    <Link
                      to="/history"
                      className={cn(
                        "flex items-center p-3 rounded-lg text-base font-medium text-gray-700",
                        "border-l-4 border-transparent",
                        isActive('/history') ? "bg-gray-50 border-gray-500 text-gray-700" : "text-gray-600"
                      )}
                      onClick={closeMobileMenu}
                    >
                      <span className="flex items-center pointer-events-none">
                        <History className="w-5 h-5 mr-3 !min-h-5 !min-w-5" />
                        <span>Geschiedenis</span>
                      </span>
                    </Link>
                    <Link
                      to="/todos"
                      className={cn(
                        "flex items-center p-3 rounded-lg text-base font-medium text-gray-700",
                        "border-l-4 border-transparent",
                        isActive('/todos') && !isActive('/todos/historie') ? "bg-gray-50 border-gray-500 text-gray-700" : "text-gray-600"
                      )}
                      onClick={closeMobileMenu}
                    >
                      <span className="flex items-center pointer-events-none">
                        <ClipboardList className="w-5 h-5 mr-3 !min-h-5 !min-w-5" />
                        <span>Todo Lijst</span>
                      </span>
                    </Link>
                    <Link
                      to="/todos/historie"
                      className={cn(
                        "flex items-center p-3 rounded-lg text-base font-medium text-gray-700",
                        "border-l-4 border-transparent",
                        isActive('/todos/historie') ? "bg-gray-50 border-gray-500 text-gray-700" : "text-gray-600"
                      )}
                      onClick={closeMobileMenu}
                    >
                      <span className="flex items-center pointer-events-none">
                        <History className="w-5 h-5 mr-3 !min-h-5 !min-w-5" />
                        <span>Todo Geschiedenis</span>
                      </span>
                    </Link>
                  </div>

                  {/* Management Link (Mobile) removed - now in avatar dropdown */}

                  {/* Auth Links (Mobile) */}
                  <div className="border-t mt-6 pt-4">
                    {currentUser ? (
                      <>
                        <Link
                          to="/account"
                          className="flex items-center gap-2 text-base px-3 py-2 mb-3 text-primary hover:underline rounded-md"
                          onClick={closeMobileMenu}
                        >
                          <User className="h-5 w-5 mr-2 !min-h-5 !min-w-5" />
                          <span>{currentUser.email}</span>
                          {isAuthorizedManager && <span className="ml-1 text-xs bg-amber-100 text-amber-600 px-1 rounded">Beheerder</span>}
                          {userProfile?.ploeg && <span className="ml-1 text-xs bg-blue-100 text-blue-600 px-1 rounded">{userProfile.ploeg}</span>}
                        </Link>
                        <button type="button" onClick={handleLogout} className="flex items-center w-full text-left text-red-600 hover:bg-red-50 p-3 rounded-md text-base font-medium">
                          <LogOut className="w-5 h-5 mr-3 !min-h-5 !min-w-5" /> Uitloggen
                        </button>
                      </>
                    ) : (
                      <Link to="/auth" className="flex items-center p-3 rounded-md text-base font-medium text-primary" onClick={closeMobileMenu}>
                        <LogIn className="w-5 h-5 mr-3 !min-h-5 !min-w-5" /> Inloggen
                      </Link>
                    )}
                  </div>
                </nav>
              </div>
            </div>
          )}

          <main className="flex-grow p-6">
            <div className="animate-fade-in h-full">
              {children}
            </div>
            {isMobile && (
              <div className="mt-6 flex justify-center">
                <SyncIndicator />
              </div>
            )}
          </main>
        </div>
      </div>
    </div>
  );
};

export default Layout;
