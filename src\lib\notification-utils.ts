import { supabase } from './supabase-client';
import { TodoItem, NotificationType } from '@/types';
import { format } from 'date-fns';
import { nl } from 'date-fns/locale';

// Create a notification for a user
export const createNotification = async (
  userId: string,
  type: NotificationType,
  message: string,
  relatedId?: string,
  data?: Record<string, any>
) => {
  try {
    const { error } = await supabase
      .from('notifications')
      .insert([
        {
          user_id: userId,
          type,
          message,
          related_id: relatedId,
          data
        }
      ]);

    if (error) {
      console.error('Error creating notification:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Unexpected error creating notification:', error);
    return false;
  }
};

// Create a notification for a todo completion
export const createTodoCompletedNotification = async (todo: TodoItem, completedByName: string) => {
  try {
    // Get the creator of the todo
    if (!todo.created_by) {
      console.error('Todo has no creator ID');
      return false;
    }

    // Only send notification if the completer is not the creator
    if (todo.created_by === todo.completed_by) {
      console.log('Todo completed by creator, no notification needed');
      return true;
    }

    // Create notification for the creator
    const message = `Taak "${todo.title}" is afgerond door ${completedByName || 'een gebruiker'}.`;

    return await createNotification(
      todo.created_by,
      'todo_completed',
      message,
      todo.id,
      {
        todoTitle: todo.title,
        completedBy: todo.completed_by,
        completedByName: completedByName,
        completedAt: todo.completed_at,
        comments: todo.comments
      }
    );
  } catch (error) {
    console.error('Error creating todo completed notification:', error);
    return false;
  }
};

// Create notifications for assigned teams
export const createTodoAssignedNotification = async (todo: TodoItem, teamMembers: string[]) => {
  try {
    const dueDateText = todo.due_date ? ` (deadline: ${format(new Date(todo.due_date), 'dd-MM-yyyy', { locale: nl })})` : '';
    const message = `Nieuwe taak "${todo.title}"${dueDateText} is aan je ploeg toegewezen.`;

    // Create a notification for each team member
    const promises = teamMembers.map(userId =>
      createNotification(
        userId,
        'todo_assigned',
        message,
        todo.id,
        {
          todoTitle: todo.title,
          createdBy: todo.created_by,
          createdByName: todo.created_by_name,
          dueDate: todo.due_date,
          priority: todo.priority
        }
      )
    );

    await Promise.all(promises);
    return true;
  } catch (error) {
    console.error('Error creating todo assigned notifications:', error);
    return false;
  }
};

// Get all users in a team
export const getUsersInTeam = async (team: string): Promise<string[]> => {
  try {
    console.log(`Fetching users for team: ${team}`);

    const { data, error } = await supabase
      .from('user_teams')
      .select('user_id')
      .eq('team', team);

    if (error) {
      console.error('Error fetching users in team:', error);
      return [];
    }

    console.log(`Found ${data?.length || 0} users in team ${team}:`, data);

    // If no users found, try to get all users with this team from profiles table
    if (!data || data.length === 0) {
      console.log(`No users found in user_teams, trying profiles table for team ${team}`);

      const { data: profilesData, error: profilesError } = await supabase
        .from('profiles')
        .select('id')
        .eq('ploeg', team);

      if (profilesError) {
        console.error('Error fetching profiles for team:', profilesError);
        return [];
      }

      console.log(`Found ${profilesData?.length || 0} profiles with team ${team}:`, profilesData);

      if (profilesData && profilesData.length > 0) {
        return profilesData.map(profile => profile.id);
      }
    }

    return data.map(item => item.user_id);
  } catch (error) {
    console.error('Unexpected error fetching users in team:', error);
    return [];
  }
};

// Create notifications for all users in specified teams
export const createTeamNotifications = async (teams: string[], message: string, type: NotificationType, relatedId?: string, data?: Record<string, any>, updateExisting: boolean = true) => {
  try {
    // Get all users in the specified teams
    const userPromises = teams.map(team => getUsersInTeam(team));
    const usersInTeams = await Promise.all(userPromises);

    // Flatten the array of arrays and remove duplicates
    const userIds = [...new Set(usersInTeams.flat())];

    // For each user, check if they already have a notification for this todo
    // If they do, update it. If not, create a new one.
    const notificationPromises = userIds.map(async (userId) => {
      if (relatedId && type === 'todo_assigned') {
        // Check if user already has a notification for this todo
        const { data: existingNotifications, error } = await supabase
          .from('notifications')
          .select('id')
          .eq('user_id', userId)
          .eq('type', type)
          .eq('related_id', relatedId);

        if (error) {
          console.error('Error checking existing notifications:', error);
        } else if (existingNotifications && existingNotifications.length > 0) {
          // Update existing notification instead of creating a new one
          const { error: updateError } = await supabase
            .from('notifications')
            .update({
              message,
              read: false, // Mark as unread again
              created_at: new Date().toISOString(), // Update timestamp
              data
            })
            .eq('id', existingNotifications[0].id);

          if (updateError) {
            console.error('Error updating notification:', updateError);
          }

          return; // Skip creating a new notification
        }
      }

      // Create a new notification if no existing one was found or updated
      return createNotification(userId, type, message, relatedId, data);
    });

    await Promise.all(notificationPromises);
    return true;
  } catch (error) {
    console.error('Error creating team notifications:', error);
    return false;
  }
};

// Send daily reminders for open todos
export const sendDailyTodoReminders = async () => {
  try {
    // Get all incomplete todos
    const { data: openTodos, error } = await supabase
      .from('todos')
      .select('*')
      .eq('completed', false);

    if (error) {
      console.error('Error fetching open todos for daily reminders:', error);
      return false;
    }

    if (!openTodos || openTodos.length === 0) {
      console.log('No open todos to send reminders for');
      return true;
    }

    console.log(`Sending daily reminders for ${openTodos.length} open todos`);

    // Process each open todo
    for (const todo of openTodos) {
      if (todo.assigned_teams && todo.assigned_teams.length > 0) {
        // Create reminder message
        const dueDateText = todo.due_date
          ? ` (deadline: ${format(new Date(todo.due_date), 'dd-MM-yyyy', { locale: nl })})`
          : '';
        const message = `Herinnering: Openstaande taak "${todo.title}"${dueDateText} wacht op afronding.`;

        // Send notification to all assigned teams
        await createTeamNotifications(
          todo.assigned_teams,
          message,
          'todo_assigned',
          todo.id,
          {
            todoTitle: todo.title,
            createdBy: todo.created_by,
            dueDate: todo.due_date,
            priority: todo.priority,
            isReminder: true
          }
        );
      }
    }

    return true;
  } catch (error) {
    console.error('Error sending daily todo reminders:', error);
    return false;
  }
};

// Check for expired todos and create notifications
export const checkForExpiredTodos = async () => {
  try {
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Set to beginning of day

    // Get all todos that are expired (due date is in the past) and not completed
    const { data: expiredTodos, error } = await supabase
      .from('todos')
      .select('*')
      .lt('due_date', today.toISOString())
      .eq('completed', false)
      .eq('expired_notified', false); // Only get todos that haven't been notified yet

    if (error) {
      console.error('Error checking for expired todos:', error);
      return false;
    }

    if (!expiredTodos || expiredTodos.length === 0) {
      return true; // No expired todos
    }

    // Process each expired todo
    for (const todo of expiredTodos) {
      // 1. Create notification for the assigned teams that the todo has expired
      if (todo.assigned_teams && todo.assigned_teams.length > 0) {
        const formattedDate = format(new Date(todo.due_date), 'dd-MM-yyyy', { locale: nl });
        const teamMessage = `Taak "${todo.title}" is verlopen (deadline was ${formattedDate}).`;

        await createTeamNotifications(
          todo.assigned_teams,
          teamMessage,
          'todo_expired',
          todo.id,
          {
            todoTitle: todo.title,
            dueDate: todo.due_date,
            priority: todo.priority
          }
        );
      }

      // 2. Create notification for the creator that the todo was not completed
      if (todo.created_by) {
        const formattedDate = format(new Date(todo.due_date), 'dd-MM-yyyy', { locale: nl });
        const creatorMessage = `Taak "${todo.title}" is niet voltooid door de toegewezen ploeg(en) en is verlopen (deadline was ${formattedDate}).`;

        await createNotification(
          todo.created_by,
          'todo_not_completed',
          creatorMessage,
          todo.id,
          {
            todoTitle: todo.title,
            dueDate: todo.due_date,
            assignedTeams: todo.assigned_teams
          }
        );
      }

      // 3. Mark the todo as notified so we don't send duplicate notifications
      await supabase
        .from('todos')
        .update({ expired_notified: true })
        .eq('id', todo.id);
    }

    return true;
  } catch (error) {
    console.error('Error processing expired todos:', error);
    return false;
  }
};