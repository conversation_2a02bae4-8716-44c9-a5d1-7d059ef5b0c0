import React, { useState, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { ChevronsUpDown, Check as CheckIcon, Search, X, ArrowLeft, User } from 'lucide-react';
import { Link } from 'react-router-dom';
import { useProduction } from '@/context/ProductionContext';
import { ProductionLine, EquipmentEntry, ShiftColor, TdLogEntry } from '@/types';
import { cn } from '@/lib/utils';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command";
import { format, parseISO } from 'date-fns';
import { nl } from 'date-fns/locale';

// Helper functions
const getShiftTextColor = (color: ShiftColor): string => {
    switch (color) {
        case 'Geel': return 'text-yellow-800';
        case 'Blauw': return 'text-blue-800';
        case 'Groen': return 'text-green-800';
        case 'Rood': return 'text-red-800';
        case 'Wit': return 'text-gray-800';
        default: return 'text-gray-500';
    }
};
const getShiftBgColor = (color: ShiftColor): string => {
    switch (color) {
        case 'Geel': return 'bg-yellow-100';
        case 'Blauw': return 'bg-blue-100';
        case 'Groen': return 'bg-green-100';
        case 'Rood': return 'bg-red-100';
        case 'Wit': return 'bg-gray-100';
        default: return 'bg-gray-100';
    }
};

const TdLogbookHistoryPage: React.FC = () => {
  const { tdLogEntries, equipmentOptions } = useProduction();

  // State for Filters
  const [filterText, setFilterText] = useState('');
  const [filterName, setFilterName] = useState<string | 'all'>('all');
  const [filterLine, setFilterLine] = useState<ProductionLine | 'all'>('all');
  const [filterEquipment, setFilterEquipment] = useState('');
  const [filterShiftColor, setFilterShiftColor] = useState<ShiftColor | 'all'>('all');
  const [filterEquipmentPopoverOpen, setFilterEquipmentPopoverOpen] = useState(false);
  const [filterEquipmentSearch, setFilterEquipmentSearch] = useState('');
  // --- Memoized Reporter Names ---
  const reporterNames = useMemo(() => {
    const names = new Set<string>();
    tdLogEntries.forEach(entry => {
      if (entry.reporter_name) {
        names.add(entry.reporter_name);
      }
    });
    return Array.from(names).sort(); // Sort alphabetically
  }, [tdLogEntries]);


  // --- Memoized Options ---
  const filterEquipmentOptions = useMemo(() => {
      let allOptions: EquipmentEntry[] = [];
      if (filterLine === 'all') {
          Object.values(equipmentOptions).forEach(lineOpts => {
              if (lineOpts) allOptions = allOptions.concat(Object.values(lineOpts).flat());
          });
      } else {
          const lineOpts = equipmentOptions[filterLine];
          if (lineOpts) allOptions = Object.values(lineOpts).flat();
      }
      const uniqueOptions = Array.from(new Map(allOptions.map(opt => [opt.value, opt])).values());
      return uniqueOptions.map(opt => ({ value: opt.value, label: opt.label_nl }));
  }, [filterLine, equipmentOptions]);

  // --- Filtered Entries (Older than 5 Days) ---
  const filteredHistoryEntries = useMemo(() => {
    const fiveDaysAgo = new Date();
    fiveDaysAgo.setDate(fiveDaysAgo.getDate() - 5);
    fiveDaysAgo.setHours(0, 0, 0, 0);

    return tdLogEntries
      .filter(entry => {
        try {
          if (parseISO(entry.timestamp) >= fiveDaysAgo) {
            return false;
          }
        } catch (e) {
          console.error("Error parsing entry timestamp:", entry.timestamp, e);
          return false;
        }

        const textMatch = filterText ? entry.text.toLowerCase().includes(filterText.toLowerCase()) : true;
        const lineMatch = filterLine === 'all' ? true : entry.line === filterLine;
        const equipmentMatch = filterEquipment ? entry.equipment === filterEquipment : true;
        const shiftColorMatch = filterShiftColor === 'all' ? true : entry.shiftColor === filterShiftColor;
        const nameMatch = filterName === 'all' ? true : entry.reporter_name === filterName;
        return textMatch && lineMatch && equipmentMatch && shiftColorMatch && nameMatch;
      })
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()); // Sort newest first within history
  }, [tdLogEntries, filterText, filterLine, filterEquipment, filterShiftColor, filterName]);


  // --- JSX ---
  return (
    <div className="animate-fade-in p-4 md:p-6">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <h1 className="text-2xl md:text-3xl font-semibold text-faerch-blue mb-4 md:mb-0">
          TD Logboek Historie
        </h1>
         <Link to="/td-logboek" className="text-sm text-blue-600 hover:underline inline-flex items-center gap-1">
           <ArrowLeft className="h-4 w-4" />
           Terug naar recent logboek
         </Link>
      </div>

      {/* --- Filter Bar --- */}
      <div className="mb-6 p-4 border rounded-lg bg-gray-50 shadow-sm">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 items-end">
          {/* Text Search */}
          <div className="space-y-1 px-6">
            <Label htmlFor="filter-text" className="text-xs">Zoekterm</Label>
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                id="filter-text"
                placeholder="Zoek in melding..."
                value={filterText}
                onChange={(e) => setFilterText(e.target.value)}
                className="pl-8"
              />
              {filterText && (
                 <Button variant="ghost" size="icon" className="absolute right-1 top-1 h-6 w-6" onClick={() => setFilterText('')}>
                    <X className="h-4 w-4" />
                 </Button>
              )}
            </div>
          </div>
          {/* Name Filter */}
          <div className="space-y-1 px-6">
            <Label htmlFor="filter-name" className="text-xs">Naam</Label>
            <Select value={filterName} onValueChange={(value) => setFilterName(value)}>
              <SelectTrigger id="filter-name">
                <SelectValue placeholder="Selecteer naam" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Alle Namen</SelectItem>
                {reporterNames.map(name => (
                  <SelectItem key={name} value={name}>{name}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>


          {/* Line Filter */}
          <div className="space-y-1 px-6">
            <Label htmlFor="filter-line" className="text-xs">Lijn</Label>
            <Select value={filterLine} onValueChange={(value) => setFilterLine(value as ProductionLine | 'all')}>
              <SelectTrigger id="filter-line">
                <SelectValue placeholder="Selecteer lijn" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Alle Lijnen</SelectItem>
                <SelectItem value="tl1">TL1</SelectItem>
                <SelectItem value="tl2">TL2</SelectItem>
                <SelectItem value="p1">P1</SelectItem>
                <SelectItem value="p2">P2</SelectItem>
                <SelectItem value="p3">P3</SelectItem>
                <SelectItem value="Yard">Yard</SelectItem>
                <SelectItem value="Koeling">Koeling</SelectItem>
                <SelectItem value="Gebouw">Gebouw</SelectItem>
                <SelectItem value="Overige">Overige</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Equipment Filter */}
          <div className="space-y-1 px-6">
            <Label htmlFor="filter-equipment" className="text-xs">Onderdeel</Label>
            <Popover open={filterEquipmentPopoverOpen} onOpenChange={setFilterEquipmentPopoverOpen}>
              <PopoverTrigger asChild>
                <Button variant="outline" role="combobox" aria-expanded={filterEquipmentPopoverOpen} className="w-full justify-between font-normal">
                  {filterEquipment ? filterEquipmentOptions.find((opt) => opt.value === filterEquipment)?.label : "Alle Onderdelen"}
                  <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-[--radix-popover-trigger-width] max-h-[--radix-popover-content-available-height] p-0">
                <Command filter={(value, search) => {
                    if (value === '__OTHER__') return search.toLowerCase().includes('overige') ? 1 : 0; // Allow filtering for "Overige"
                    const item = filterEquipmentOptions.find(opt => opt.value === value);
                    if (!item) return 0;
                    const searchTerm = search.toLowerCase();
                    const labelMatch = item.label.toLowerCase().includes(searchTerm);
                    const valueMatch = item.value.toLowerCase().includes(searchTerm);
                    return (labelMatch || valueMatch) ? 1 : 0;
                 }}>
                  <CommandInput placeholder="Zoek onderdeel..." value={filterEquipmentSearch} onValueChange={setFilterEquipmentSearch} />
                  <CommandList>
                    <CommandEmpty>Geen onderdeel gevonden.</CommandEmpty>
                    <CommandItem key="all-equipment" value="" onSelect={() => { setFilterEquipment(''); setFilterEquipmentPopoverOpen(false); setFilterEquipmentSearch(''); }}>
                      <CheckIcon className={cn("mr-2 h-4 w-4", !filterEquipment ? "opacity-100" : "opacity-0")} />
                      Alle Onderdelen
                    </CommandItem>
                     {/* Add "Overige" option to filter */}
                     <CommandItem key="__OTHER__" value="__OTHER__" onSelect={() => { setFilterEquipment('__OTHER__'); setFilterEquipmentPopoverOpen(false); setFilterEquipmentSearch(''); }}>
                       <CheckIcon className={cn("mr-2 h-4 w-4", filterEquipment === '__OTHER__' ? "opacity-100" : "opacity-0")} />
                       Overige
                     </CommandItem>
                    <CommandGroup>
                      {filterEquipmentOptions.map((option) => (
                        <CommandItem key={option.value} value={option.value} onSelect={(currentValue) => { setFilterEquipment(currentValue === filterEquipment ? "" : currentValue); setFilterEquipmentPopoverOpen(false); setFilterEquipmentSearch(''); }}>
                          <CheckIcon className={cn("mr-2 h-4 w-4", filterEquipment === option.value ? "opacity-100" : "opacity-0")} />
                          {option.label} ({option.value})
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  </CommandList>
                </Command>
              </PopoverContent>
            </Popover>
          </div>

          {/* Shift Color Filter */}
          <div className="space-y-1 px-6">
            <Label htmlFor="filter-shift-color" className="text-xs">Ploeg</Label>
            <Select value={filterShiftColor} onValueChange={(value) => setFilterShiftColor(value as ShiftColor | 'all')}>
              <SelectTrigger id="filter-shift-color">
                <SelectValue placeholder="Selecteer ploeg" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Alle Ploegen</SelectItem>
                <SelectItem value="Geel" className={getShiftTextColor('Geel')}>Geel</SelectItem>
                <SelectItem value="Blauw" className={getShiftTextColor('Blauw')}>Blauw</SelectItem>
                <SelectItem value="Groen" className={getShiftTextColor('Groen')}>Groen</SelectItem>
                <SelectItem value="Rood" className={getShiftTextColor('Rood')}>Rood</SelectItem>
                <SelectItem value="Wit" className={getShiftTextColor('Wit')}>Wit</SelectItem>
                <SelectItem value="Onbekend" className={getShiftTextColor('Onbekend')}>Onbekend</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      {/* --- Log Entry List --- */}
      <div className="space-y-3">
        {filteredHistoryEntries.length === 0 ? (
          <p className="text-center text-gray-500 py-4">Geen historie meldingen gevonden.</p>
        ) : (
          filteredHistoryEntries.map((entry) => (
            <div key={entry.id} className="p-3 border rounded shadow-sm bg-white opacity-80"> {/* Slightly faded */}
              <div className="flex justify-between items-center text-xs text-gray-500 mb-1">
                <div className="flex items-center gap-2">
                  <span>{format(parseISO(entry.timestamp), 'd MMM yyyy, HH:mm', { locale: nl })}</span>
                  {entry.reporter_name && (
                    <span className="flex items-center gap-1 bg-blue-50 text-blue-700 px-2 py-0.5 rounded-full">
                      <User className="h-3 w-3" />
                      {entry.reporter_name}
                    </span>
                  )}
                </div>
                <div className="flex items-center gap-2">
                   <span className={`px-1.5 py-0.5 rounded text-xs font-medium ${getShiftBgColor(entry.shiftColor)} ${getShiftTextColor(entry.shiftColor)}`}>
                       {entry.shiftColor}
                   </span>
                   <span className="font-semibold">{entry.line.toUpperCase()}</span>
                </div>
              </div>
              <p className="text-gray-800 whitespace-pre-wrap mb-1">{entry.text}</p>
              {entry.equipment && (
                <p className="text-xs text-gray-500">
                  Onderdeel: {entry.equipment === '__OTHER__' ? 'Overige' : filterEquipmentOptions.find(opt => opt.value === entry.equipment)?.label || entry.equipment}
                </p>
              )}
              {/* No Edit button in history view */}
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default TdLogbookHistoryPage;