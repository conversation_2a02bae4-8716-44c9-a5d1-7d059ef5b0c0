// src/types/screen-orientation.d.ts

// Extend the existing ScreenOrientation interface to include lock and unlock methods
// See: https://developer.mozilla.org/en-US/docs/Web/API/ScreenOrientation
interface ScreenOrientation extends EventTarget {
  readonly angle: number;
  readonly type: OrientationType;
  onchange: ((this: ScreenOrientation, ev: Event) => any) | null;

  // Add the missing methods
  lock(orientation: OrientationLockType): Promise<void>;
  unlock(): void;
}

// Define the possible lock types if not already available
type OrientationLockType =
  | "any"
  | "natural"
  | "landscape"
  | "portrait"
  | "portrait-primary"
  | "portrait-secondary"
  | "landscape-primary"
  | "landscape-secondary";

// Define the possible orientation types if not already available
type OrientationType =
  | "portrait-primary"
  | "portrait-secondary"
  | "landscape-primary"
  | "landscape-secondary";