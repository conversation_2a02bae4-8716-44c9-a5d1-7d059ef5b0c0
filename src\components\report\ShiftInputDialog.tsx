import React, { useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import ShiftForm, { ShiftFormProps } from './ShiftForm'; // Hergebruik ShiftForm
import { ProductionShiftData, ProductionLine } from '@/types'; // ShiftData vervangen door ProductionShiftData
import { format } from 'date-fns';
import { nl } from 'date-fns/locale';
import { createInitialShiftData } from '@/hooks/report/useReportForm'; // Importeer helper

interface ShiftInputDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: { od: ProductionShiftData; md: ProductionShiftData; nd: ProductionShiftData }) => void; // ShiftData vervangen
  selectedDate: Date | null;
  selectedLine: ProductionLine;
  materialOptions: string[]; 
  target?: number; // Optioneel: dagtotaal target
  initialData?: { od: ProductionShiftData; md: ProductionShiftData; nd: ProductionShiftData } | null; // ShiftData vervangen
}

const ShiftInputDialog: React.FC<ShiftInputDialogProps> = ({
  open,
  onOpenChange,
  onSubmit,
  selectedDate,
  selectedLine,
  materialOptions,
  target = 0,
  initialData = null // Standaard waarde
}) => {
  const [odData, setOdData] = useState<ProductionShiftData>(createInitialShiftData()); // ShiftData vervangen
  const [mdData, setMdData] = useState<ProductionShiftData>(createInitialShiftData()); // ShiftData vervangen
  const [ndData, setNdData] = useState<ProductionShiftData>(createInitialShiftData()); // ShiftData vervangen

  // Reset/vul state when dialog opens 
  useEffect(() => {
    if (open) {
      const shiftTarget = target > 0 ? parseFloat((target / 3).toFixed(1)) : 0;
      // Gebruik initialData indien beschikbaar, maar bereken target altijd opnieuw
      setOdData({ ...(initialData?.od || createInitialShiftData()), target: shiftTarget });
      setMdData({ ...(initialData?.md || createInitialShiftData()), target: shiftTarget });
      setNdData({ ...(initialData?.nd || createInitialShiftData()), target: shiftTarget });
    } else {
      // Reset state bij sluiten
       setOdData(createInitialShiftData());
       setMdData(createInitialShiftData());
       setNdData(createInitialShiftData());
    }
  }, [open, selectedDate, target, initialData]);

  const handleShiftChange = (
    shift: 'od' | 'md' | 'nd',
    field: string,
    value: string | number | boolean
  ) => {
    const setter = shift === 'od' ? setOdData : shift === 'md' ? setMdData : setNdData;
    setter(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSubmit = () => {
    // Basis validatie: Check of productie is ingevuld?
    if (!odData.production && !mdData.production && !ndData.production) {
      // Toon melding? Of sta toe om lege dag op te slaan?
      console.warn("Geen productie ingevuld voor alle shifts.");
    }
    onSubmit({ od: odData, md: mdData, nd: ndData });
    onOpenChange(false); // Sluit dialog na submit
  };

  const formattedDate = selectedDate ? format(selectedDate, 'eeee d MMMM yyyy', { locale: nl }) : '';

  // Format function now specifically handles yield display with comma
  const formatForDisplay = (value: string | number | undefined): string => {
    if (value === undefined || value === null) return '';
    // Convert number or string-with-dot to string-with-comma
    return String(value).replace('.', ',');
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl" onOpenAutoFocus={(e) => e.preventDefault()}>
        <DialogHeader>
          <DialogTitle>Shift Gegevens Invoeren</DialogTitle>
          <DialogDescription>
            Voer de productiegegevens in voor {selectedLine.toUpperCase()} op {formattedDate}.
          </DialogDescription>
        </DialogHeader>
        <div className="grid grid-cols-1 gap-6 py-4 max-h-[70vh] overflow-y-auto px-2">
          <ShiftForm
            shift="od"
            shiftLabel="Ochtenddienst"
            shiftData={odData}
            materialOptions={materialOptions}
            isDisabled={false}
            onShiftChange={handleShiftChange}
            formatForDisplay={formatForDisplay} 
            onPasteProduction={() => {}}
          />
          <ShiftForm
            shift="md"
            shiftLabel="Middagdienst"
            shiftData={mdData}
            materialOptions={materialOptions}
            isDisabled={false}
            onShiftChange={handleShiftChange}
            formatForDisplay={formatForDisplay}
            onPasteProduction={() => {}}
          />
          <ShiftForm
            shift="nd"
            shiftLabel="Nachtdienst"
            shiftData={ndData}
            materialOptions={materialOptions}
            isDisabled={false}
            onShiftChange={handleShiftChange}
            formatForDisplay={formatForDisplay}
            onPasteProduction={() => {}}
          />
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>Annuleren</Button>
          <Button onClick={handleSubmit}>Opslaan</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ShiftInputDialog; 