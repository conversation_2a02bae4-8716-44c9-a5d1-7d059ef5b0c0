-- Migration script to update the RLS policies for the todos table

-- Drop existing policies
DROP POLICY IF EXISTS "Allow select for authenticated users" ON public.todos;
DROP POLICY IF EXISTS "Allow insert for authenticated users" ON public.todos;
DROP POLICY IF EXISTS "Allow update for creator or team member" ON public.todos;
DROP POLICY IF EXISTS "Allow delete for creator" ON public.todos;

-- Create new policies for team-based permissions

-- Policy: Allow managers to view all todos
CREATE POLICY "Allow managers to view all todos" ON public.todos
FOR SELECT
USING (EXISTS (SELECT 1 FROM authorized_managers WHERE authorized_managers.user_id = auth.uid()));

-- Policy: Allow users to view todos assigned to their team
CREATE POLICY "Allow users to view todos for their team" ON public.todos
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM user_teams
    WHERE user_teams.user_id = auth.uid()
    AND user_teams.team = ANY(assigned_teams)
  )
);

-- Policy: Allow managers to insert new todos
CREATE POLICY "Allow managers to insert todos" ON public.todos
FOR INSERT
WITH CHECK (EXISTS (SELECT 1 FROM authorized_managers WHERE authorized_managers.user_id = auth.uid()));

-- Policy: Allow managers to update any todo
CREATE POLICY "Allow managers to update any todo" ON public.todos
FOR UPDATE
USING (EXISTS (SELECT 1 FROM authorized_managers WHERE authorized_managers.user_id = auth.uid()));

-- Policy: Allow team members to update completion status of todos assigned to their team
CREATE POLICY "Allow team members to update completion status" ON public.todos
FOR UPDATE
USING (
  EXISTS (
    SELECT 1 FROM user_teams
    WHERE user_teams.user_id = auth.uid()
    AND user_teams.team = ANY(assigned_teams)
  )
)
WITH CHECK (
  -- Only allow updating completion status fields
  (OLD.title = NEW.title) AND
  (OLD.description = NEW.description) AND
  (OLD.priority = NEW.priority) AND
  (OLD.assigned_teams = NEW.assigned_teams) AND
  (OLD.due_date = NEW.due_date) AND
  (OLD.created_by = NEW.created_by)
  -- completed, completed_at, and completed_by can be changed
);

-- Policy: Allow managers to delete any todo
CREATE POLICY "Allow managers to delete any todo" ON public.todos
FOR DELETE
USING (EXISTS (SELECT 1 FROM authorized_managers WHERE authorized_managers.user_id = auth.uid()));

-- Add index on assigned_teams if it doesn't exist yet
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_indexes 
    WHERE indexname = 'idx_todos_assigned_teams'
  ) THEN
    CREATE INDEX idx_todos_assigned_teams ON public.todos USING GIN (assigned_teams);
  END IF;
END $$;
