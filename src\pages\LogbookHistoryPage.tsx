import React, { useState, useMemo } from 'react';
import { useProduction } from '@/context/ProductionContext';
import { LogbookEntry, LogbookPriority } from '@/types'; // Removed ProductionLine
import { DateRange } from 'react-day-picker'; // Import DateRange
// import LineSelector from '@/components/common/LineSelector'; // Removed LineSelector import
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { format } from 'date-fns';
import { nl } from 'date-fns/locale';
import { Calendar as CalendarIcon } from 'lucide-react';
import { cn } from '@/lib/utils'; // Import cn utility
import { Calendar } from '@/components/ui/calendar'; // Import Calendar
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'; // Import Popover components
import { Combobox } from '@/components/ui/combobox'; // Import Combobox

// Define predefined locations for filtering (can be shared or redefined)
const locations = [
  { value: 'tl1', label: 'TL1' },
  { value: 'tl2', label: 'TL2' },
  { value: 'p1', label: 'P1' },
  { value: 'p2', label: 'P2' },
  { value: 'p3', label: 'P3' },
  { value: 'koeling', label: 'Koeling' },
  { value: 'yard', label: 'Yard' },
  { value: 'overige', label: 'Overige' },
];

const LogbookHistoryPage: React.FC = () => {
  const { logbookHistory } = useProduction(); // Fetch logbookHistory instead of logbookEntries
  // const [selectedLine, setSelectedLine] = useState<ProductionLine>('tl1'); // Removed line selection state
  const [searchTerm, setSearchTerm] = useState('');
  const [dateRange, setDateRange] = useState<DateRange | undefined>(); // Date range state
  const [locationFilter, setLocationFilter] = useState(''); // State for location filter

  // Filter logic (basic keyword search for now)
  const filteredEntries = useMemo(() => {
    let entries = logbookHistory || []; // Use logbookHistory directly

    // Implement date range filtering
    if (dateRange?.from) {
      entries = entries.filter(entry => new Date(entry.timestamp) >= dateRange.from!);
    }
    if (dateRange?.to) {
      // Add 1 day to 'to' date to include the selected end date
      const toDate = new Date(dateRange.to);
      toDate.setDate(toDate.getDate() + 1);
      entries = entries.filter(entry => new Date(entry.timestamp) < toDate);
    }

    // Add location filtering
    if (locationFilter) {
      const lowerLocationFilter = locationFilter.toLowerCase();
      entries = entries.filter(entry =>
        entry.location?.toLowerCase().includes(lowerLocationFilter) // Check if location includes filter term
      );
    }

    if (searchTerm) {
      const lowerSearchTerm = searchTerm.toLowerCase();
      entries = entries.filter(entry =>
        entry.text.toLowerCase().includes(lowerSearchTerm) || // Keep text search
        entry.priority.toLowerCase().includes(lowerSearchTerm) // Keep priority search (Removed duplicate line)
      );
    }
    return entries; // Already sorted by timestamp in the hook
  }, [logbookHistory, searchTerm, dateRange, locationFilter]); // Add locationFilter dependency

  const getPriorityBadgeVariant = (priority: LogbookPriority): "default" | "secondary" | "destructive" | "outline" | null | undefined => {
     switch (priority) {
      case 'Hoog': return 'destructive';
      case 'Middel': return 'secondary';
      case 'Laag': return 'outline';
      default: return 'default';
    }
  };


  return (
    <div className="animate-fade-in space-y-6">
       <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-2">
         <h1 className="text-2xl md:text-3xl font-semibold text-faerch-blue mb-4 md:mb-0">
           Logboek Historie
         </h1>
        {/* <LineSelector selectedLine={selectedLine} onChange={setSelectedLine} /> */} {/* Removed LineSelector */}
       </div>

       {/* Filters */}
       <div className="flex flex-col md:flex-row gap-4 mb-4 p-4 border rounded-md bg-card shadow-sm">
          <Input
            placeholder="Zoek op woord..."
            value={searchTerm} // Corrected: was value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="max-w-xs" // Adjusted width
          />
           {/* Location Filter Combobox */}
           <Combobox
             options={locations}
             value={locationFilter}
             onValueChange={setLocationFilter}
             placeholder="Filter op locatie..."
             searchPlaceholder="Zoek/typ locatie..."
             notFoundMessage="Geen locatie gevonden."
             // Apply similar width styling if needed via wrapper div
             // className="w-full sm:w-[180px]"
           />
          {/* Date Range Picker Implementation */}
          <Popover>
            <PopoverTrigger asChild>
              <Button
                id="date"
                variant={"outline"}
                className={cn(
                  "w-full sm:w-[260px] justify-start text-left font-normal", // Adjusted width
                  !dateRange && "text-muted-foreground"
                )}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {dateRange?.from ? (
                  dateRange.to ? (<> {format(dateRange.from, "LLL dd, y", { locale: nl })} - {format(dateRange.to, "LLL dd, y", { locale: nl })} </>) : (format(dateRange.from, "LLL dd, y", { locale: nl }))
                ) : ( <span>Kies een datum range</span> )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar initialFocus mode="range" defaultMonth={dateRange?.from} selected={dateRange} onSelect={setDateRange} numberOfMonths={2} locale={nl} />
            </PopoverContent>
          </Popover>
          <Button variant="outline" onClick={() => { setSearchTerm(''); setDateRange(undefined); setLocationFilter(''); }}>Reset Filters</Button> {/* Add location reset */}
       </div>

       {/* History List */}
       <div className="dashboard-card p-4">
         <div className="space-y-3 max-h-[65vh] overflow-y-auto pr-2">
           {filteredEntries.length === 0 && (
             <p className="text-sm text-gray-500 italic text-center py-4">Geen logboek items gevonden voor de geselecteerde filters.</p>
           )}
           {filteredEntries.map((entry) => (
             <div key={entry.id} className="text-sm border-b pb-2 last:border-b-0 flex justify-between items-start gap-2">
                <div className="flex-grow">
                    <div className="flex justify-between items-start mb-1">
                    <Badge variant={getPriorityBadgeVariant(entry.priority)} className="text-xs px-1.5 py-0.5">
                        {entry.priority}
                    </Badge>
                    <span className="text-xs text-gray-400 flex-shrink-0 ml-2">
                        {format(new Date(entry.timestamp), 'dd-MM-yyyy HH:mm', { locale: nl })}
                    </span>
                  </div>
                  {/* Display Location */}
                  <p className="text-xs text-gray-500 mb-1">Locatie: {entry.location || 'N/A'}</p>
                  <p className="text-gray-700 whitespace-pre-wrap">{entry.text}</p>
               </div>
                {/* Optional: Add view/edit/delete actions if needed */}
             </div>
           ))}
         </div>
       </div>
    </div>
  );
};

export default LogbookHistoryPage;