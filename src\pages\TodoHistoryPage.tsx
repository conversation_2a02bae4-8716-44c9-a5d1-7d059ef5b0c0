import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { TodoItem } from '@/types';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { format } from 'date-fns';
import { nl } from 'date-fns/locale';
import { toast } from 'sonner';
import { useAuth } from '@/context/AuthContext';
import { useUserTeam } from '@/hooks/use-user-team';
import { supabase } from '@/lib/supabase-client';
import { Calendar, CheckCircle, Clock, MessageSquare, ArrowLeft } from 'lucide-react';

// Team colors for badges
const teamColors: Record<string, string> = {
  'Blauw': 'bg-blue-100 text-blue-800 border-blue-200',
  'Geel': 'bg-yellow-100 text-yellow-800 border-yellow-200',
  'Groen': 'bg-green-100 text-green-800 border-green-200',
  'Rood': 'bg-red-100 text-red-800 border-red-200',
  'Wit': 'bg-gray-100 text-gray-800 border-gray-200',
};

// Priority colors
const priorityColors: Record<string, string> = {
  'low': 'bg-green-100 text-green-800',
  'medium': 'bg-yellow-100 text-yellow-800',
  'high': 'bg-red-100 text-red-800',
};

const priorityLabels: Record<string, string> = {
  'low': 'Laag',
  'medium': 'Middel',
  'high': 'Hoog',
};

const TodoHistoryPage: React.FC = () => {
  const { user } = useAuth();
  const { team, isManager } = useUserTeam();
  const [completedTodos, setCompletedTodos] = useState<TodoItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Helper function to get user email from user ID
  const getUserEmail = async (userId: string | null): Promise<string> => {
    if (!userId) return 'Onbekend';

    try {
      // Try to get user from auth_users_view (which should have email)
      const { data, error } = await supabase
        .from('auth_users_view')
        .select('email')
        .eq('id', userId)
        .maybeSingle();

      if (error) {
        console.error('Error fetching user email:', error);
        return userId; // Return the ID if we can't get the email
      }

      return data?.email || userId;
    } catch (error) {
      console.error('Unexpected error fetching user email:', error);
      return userId;
    }
  };

  // Fetch completed todos from Supabase
  const fetchCompletedTodos = async () => {
    setIsLoading(true);
    try {
      let query = supabase
        .from('todos')
        .select('*') // Select all columns
        .eq('completed', true) // Only show completed todos
        .order('completed_at', { ascending: false }); // Order by completion date, newest first

      // If not a manager, filter by team
      if (!isManager && team) {
        // Check if 'assigned_teams' array contains the user's team
        query = query.contains('assigned_teams', [team]);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching completed todos:', error);
        toast.error(`Fout bij ophalen van afgeronde taken: ${error.message}`);
        setCompletedTodos([]); // Set to empty array on error
        return;
      }

      // Fetch user emails for created_by and completed_by fields
      const todosWithUserInfo = await Promise.all(
        (data as TodoItem[]).map(async (todo) => {
          const [createdByEmail, completedByEmail] = await Promise.all([
            getUserEmail(todo.created_by),
            getUserEmail(todo.completed_by)
          ]);

          return {
            ...todo,
            created_by_name: createdByEmail,
            completed_by_name: completedByEmail
          };
        })
      );

      setCompletedTodos(todosWithUserInfo);
    } catch (error) {
      console.error('Unexpected error fetching completed todos:', error);
      toast.error('Onverwachte fout bij ophalen van afgeronde taken');
      setCompletedTodos([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Load completed todos on component mount
  useEffect(() => {
    fetchCompletedTodos();
  }, [team, isManager]);

  // Set up Realtime subscription for todos
  useEffect(() => {
    // Don't set up subscription if user isn't authenticated
    if (!user) return;

    console.log('Setting up Realtime subscription for todos history...');

    const channel = supabase
      .channel('todos_history_changes')
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'todos', filter: 'completed=eq.true' },
        (payload) => {
          console.log('Realtime todo history change received:', payload);

          // Refresh todos from the database to ensure we have the latest data
          fetchCompletedTodos();
        }
      )
      .subscribe((status, err) => {
        if (status === 'SUBSCRIBED') {
          console.log('Successfully subscribed to todos history changes!');
        } else if (status === 'CHANNEL_ERROR' || status === 'TIMED_OUT') {
          console.error(`Subscription error: ${status}`, err);
          toast.error('Realtime connection error');
        } else {
          console.log(`Subscription status: ${status}`);
        }
      });

    // Clean up subscription when component unmounts
    return () => {
      console.log('Cleaning up Realtime subscription for todos history...');
      supabase.removeChannel(channel);
    };
  }, [user]);

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Todo Geschiedenis</h1>
        <Link to="/todos">
          <Button variant="outline" size="sm" className="flex items-center gap-2">
            <ArrowLeft className="h-4 w-4" />
            Terug naar Todo Lijst
          </Button>
        </Link>
      </div>

      <div className="bg-white rounded-lg shadow">
        <div className="p-4 border-b">
          <div className="flex justify-between items-center">
            <h2 className="text-lg font-medium">Afgeronde Taken</h2>
            <div className="text-sm text-gray-500">
              Totaal: {completedTodos.length} afgeronde taken
            </div>
          </div>
        </div>

        <div className="p-4">
          {isLoading ? (
            <div className="flex justify-center items-center h-32">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
            </div>
          ) : completedTodos.length > 0 ? (
            <div className="space-y-4">
              {completedTodos.map(todo => (
                <div
                  key={todo.id}
                  className="p-4 border rounded-md bg-gray-50"
                >
                  <div className="flex items-start gap-4">
                    <div className="flex-grow">
                      <div className="flex justify-between items-start">
                        <h3 className="font-medium line-through text-gray-500">{todo.title}</h3>
                        <Badge className={priorityColors[todo.priority]}>
                          {priorityLabels[todo.priority]}
                        </Badge>
                      </div>

                      {todo.description && (
                        <p className="text-sm text-gray-600 mt-2 mb-3">
                          {todo.description}
                        </p>
                      )}

                      {todo.comments && (
                        <div className="mt-2 p-3 bg-blue-50 rounded-md border border-blue-100">
                          <div className="flex items-center text-sm text-blue-700 mb-1">
                            <MessageSquare className="h-4 w-4 mr-1" />
                            <span className="font-medium">Opmerking bij afronding:</span>
                          </div>
                          <p className="text-sm text-gray-700">{todo.comments}</p>
                        </div>
                      )}

                      <div className="flex flex-wrap gap-2 mt-3">
                        {todo.assigned_teams.map(teamName => (
                          <Badge
                            key={teamName}
                            variant="outline"
                            className={teamColors[teamName]}
                          >
                            {teamName}
                          </Badge>
                        ))}
                      </div>

                      <div className="flex flex-wrap items-center gap-4 mt-3 text-xs text-gray-500">
                        <div className="flex items-center">
                          <Calendar className="h-3 w-3 mr-1" />
                          Aangemaakt: {format(new Date(todo.created_at), 'dd-MM-yyyy HH:mm', { locale: nl })}
                        </div>

                        {todo.due_date && (
                          <div className="flex items-center">
                            <Clock className="h-3 w-3 mr-1" />
                            Deadline: {format(new Date(todo.due_date), 'dd-MM-yyyy', { locale: nl })}
                          </div>
                        )}

                        <div>
                          Door: {todo.created_by_name || todo.created_by}
                        </div>

                        {todo.completed_at && (
                          <div className="flex items-center text-green-600">
                            <CheckCircle className="h-3 w-3 mr-1" />
                            Afgerond op: {format(new Date(todo.completed_at), 'dd-MM-yyyy HH:mm', { locale: nl })}
                          </div>
                        )}

                        {todo.completed_by && (
                          <div className="text-green-600">
                            Afgerond door: {todo.completed_by_name || todo.completed_by}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              Geen afgeronde taken gevonden
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TodoHistoryPage;
