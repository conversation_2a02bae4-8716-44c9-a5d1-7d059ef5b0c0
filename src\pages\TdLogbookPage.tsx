import React, { useState, useMemo, useEffect } from 'react';
import { useIsMobile } from '@/hooks/use-mobile'; // Import useIsMobile
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import TextareaAutosize from 'react-textarea-autosize';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { ChevronsUpDown, Check as CheckIcon, Search, X, History, Pencil, Save, Trash2, UserPlus } from 'lucide-react';
import { Link } from 'react-router-dom';
// Removed AlertDialog imports
import {
  DropdownMenu, // Import Dropdown components
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog, // Import Dialog components
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
  DialogClose,
  DialogDescription, // Added DialogDescription
} from "@/components/ui/dialog";
import { useProduction } from '@/context/ProductionContext';
import { ProductionLine, TdLogEntryInput, EquipmentEntry, ShiftColor, TdLogEntry, TdReporterName } from '@/types'; // Added TdReporterName
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import { format, parseISO } from 'date-fns';
import { nl } from 'date-fns/locale';
import { getShiftInfo } from '@/utils/shift-utils';
import EquipmentSelector from '@/components/common/EquipmentSelector'; // Import the component

// Initial state for the form
const initialFormState: TdLogEntryInput = {
  line: 'tl1',
  equipment: '',
  text: '',
  reporter_name: '', // Add reporter_name
};

// Helper functions for colors
const getShiftTextColor = (color: ShiftColor): string => {
    switch (color) {
        case 'Geel': return 'text-yellow-800';
        case 'Blauw': return 'text-blue-800';
        case 'Groen': return 'text-green-800';
        case 'Rood': return 'text-red-800';
        case 'Wit': return 'text-gray-800';
        default: return 'text-gray-500';
    }
};
const getShiftBgColor = (color: ShiftColor): string => {
    switch (color) {
        case 'Geel': return 'bg-yellow-100';
        case 'Blauw': return 'bg-blue-100';
        case 'Groen': return 'bg-green-100';
        case 'Rood': return 'bg-red-100';
        case 'Wit': return 'bg-gray-100';
        default: return 'bg-gray-100';
    }
};


const TdLogbookPage: React.FC = () => {
  const { tdLogEntries, addTdLogEntry, updateTdLogEntry, equipmentOptions, tdReporterNames, addTdReporterName, deleteTdReporterName } = useProduction(); // Added reporter name state/functions
  const isMobile = useIsMobile(); // Get mobile status

  // State for Inline Form, Editing, etc.
  const [formData, setFormData] = useState<TdLogEntryInput>(initialFormState);
  const [currentShiftInfo, setCurrentShiftInfo] = useState<{ shift: string; teamColor: ShiftColor } | null>(null);
  const [previousShiftColor, setPreviousShiftColor] = useState<ShiftColor | null>(null); // State for previous shift
  const [editingEntryId, setEditingEntryId] = useState<string | null>(null);
  const [editFormData, setEditFormData] = useState<TdLogEntryInput | null>(null);

  // --- Filter State ---
  const [filterText, setFilterText] = useState('');
  const [filterEquipment, setFilterEquipment] = useState(''); // Holds the value of the selected equipment filter
  const [filterLine, setFilterLine] = useState<ProductionLine | ''>(''); // State for line filter
  const [filterReporterName, setFilterReporterName] = useState<string>('all'); // State for reporter name filter ('all' for 'Alle Melders', or specific name)

  // State for Add/Delete Name Dialogs
  const [addNameDialogOpen, setAddNameDialogOpen] = useState(false);
  const [newReporterName, setNewReporterName] = useState('');
  const [reporterPendingDeleteId, setReporterPendingDeleteId] = useState<string | null>(null); // State for inline delete confirmation

  // --- Effects ---
  useEffect(() => {
    const updateShift = () => {
      const newShiftInfo = getShiftInfo(new Date().toISOString());
      setCurrentShiftInfo(newShiftInfo);

      // Check for shift change and reset reporter name if needed
      if (newShiftInfo && previousShiftColor && newShiftInfo.teamColor !== previousShiftColor) {
        setFormData(prev => ({ ...prev, reporter_name: '' })); // Reset reporter name
        toast.info(`Nieuwe ploeg (${newShiftInfo.teamColor}). Selecteer melder opnieuw.`);
      }
      // Update previous shift color
      if (newShiftInfo) {
          setPreviousShiftColor(newShiftInfo.teamColor);
      }
    };
    updateShift(); // Initial call
    const intervalId = setInterval(updateShift, 60000); // Check every minute
    return () => clearInterval(intervalId);
  }, [previousShiftColor]); // Add previousShiftColor to dependency array


  // --- Memoized Options ---
  const formEquipmentOptions = useMemo(() => {
    const lineOptions = equipmentOptions[formData.line];
    if (!lineOptions) return [];
    return Object.values(lineOptions).flat().sort((a, b) => a.label_nl.localeCompare(b.label_nl));
  }, [formData.line, equipmentOptions]);

  const editEquipmentOptions = useMemo(() => {
    if (!editFormData) return [];
    const lineOptions = equipmentOptions[editFormData.line];
    if (!lineOptions) return [];
    return Object.values(lineOptions).flat().sort((a, b) => a.label_nl.localeCompare(b.label_nl));
  }, [editFormData, equipmentOptions]);

  // Prepare the actual EquipmentEntry objects for the selector prop
  const allEquipmentEntriesFlat = useMemo(() => {
      let allOptions: EquipmentEntry[] = [];
      Object.values(equipmentOptions).forEach(lineOpts => {
          if (lineOpts) {
              allOptions = allOptions.concat(Object.values(lineOpts).flat());
          }
      });
      // Return unique entries based on 'id' (which EquipmentSelector uses for value)
      return Array.from(new Map(allOptions.map(opt => [opt.id, opt])).values());
  }, [equipmentOptions]);

  // Prepare options specifically for display purposes (e.g., showing label for a selected value)
   const allEquipmentOptionsForDisplay = useMemo(() => {
       // Use the flattened unique entries to create the display map
       const uniqueOptions = allEquipmentEntriesFlat;
       // Create a map for quick lookup of label by id/value
       const displayMap = new Map(uniqueOptions.map(opt => [opt.id, `${opt.label_nl}${opt.value ? ` (${opt.value})` : ''}`]));
       // Add the "Alle Onderdelen" option for display if needed elsewhere, or just return the map
       // For now, just return the map for lookup purposes
       return displayMap;
   }, [allEquipmentEntriesFlat]);

   // Prepare equipment options specifically for the FILTER dropdown
   const filterEquipmentOptions = useMemo(() => {
       if (!filterLine) {
           // If no line is selected, show all unique equipment
           return allEquipmentEntriesFlat;
       }
       // If a line is selected, show only equipment for that line
       const lineOptions = equipmentOptions[filterLine];
       if (!lineOptions) return [];
       // Flatten and get unique equipment for the selected line
       const lineSpecificEntries = Object.values(lineOptions).flat();
       return Array.from(new Map(lineSpecificEntries.map(opt => [opt.id, opt])).values());
   }, [filterLine, equipmentOptions, allEquipmentEntriesFlat]);


  // --- Filtered Entries (Last 5 Days Only) ---
  const recentEntries = useMemo(() => {
    const fiveDaysAgo = new Date();
    fiveDaysAgo.setDate(fiveDaysAgo.getDate() - 5);
    fiveDaysAgo.setHours(0, 0, 0, 0);

    return tdLogEntries
      .filter(entry => {
        try {
          return parseISO(entry.timestamp) >= fiveDaysAgo;
        } catch (e) {
          console.error("Error parsing entry timestamp:", entry.timestamp, e);
          return false;
        }
      })
      .filter(entry => {
          // Apply text filter (case-insensitive)
          const textMatch = !filterText || entry.text.toLowerCase().includes(filterText.toLowerCase());
          // Apply equipment filter
          const equipmentMatch = !filterEquipment || (entry.equipment && entry.equipment.toLowerCase() === filterEquipment.toLowerCase());
          // Handle '__OTHER__' case if needed for filtering, assuming filterEquipment holds the actual value or '__OTHER__'
          const otherEquipmentMatch = filterEquipment === '__OTHER__' && entry.equipment === '__OTHER__';

          return textMatch && (equipmentMatch || otherEquipmentMatch);
      })
      .filter(entry => {
          // Apply line filter
          const lineMatch = !filterLine || entry.line === filterLine;
          return lineMatch;
      })
      .filter(entry => {
          // Apply reporter name filter
          const reporterMatch = !filterReporterName || filterReporterName === 'all' || (entry.reporter_name && entry.reporter_name === filterReporterName);
          return reporterMatch;
      })
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
 }, [tdLogEntries, filterText, filterEquipment, filterLine, filterReporterName]); // Add all filter states to dependencies


  // --- Event Handlers ---
  const handleFormInputChange = (field: keyof TdLogEntryInput, value: string | boolean | ProductionLine) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
    if (field === 'line') {
        setFormData(prev => ({ ...prev, equipment: '' }));
    }
  };

  const resetForm = () => {
    setFormData(initialFormState);
  };

  const handleInlineSubmit = async () => {
    if (!formData.text.trim() || !formData.line || !formData.reporter_name) {
        if (!formData.reporter_name) {
            toast.error("Selecteer a.u.b. een melder.");
        } else {
            toast.error("Vul a.u.b. de lijn en een melding in.");
        }
        return;
    }
    try {
      const dataToSubmit = {
          ...formData,
          equipment: formData.equipment || undefined
      }
      await addTdLogEntry(dataToSubmit);
      resetForm();
    } catch (error) {
      console.error("Error submitting TD Log Entry:", error);
    }
  };

  // Edit Handlers
  const handleEditClick = (entry: TdLogEntry) => {
    setEditingEntryId(entry.id);
    setEditFormData({
        line: entry.line,
        equipment: entry.equipment || '',
        text: entry.text,
    });
  };

  const handleCancelEdit = () => {
    setEditingEntryId(null);
    setEditFormData(null);
  };

  const handleEditInputChange = (field: keyof TdLogEntryInput, value: string | boolean | ProductionLine) => {
    setEditFormData(prev => prev ? { ...prev, [field]: value } : null);
     if (field === 'line' && editFormData) {
        setEditFormData(prev => prev ? ({ ...prev, equipment: '' }) : null);
    }
  };

  const handleEditSubmit = async () => {
    if (!editingEntryId || !editFormData || !editFormData.text.trim()) return;
    try {
        const updates: Partial<TdLogEntryInput> = {
            line: editFormData.line,
            equipment: editFormData.equipment || undefined,
            text: editFormData.text,
        };
      await updateTdLogEntry(editingEntryId, updates);
      handleCancelEdit();
    } catch (error) {
      console.error("Error updating TD Log Entry:", error);
    }
  };


  // --- JSX ---
  return (
    <div className="animate-fade-in p-4 md:p-6"> {/* Removed overflow-y-scroll */}
      {/* Header with History Link */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <h1 className="text-2xl md:text-3xl font-semibold text-faerch-blue mb-4 md:mb-0">
          TD Logboek (Laatste 5 Dagen)
        </h1>
        <Link to="/td-logboek/historie" className="text-sm text-blue-600 hover:underline inline-flex items-center gap-1">
          <History className="h-4 w-4" />
          Bekijk Historie
        </Link>
      </div>

      {/* --- Filter Row --- */}
      <div className="flex flex-col md:flex-row gap-3 mb-4 p-3 border rounded-lg bg-gray-50 shadow-sm items-end">
        {/* Text Filter */}
        {/* Added max-w-md to constrain the width */}
        <div className="flex-grow max-w-[300px]">
          {/* Reduced width by 100px from max-w-md (which is 28rem or ~448px) */}
          <Label htmlFor="filter-text" className="text-xs font-medium text-gray-600">Zoek in melding</Label>
          <div className="relative">
             <Search className="absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
             <Input
               id="filter-text"
               placeholder="Zoek..."
               value={filterText}
               onChange={(e) => setFilterText(e.target.value)}
               className="h-9 pl-8 text-sm" // Adjusted padding and height
             />
             {filterText && (
                <Button variant="ghost" size="icon" className="absolute right-1 top-1/2 h-6 w-6 -translate-y-1/2 text-gray-400 hover:text-gray-600" onClick={() => setFilterText('')}>
                   <X className="h-4 w-4" />
                </Button>
             )}
          </div>
        </div>
        {/* Equipment Filter */}
        <div className="w-full md:w-[300px]">
           <Label htmlFor="filter-equipment" className="text-xs font-medium text-gray-600">Filter op onderdeel</Label>
           {/* Use the common EquipmentSelector component */}
           <EquipmentSelector
               id="filter-equipment"
               value={filterEquipment}
               onChange={setFilterEquipment}
               equipmentOptions={filterEquipmentOptions} // Pass the dynamically filtered options
               placeholder="Alle Onderdelen"
               className="h-9 text-sm" // Adjusted height
           />
        </div>
        {/* Name Filter */}
        <div className="w-full md:w-[170px]">
            <Label htmlFor="filter-reporter" className="text-xs font-medium text-gray-600">Filter op naam</Label>
            <Select value={filterReporterName} onValueChange={(value) => {
                setFilterReporterName(value);
            }}>
                <SelectTrigger id="filter-reporter" className="h-9 text-sm">
                    <SelectValue placeholder="Alle Melders" />
                </SelectTrigger>
                <SelectContent className="max-h-[200px] overflow-y-auto">
                    <SelectItem value="all">Alle Melders</SelectItem>
                    {tdReporterNames.map((reporter) => (
                        <SelectItem key={reporter.id} value={reporter.name}>{reporter.name}</SelectItem>
                    ))}
                </SelectContent>
            </Select>
        </div>

        {/* Line Filter */}
        <div className="w-full md:w-[170px]"> {/* Increased width by 100px */}
            <Label htmlFor="filter-line" className="text-xs font-medium text-gray-600">Filter op lijn</Label>
            <Select value={filterLine} onValueChange={(value) => {
                const newLine = value as ProductionLine | '';
                setFilterLine(newLine);
                // Reset equipment filter when line changes
                setFilterEquipment('');
            }}>
                <SelectTrigger id="filter-line" className="h-9 text-sm">
                    <SelectValue placeholder="Alle Lijnen" />
                </SelectTrigger>
                <SelectContent className="max-h-[200px] overflow-y-auto"> {/* Constrain height and add scroll */}
                    <SelectItem value="tl1">TL1</SelectItem>
                    <SelectItem value="tl2">TL2</SelectItem>
                    <SelectItem value="p1">P1</SelectItem>
                    <SelectItem value="p2">P2</SelectItem>
                    <SelectItem value="p3">P3</SelectItem>
                    <SelectItem value="Yard">Yard</SelectItem>
                    <SelectItem value="Koeling">Koeling</SelectItem>
                    <SelectItem value="Gebouw">Gebouw</SelectItem>
                    <SelectItem value="Overige">Overige</SelectItem>
                </SelectContent>
            </Select>
        </div>
        {/* Clear All Filters Button */}
        <div className="w-[110px] flex items-end">
            <Button
                variant="outline"
                size="sm"
                className={cn(
                    "h-9 w-full transition-opacity duration-200",
                    (filterText || filterEquipment || filterLine || filterReporterName) // Added reporter filter
                        ? "opacity-100 pointer-events-auto"
                        : "opacity-0 pointer-events-none"
                )}
                onClick={() => { setFilterText(''); setFilterEquipment(''); setFilterLine(''); setFilterReporterName('all'); }} // Added reporter filter reset
                aria-hidden={!(filterText || filterEquipment || filterLine || filterReporterName)}
                tabIndex={(filterText || filterEquipment || filterLine || filterReporterName) ? 0 : -1}
            >
                Reset Filters
            </Button>
        </div>
      </div>

      {/* --- Inline Entry Row --- */}
      {!editingEntryId && (
        <div className="mb-3 p-3 border border-blue-200 rounded-lg bg-blue-50 shadow-sm">
          {/* First row of inputs */}
          <div className="grid grid-cols-1 lg:grid-cols-5 gap-4 items-center"> {/* Revert to 5 equal columns */}
            {/* Date/Time/Shift Info */}
            {/* Date/Time/Shift Info */}
            <div className="space-y-1 text-xs text-gray-600 pt-1 h-8">
              {!isMobile && (formData.text || formData.equipment) && currentShiftInfo && (
                 <>
                   <div>{format(new Date(), 'd MMM, HH:mm', { locale: nl })}</div>
                   <div className={`font-medium ${getShiftTextColor(currentShiftInfo.teamColor)}`}>
                       Ploeg: {currentShiftInfo.teamColor} ({currentShiftInfo.shift})
                    </div>
                 </>
               )}
            </div>
            {/* Line Selection */}
            <div className="space-y-1"> {/* Removed w-[200px] */}
              <Label htmlFor="inline-log-line" className="sr-only">Lijn</Label>
              <Select value={formData.line} onValueChange={(value) => handleFormInputChange('line', value as ProductionLine)} required>
                <SelectTrigger id="inline-log-line" className="h-8 text-xs w-[125px]"><SelectValue placeholder="Lijn" /></SelectTrigger>
                <SelectContent>
                  <SelectItem value="tl1">TL1</SelectItem>
                  <SelectItem value="tl2">TL2</SelectItem>
                  <SelectItem value="p1">P1</SelectItem>
                  <SelectItem value="p2">P2</SelectItem>
                  <SelectItem value="p3">P3</SelectItem>
                  <SelectItem value="Yard">Yard</SelectItem>
                  <SelectItem value="Koeling">Koeling</SelectItem>
                  <SelectItem value="Gebouw">Gebouw</SelectItem>
                  <SelectItem value="Overige">Overige</SelectItem>
                </SelectContent>
              </Select>
            </div>
            {/* Equipment Selection */}
            <div className="space-y-1">
               <Label htmlFor="inline-log-equipment" className="sr-only">Onderdeel</Label>
               <EquipmentSelector
                   id="inline-log-equipment"
                   value={formData.equipment}
                   onChange={(value) => handleFormInputChange('equipment', value)}
                   equipmentOptions={formEquipmentOptions}
                   placeholder="Onderdeel..."
                   className="h-8 text-xs w-[200px]"
               />
            </div>
            {/* Reporter Name Selection (Inline Form) */}
            <div className="space-y-1">
              <Label htmlFor="inline-log-reporter" className="sr-only">Melder</Label>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" className="h-8 text-xs w-[75px] justify-between font-normal">
                    <span>{formData.reporter_name || 'Selecteer Melder'}</span>
                    <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-[--radix-dropdown-menu-trigger-width]">
                  <DropdownMenuItem onSelect={() => handleFormInputChange('reporter_name', '')}>
                    (Geen)
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuLabel>Beschikbare Melders</DropdownMenuLabel>
                  {tdReporterNames.map((reporter) => (
                    <DropdownMenuItem
                      key={reporter.id}
                      className="flex justify-between items-center"
                      onSelect={() => handleFormInputChange('reporter_name', reporter.name)}
                    >
                      <span className="flex-grow">
                        {reporter.name}
                      </span>
                    </DropdownMenuItem>
                  ))}
                  <DropdownMenuSeparator />
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
            {/* Add Reporter Button */}
            {/* Add Reporter Button - Moved to end */}
            <div className="pt-1"> {/* Removed ml-auto */}
              <Dialog open={addNameDialogOpen} onOpenChange={setAddNameDialogOpen}>
                <DialogTrigger asChild>
                  <Button type="button" variant="ghost" size="icon" className="h-8 w-8 text-blue-600 hover:bg-blue-100" aria-label="Nieuwe melder toevoegen">
                    <UserPlus className="h-4 w-4" />
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-[425px] z-[9999]">
                  <DialogHeader>
                    <DialogTitle>Nieuwe Melder Toevoegen</DialogTitle>
                  </DialogHeader>
                  <div className="grid gap-4 py-4">
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor="new-reporter-name" className="text-right">
                        Naam
                      </Label>
                      <Input
                        id="new-reporter-name"
                        value={newReporterName}
                        onChange={(e) => setNewReporterName(e.target.value)}
                        className="col-span-3"
                        placeholder="Volledige naam"
                      />
                    </div>
                  </div>
                  {/* Inline delete section added here */}
                  <div className="mt-4 pt-4 border-t">
                    <h3 className="mb-2 text-sm font-medium text-muted-foreground">Bestaande Melders</h3>
                    <div className="max-h-48 overflow-y-auto space-y-1 pr-2">
                      {tdReporterNames.length === 0 ? (
                        <p className="text-xs text-muted-foreground italic">Geen bestaande melders.</p>
                      ) : (
                        tdReporterNames.map((reporter) => (
                          <div key={reporter.id} className="flex items-center justify-between text-sm p-1 rounded group">
                            <span>{reporter.name}</span>
                            {reporterPendingDeleteId === reporter.id ? (
                              <div className="flex items-center space-x-1">
                                <span className="text-xs text-muted-foreground italic mr-1">Sure?</span>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-6 w-6 text-red-600 hover:bg-red-100"
                                  onClick={async () => {
                                    await deleteTdReporterName(reporter.id);
                                    setReporterPendingDeleteId(null);
                                  }}
                                  aria-label={`Bevestig verwijderen ${reporter.name}`}
                                >
                                  <CheckIcon className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-6 w-6 hover:bg-accent"
                                  onClick={() => setReporterPendingDeleteId(null)}
                                  aria-label="Annuleer verwijderen"
                                >
                                  <X className="h-4 w-4" />
                                </Button>
                              </div>
                            ) : (
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-6 w-6 text-red-500 hover:bg-red-100 opacity-0 group-hover:opacity-100 transition-opacity"
                                onClick={() => {
                                  setReporterPendingDeleteId(reporter.id);
                                }}
                                aria-label={`Verwijder ${reporter.name}`}
                              >
                                <Trash2 className="h-3.5 w-3.5" />
                              </Button>
                            )}
                          </div>
                        ))
                      )}
                    </div>
                  </div>
                  <DialogFooter>
                    <Button type="button" variant="outline" onClick={() => setAddNameDialogOpen(false)}>Annuleren</Button>
                    <Button type="button" onClick={async () => {
                      const success = await addTdReporterName(newReporterName);
                      if (success) {
                        setNewReporterName('');
                        setAddNameDialogOpen(false);
                      }
                    }}>Opslaan</Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
            {/* Line Selection */}
            {/* Equipment Selection */}
          </div>
          {/* New row for Text Input and Submit Button */}
          <div className="flex items-end gap-3 mt-2"> {/* Use flex, items-end, add gap and top margin */}
            {/* Text Input */}
            <div className="flex-grow space-y-1"> {/* Allow textarea to grow */}
               <Label htmlFor="inline-log-text" className="sr-only">Melding</Label>
               <TextareaAutosize
                 id="inline-log-text"
                 placeholder="Nieuwe melding... (Shift+Enter to save)"
                 value={formData.text}
                 onChange={(e) => handleFormInputChange('text', e.target.value)}
                 required
                 minRows={1}
                 className="w-full text-sm px-2 py-1.5 border rounded resize-none focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
                 onKeyDown={(event: React.KeyboardEvent<HTMLTextAreaElement>) => {
                   if (event.key === 'Enter') {
                     if (event.shiftKey) {
                       // Shift + Enter: Submit the form
                       event.preventDefault(); // Prevent default Enter behavior
                       if (formData.text.trim()) { // Only submit if text is not empty
                         handleInlineSubmit();
                       }
                     }
                     // Enter only: Allow default behavior (insert newline)
                   }
                 }}
               />
            </div>
            {/* Submit Button */}
            <div className="flex-shrink-0"> {/* Prevent button from shrinking */}
               <Button type="button" size="icon" variant="success" className="h-8 w-8" onClick={handleInlineSubmit} disabled={!formData.text.trim()} aria-label="Melding opslaan">
                  <CheckIcon className="h-4 w-4" />
               </Button>
            </div>
          </div>
        </div>
      )}

      {/* --- Log Entry List (Recent Entries) --- */}
      <div className="space-y-3">
        {recentEntries.length === 0 ? (
          <p className="text-center text-gray-500 py-4">Geen recente logboek meldingen gevonden.</p>
        ) : (
          recentEntries.map((entry) => (
            entry.id === editingEntryId && editFormData ? (
              // --- EDITING ROW ---
              <div key={entry.id} className="mb-3 p-3 border border-yellow-400 rounded-lg bg-yellow-50 shadow-sm">
                <div className="grid grid-cols-[150px_80px_180px_1fr_auto] gap-3 items-start">
                  {/* Date/Time/Shift Info (Read Only) */}
                  <div className="space-y-1 text-xs text-gray-600 pt-1 h-8">
                     <div>{format(parseISO(entry.timestamp), 'd MMM, HH:mm', { locale: nl })}</div>
                     <div className={`font-medium ${getShiftTextColor(entry.shiftColor)}`}> Ploeg: {entry.shiftColor} </div>
                     {entry.reporter_name && (
                       <div className="flex items-center gap-1 text-blue-700 mt-1">
                         <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-user"><path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"/><circle cx="12" cy="7" r="4"/></svg>
                         <span className="text-xs">{entry.reporter_name}</span>
                       </div>
                     )}
                  </div>
                  {/* Line Selection (Edit) */}
                  <div className="space-y-1">
                     <Select value={editFormData.line} onValueChange={(value) => handleEditInputChange('line', value as ProductionLine)} required>
                        <SelectTrigger className="h-8 text-xs"><SelectValue /></SelectTrigger>
                        <SelectContent>
                           <SelectItem value="tl1">TL1</SelectItem>
                           <SelectItem value="tl2">TL2</SelectItem>
                           <SelectItem value="p1">P1</SelectItem>
                           <SelectItem value="p2">P2</SelectItem>
                           <SelectItem value="p3">P3</SelectItem>
                           <SelectItem value="Yard">Yard</SelectItem>
                           <SelectItem value="Koeling">Koeling</SelectItem>
                           <SelectItem value="Gebouw">Gebouw</SelectItem>
                           <SelectItem value="Overige">Overige</SelectItem>
                        </SelectContent>
                     </Select>
                  </div>
                  {/* Equipment Selection (Edit) */}
                  <div className="space-y-1">
                     <EquipmentSelector
                         value={editFormData.equipment || ''}
                         onChange={(value) => handleEditInputChange('equipment', value)}
                         equipmentOptions={editEquipmentOptions}
                         placeholder="Onderdeel..."
                         className="h-8 text-xs"
                     />
                  </div>
                  {/* Text Input (Edit) */}
                  <div className="space-y-1">
                     <TextareaAutosize
                       value={editFormData.text}
                       onChange={(e) => handleEditInputChange('text', e.target.value)}
                       required minRows={1}
                       className="w-full text-sm p-2 border rounded resize-none focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
                     />
                  </div>
                  {/* Save/Cancel Buttons */}
                  <div className="pt-1 flex gap-1">
                     <Button type="button" size="icon" variant="success" className="h-8 w-8" onClick={handleEditSubmit} disabled={!editFormData.text.trim()} aria-label="Wijziging opslaan">
                        <Save className="h-4 w-4" />
                     </Button>
                     <Button type="button" size="icon" variant="ghost" className="h-8 w-8 text-red-500 hover:bg-red-100" onClick={handleCancelEdit} aria-label="Annuleren">
                        <X className="h-4 w-4" />
                     </Button>
                  </div>
                </div>
              </div>
            ) : (
              // --- DISPLAY ROW ---
              <div key={entry.id} className="p-3 border rounded shadow-sm bg-white group relative">
                <div className="flex justify-between items-center text-xs text-gray-500 mb-1">
                  <div className="flex items-center gap-2">
                    <span>{format(parseISO(entry.timestamp), 'd MMM yyyy, HH:mm', { locale: nl })}</span>
                    {entry.reporter_name && (
                      <span className="flex items-center gap-1 bg-blue-50 text-blue-700 px-2 py-0.5 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-user"><path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"/><circle cx="12" cy="7" r="4"/></svg>
                        {entry.reporter_name}
                      </span>
                    )}
                  </div>
                  <div className="flex items-center gap-3">
                      {entry.equipment && (
                        <p className="text-xs text-gray-500">
                          {/* Use the display map to find the label */}
                          Onderdeel: {entry.equipment === '__OTHER__' ? 'Overige' : allEquipmentOptionsForDisplay.get(entry.equipment) || entry.equipment || 'N/A'}
                        </p>
                      )}
                      <div className="flex items-center gap-2">
                         <span className={`px-1.5 py-0.5 rounded text-xs font-medium ${getShiftBgColor(entry.shiftColor)} ${getShiftTextColor(entry.shiftColor)}`}>
                             {entry.shiftColor}
                         </span>
                         <span className="font-semibold">{entry.line.toUpperCase()}</span>
                      </div>
                  </div>
                </div>
                <p className="text-gray-800 whitespace-pre-wrap mb-1">{entry.text}</p>
                {/* Container only for Edit button now */}
                <div className="flex justify-end mt-1">
                    <Button
                        variant="ghost"
                        size="icon"
                        className="h-6 w-6 text-blue-500 hover:bg-blue-100 rounded-full opacity-0 group-hover:opacity-100 transition-opacity self-end"
                        onClick={() => handleEditClick(entry)}
                        aria-label="Bewerk melding"
                    >
                        <Pencil className="h-3.5 w-3.5" />
                    </Button>
                </div>
              </div>
            )
          ))
        )}
      </div>

      {/* --- Dialogs --- */}
      {/* Confirmation Dialog removed - using inline confirmation */}

      {/* Link to History Page */}
      <div className="mt-6 text-center">
        <Link to="/td-logboek/historie" className="text-sm text-blue-600 hover:underline inline-flex items-center gap-1">
          <History className="h-4 w-4" />
          Bekijk Historie
        </Link>
      </div>
    </div>
  );
};

export default TdLogbookPage;