import React, { useState, useEffect } from 'react';
import { LogbookEntry } from '@/types';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from "@/components/ui/checkbox"; // Import Checkbox
import { Label } from "@/components/ui/label"; // Import Label
import { format } from 'date-fns';
import { nl } from 'date-fns/locale';
import { Trash2, Pencil, X, Users } from 'lucide-react';
import { useUserTeam } from '@/hooks/use-user-team';
import { toast } from 'sonner';

interface LogbookItemProps {
  entry: LogbookEntry;
  onEdit: (entry: LogbookEntry) => void;
  onDeleteConfirm: (entry: LogbookEntry) => void; // Renamed for clarity
}

const CHECKBOX_COLORS = ["Blauw", "Geel", "Groen", "Rood", "Wit"] as const;
type CheckboxColor = typeof CHECKBOX_COLORS[number];

const LogbookItem: React.FC<LogbookItemProps> = ({ entry, onEdit, onDeleteConfirm }) => {
  const { hasTeamAccess } = useUserTeam();
  // Removed showDeleteConfirm state as we'll always show the checkboxes
  // Initialize checkboxes state from localStorage or with default values
  const [checkboxes, setCheckboxes] = useState<Record<CheckboxColor, boolean>>(() => {
    // Try to load saved state from localStorage
    const savedState = localStorage.getItem(`logbook-delete-checkboxes-${entry.id}`);
    if (savedState) {
      try {
        return JSON.parse(savedState) as Record<CheckboxColor, boolean>;
      } catch (e) {
        console.error('Error parsing saved checkbox state:', e);
      }
    }
    // Default to all unchecked except for the team that created the entry
    const defaultState = Object.fromEntries(CHECKBOX_COLORS.map(color => {
      // If this color matches the entry's team, check it by default
      return [color, color === entry.team];
    })) as Record<CheckboxColor, boolean>;
    return defaultState;
  });

  // Controleer of de gebruiker toegang heeft tot dit item
  // Als er geen team is ingesteld, dan heeft iedereen toegang (voor backward compatibility)
  const canEditItem = !entry.team || hasTeamAccess(entry.team);

  const handleCheckboxChange = (color: CheckboxColor, checked: boolean | 'indeterminate') => {
    // Controleer of de gebruiker toegang heeft tot deze ploeg
    if (!hasTeamAccess(color)) {
      toast.error(`U heeft geen rechten om items voor ploeg ${color} goed te keuren.`);
      return;
    }

    if (typeof checked === 'boolean') {
      // Only update if the value is actually changing
      if (checkboxes[color] !== checked) {
        const newCheckboxes = { ...checkboxes, [color]: checked };
        setCheckboxes(newCheckboxes);

        // Save to localStorage - use a debounced approach with setTimeout
        // This prevents rapid localStorage writes when multiple checkboxes are changed
        setTimeout(() => {
          localStorage.setItem(`logbook-delete-checkboxes-${entry.id}`, JSON.stringify(newCheckboxes));
        }, 50);
      }
    }
  };

  const resetCheckboxes = () => {
    // Reset checkboxes to all unchecked
    const emptyState = Object.fromEntries(CHECKBOX_COLORS.map(color => [color, false])) as Record<CheckboxColor, boolean>;
    setCheckboxes(emptyState);

    // Clear localStorage
    localStorage.removeItem(`logbook-delete-checkboxes-${entry.id}`);
  };

  // Effect to handle entry ID changes - only runs when entry.id changes
  useEffect(() => {
    // When entry ID changes, try to load saved state from localStorage
    const savedState = localStorage.getItem(`logbook-delete-checkboxes-${entry.id}`);
    if (savedState) {
      try {
        const parsedState = JSON.parse(savedState) as Record<CheckboxColor, boolean>;
        // Ensure the creator's team checkbox is checked
        if (entry.team && CHECKBOX_COLORS.includes(entry.team as CheckboxColor)) {
          parsedState[entry.team as CheckboxColor] = true;
        }
        setCheckboxes(parsedState);
      } catch (e) {
        console.error('Error parsing saved checkbox state:', e);
      }
    } else if (entry.team && CHECKBOX_COLORS.includes(entry.team as CheckboxColor)) {
      // If no saved state but we have a team, create a new state with the team's checkbox checked
      const newState = Object.fromEntries(CHECKBOX_COLORS.map(color => {
        return [color, color === entry.team];
      })) as Record<CheckboxColor, boolean>;
      setCheckboxes(newState);
    }
  }, [entry.id, entry.team]); // Depend on entry.id and entry.team

  // Separate effect for cleanup/unmount
  useEffect(() => {
    return () => {
      // Only save if at least one checkbox is checked
      if (Object.values(checkboxes).some(Boolean)) {
        localStorage.setItem(`logbook-delete-checkboxes-${entry.id}`, JSON.stringify(checkboxes));
      }
    };
  }, [entry.id, checkboxes]);

  const allCheckboxesChecked = Object.values(checkboxes).every(Boolean);

  const getPriorityBadgeVariant = (priority: LogbookEntry['priority']): "default" | "secondary" | "destructive" | "outline" | null | undefined => {
    switch (priority) {
      case 'Hoog': return 'destructive';
      case 'Middel': return 'secondary';
      case 'Laag': return 'outline';
      default: return 'default';
    }
  };

  // Function to get appropriate color class for team checkboxes
  const getColorClass = (color: CheckboxColor): string => {
    switch (color) {
      case 'Blauw': return 'data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600';
      case 'Geel': return 'data-[state=checked]:bg-yellow-500 data-[state=checked]:border-yellow-500';
      case 'Groen': return 'data-[state=checked]:bg-green-600 data-[state=checked]:border-green-600';
      case 'Rood': return 'data-[state=checked]:bg-red-600 data-[state=checked]:border-red-600';
      case 'Wit': return 'data-[state=checked]:bg-gray-400 data-[state=checked]:border-gray-400';
      default: return '';
    }
  };

  // Function to get appropriate text color class for team labels
  const getTextColorClass = (color: CheckboxColor): string => {
    switch (color) {
      case 'Blauw': return 'text-blue-600';
      case 'Geel': return 'text-yellow-600';
      case 'Groen': return 'text-green-600';
      case 'Rood': return 'text-red-600';
      case 'Wit': return 'text-gray-600';
      default: return 'text-gray-600';
    }
  };

  return (
    <div className="text-sm border-b pb-2 last:border-b-0 flex justify-between items-start gap-2 group">
      {/* Entry Details */}
      <div className="flex-grow">
        <div className="flex justify-between items-center mb-1">
          <div className="flex items-center gap-2">
            <Badge variant={getPriorityBadgeVariant(entry.priority)} className="text-xs px-1.5 py-0.5">
              {entry.priority}
            </Badge>
            <span className="text-xs text-gray-400">
              {format(new Date(entry.timestamp), 'dd-MM HH:mm', { locale: nl })}
            </span>
          </div>
          <div className="flex items-center gap-2">
            {CHECKBOX_COLORS.map((color) => {
              // Pre-compute these values to avoid recalculation during renders
              const hasAccess = hasTeamAccess(color);
              const colorClass = getColorClass(color);
              const textColorClass = getTextColorClass(color);
              const isChecked = checkboxes[color];
              const checkboxId = `${entry.id}-${color}`;
              const titleText = hasAccess ? `Bevestig voor ploeg ${color}` : `U heeft geen rechten voor ploeg ${color}`;

              return (
                <div key={color} className="flex items-center gap-1">
                  <Checkbox
                    id={checkboxId}
                    checked={isChecked}
                    onCheckedChange={(checked) => handleCheckboxChange(color, checked)}
                    disabled={!hasAccess}
                    aria-label={`Checkbox ${color}`}
                    className={`h-4 w-4 ${colorClass}`}
                    title={titleText}
                  />
                  <span
                    className={`text-[0.65rem] font-medium ${!hasAccess ? 'text-gray-400' : textColorClass}`}
                  >
                    {color.charAt(0)}
                  </span>
                </div>
              );
            })}
          </div>
        </div>
        <div className="flex justify-between items-center mb-1">
          <p className="text-xs text-gray-500">Locatie: {entry.location || 'N/A'}</p>
          {entry.team && (
            <Badge variant="outline" className="text-xs px-1.5 py-0.5 flex items-center gap-1">
              <Users className="h-3 w-3" />
              {entry.team}
            </Badge>
          )}
        </div>
        <p className="text-gray-700 whitespace-pre-wrap">{entry.text}</p>
      </div>

      {/* Action Buttons Area */}
      <div className="flex flex-col items-center space-y-1 opacity-0 group-hover:opacity-100 transition-opacity">
        {/* Edit Button */}
        <Button
          variant="ghost"
          size="icon"
          className="h-6 w-6 text-blue-500 hover:bg-blue-100 rounded-full flex-shrink-0"
          onClick={() => onEdit(entry)}
          disabled={!canEditItem}
          title={!canEditItem ? `U heeft geen rechten om items van ploeg ${entry.team} te bewerken` : 'Bewerken'}
        >
          <Pencil className="h-3.5 w-3.5" />
        </Button>
        {/* Delete Button */}
        <Button
          variant="ghost"
          size="icon"
          className="h-6 w-6 text-red-500 hover:bg-red-100 rounded-full flex-shrink-0"
          onClick={() => {
            // Confirm deletion without requiring all checkboxes to be checked
            if (window.confirm('Weet u zeker dat u dit item definitief wilt verwijderen?')) {
              localStorage.removeItem(`logbook-delete-checkboxes-${entry.id}`);
              onDeleteConfirm(entry);
            }
          }}
          disabled={!canEditItem}
          title={!canEditItem ? `U heeft geen rechten om items van ploeg ${entry.team} te verwijderen` : 'Verwijderen'}
        >
          <Trash2 className="h-4 w-4" />
        </Button>
        {/* Reset Checkboxes Button (only visible if any checkbox is checked) */}
        {Object.values(checkboxes).some(Boolean) && (
          <Button
            variant="ghost"
            size="icon"
            className="h-6 w-6 text-gray-500 hover:bg-gray-100 rounded-full flex-shrink-0"
            onClick={resetCheckboxes}
            title="Reset alle vinkjes"
          >
            <X className="h-3.5 w-3.5" />
          </Button>
        )}
      </div>
    </div>
  );
};

export default LogbookItem;