-- Fix the confirm_user_email_v2 function
CREATE OR REPLACE FUNCTION public.confirm_user_email_v2(user_email text)
RETURNS json
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  user_id UUID;
  result JSON;
BEGIN
  -- Get the user ID from the email
  SELECT id INTO user_id FROM auth.users WHERE email = user_email;
  
  IF user_id IS NULL THEN
    RETURN json_build_object('error', 'User not found');
  END IF;
  
  -- Update the user's email_confirmed_at field
  UPDATE auth.users
  SET 
    email_confirmed_at = NOW(), 
    confirmed_at = NOW(),
    updated_at = NOW()
  WHERE id = user_id;
  
  -- Return success
  RETURN json_build_object('success', true, 'user_id', user_id, 'email', user_email);
EXCEPTION WHEN OTHERS THEN
  RETURN json_build_object('error', SQLERRM);
END;
$$;
