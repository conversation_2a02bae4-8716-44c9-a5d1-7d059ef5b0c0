// src/pages/management/NewRegisterPage.tsx
import React, { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase-client';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { toast } from 'sonner';
import { UserPlus, Loader2, ShieldAlert, Users } from 'lucide-react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useNavigate } from 'react-router-dom';

const NewRegisterPage: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isManager, setIsManager] = useState(false);
  const [selectedTeam, setSelectedTeam] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [isCheckingAuth, setIsCheckingAuth] = useState(true);
  const navigate = useNavigate();

  // Check if current user is authorized to register new users
  useEffect(() => {
    const checkAuthorization = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession();
        
        if (!session) {
          setIsAuthorized(false);
          setIsCheckingAuth(false);
          return;
        }

        const { data: authData, error: authError } = await supabase
          .from('authorized_managers')
          .select('user_id')
          .eq('user_id', session.user.id)
          .single();

        if (authError) {
          console.error('Autorisatiefout:', authError);
          setIsAuthorized(false);
        } else {
          console.log('Autorisatie resultaat:', authData);
          setIsAuthorized(true);
        }
      } catch (error) {
        console.error('Fout bij controleren autorisatie:', error);
        setIsAuthorized(false);
      } finally {
        setIsCheckingAuth(false);
      }
    };

    checkAuthorization();
  }, []);

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();

    // Check if passwords match
    if (password !== confirmPassword) {
      toast.error('Wachtwoorden komen niet overeen');
      return;
    }
    
    setIsLoading(true);
    setSuccessMessage('');

    try {
      // 1. Create user with signUp
      const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            is_manager: isManager,
            team: selectedTeam === 'none' ? null : selectedTeam
          }
        }
      });

      if (signUpError) {
        throw new Error(`Fout bij registreren: ${signUpError.message}`);
      }

      if (!signUpData.user) {
        throw new Error('Gebruiker kon niet worden aangemaakt');
      }

      // 2. Manually confirm email
      const { error: confirmError } = await supabase.rpc('confirm_user_email_v2', {
        user_email: email
      });

      if (confirmError) {
        console.warn('Kon e-mail niet handmatig bevestigen:', confirmError);
      }

      // 3. Add to authorized_managers if manager
      if (isManager) {
        const { error: managerError } = await supabase
          .from('authorized_managers')
          .insert([{ user_id: signUpData.user.id }]);

        if (managerError) {
          console.warn('Kon gebruiker niet als beheerder instellen:', managerError);
        }
      }

      // 4. Add to user_teams if team selected
      if (selectedTeam && selectedTeam !== 'none') {
        const { error: teamError } = await supabase
          .from('user_teams')
          .insert([{ user_id: signUpData.user.id, team: selectedTeam }]);

        if (teamError) {
          console.warn('Kon gebruiker niet aan team toewijzen:', teamError);
        }
      }

      // Success!
      let successMsg = `Gebruiker ${email} succesvol geregistreerd`;
      if (isManager) successMsg += ' als beheerder';
      if (selectedTeam && selectedTeam !== 'none') successMsg += ` in ploeg ${selectedTeam}`;
      successMsg += '! De gebruiker kan direct inloggen zonder e-mailbevestiging.';

      setSuccessMessage(successMsg);
      toast.success('Gebruiker succesvol geregistreerd');

      // Reset form
      setEmail('');
      setPassword('');
      setConfirmPassword('');
      setIsManager(false);
      setSelectedTeam('');
    } catch (error) {
      console.error('Registratiefout:', error);
      toast.error(error instanceof Error ? error.message : 'Er is een fout opgetreden bij het registreren');
    } finally {
      setIsLoading(false);
    }
  };

  if (isCheckingAuth) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
        <p className="ml-2">Autorisatie controleren...</p>
      </div>
    );
  }

  if (!isAuthorized) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4">
        <ShieldAlert className="h-16 w-16 text-red-500 mb-4" />
        <h1 className="text-2xl font-bold mb-2">Geen toegang</h1>
        <p className="text-center mb-4">
          U heeft geen toestemming om deze pagina te bekijken. Alleen beheerders kunnen nieuwe gebruikers registreren.
        </p>
        <Button onClick={() => navigate('/')}>Terug naar dashboard</Button>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex items-center mb-6">
        <UserPlus className="h-6 w-6 mr-2" />
        <h1 className="text-2xl font-bold">Nieuwe gebruiker registreren</h1>
      </div>

      <Card className="max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle>Registratieformulier</CardTitle>
          <CardDescription>
            Maak een nieuwe gebruiker aan voor het Dagstart systeem
          </CardDescription>
        </CardHeader>

        {successMessage && (
          <div className="mx-6 mb-4 p-4 bg-green-50 text-green-700 rounded-md">
            {successMessage}
          </div>
        )}

        <form onSubmit={handleRegister}>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">E-mailadres</Label>
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="<EMAIL>"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="password">Wachtwoord</Label>
              <Input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Wachtwoord"
                required
                minLength={6}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="confirmPassword">Bevestig wachtwoord</Label>
              <Input
                id="confirmPassword"
                type="password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                placeholder="Bevestig wachtwoord"
                required
                minLength={6}
              />
            </div>

            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="isManager"
                  checked={isManager}
                  onCheckedChange={(checked) => {
                    const isChecked = checked === true;
                    setIsManager(isChecked);
                    // Als beheerder wordt aangevinkt, zet team automatisch op 'none'
                    if (isChecked) {
                      setSelectedTeam('none');
                    }
                  }}
                />
                <Label htmlFor="isManager" className="cursor-pointer">Gebruiker is beheerder</Label>
              </div>

              <div className="space-y-2">
                <Label htmlFor="team">Ploeg</Label>
                <Select
                  value={selectedTeam}
                  onValueChange={setSelectedTeam}
                >
                  <SelectTrigger id="team">
                    <SelectValue placeholder="Selecteer een ploeg" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">{isManager ? "Geen specifieke ploeg (beheerder)" : "Selecteer een ploeg"}</SelectItem>
                    <SelectItem value="Blauw">Blauw</SelectItem>
                    <SelectItem value="Wit">Wit</SelectItem>
                    <SelectItem value="Geel">Geel</SelectItem>
                    <SelectItem value="Groen">Groen</SelectItem>
                    <SelectItem value="Rood">Rood</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground">
                  {isManager ?
                    "Beheerders hebben toegang tot alle ploegen. Selectie van een ploeg is optioneel." :
                    "Gebruikers kunnen alleen acties uitvoeren voor hun eigen ploeg"}
                </p>
              </div>
            </div>
          </CardContent>

          <CardFooter className="flex justify-between">
            <Button type="button" variant="outline" onClick={() => {
              setEmail('');
              setPassword('');
              setConfirmPassword('');
              setIsManager(false);
              setSelectedTeam('');
            }}>
              Annuleren
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Bezig met registreren...
                </>
              ) : (
                'Registreren'
              )}
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
};

export default NewRegisterPage;
