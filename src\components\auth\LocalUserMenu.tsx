// src/components/auth/LocalUserMenu.tsx
import React from 'react';
import { Link } from 'react-router-dom';
import { useLocalAuth } from '@/context/LocalAuthContext';
import { useToast } from '@/hooks/use-toast';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { LogOut, User, Settings, Shield } from 'lucide-react';

const LocalUserMenu: React.FC = () => {
  const { user, signOut } = useLocalAuth();
  const { toast } = useToast();

  const handleSignOut = async () => {
    await signOut();
    toast.success("Uitgelogd", {
      description: "U bent succesvol uitgelogd."
    });
  };

  // If no user, show login button
  if (!user) {
    return (
      <Link to="/auth" className="text-sm font-medium text-blue-600 hover:text-blue-800 flex items-center">
        <User className="h-4 w-4 mr-2" />
        Inloggen
      </Link>
    );
  }

  // Get initials from email for avatar
  const getInitials = () => {
    if (!user.email) return 'U';
    return user.email.substring(0, 2).toUpperCase();
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <button className="flex items-center gap-2 focus:outline-none">
          <Avatar className="h-8 w-8 cursor-pointer">
            <AvatarFallback className={user.isManager ? "bg-amber-100 text-amber-600" : "bg-blue-100 text-blue-600"}>
              {getInitials()}
            </AvatarFallback>
          </Avatar>
        </button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuLabel>
          <div className="flex flex-col space-y-1">
            <p className="text-sm font-medium leading-none">{user.email}</p>
            {user.isManager && (
              <p className="text-xs text-muted-foreground flex items-center">
                <Shield className="h-3 w-3 mr-1" /> Beheerder
              </p>
            )}
            {user.team && (
              <p className="text-xs text-muted-foreground">
                Ploeg: {user.team}
              </p>
            )}
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem asChild>
          <Link to="/account" className="flex w-full cursor-pointer items-center">
            <User className="mr-2 h-4 w-4" />
            Mijn account
          </Link>
        </DropdownMenuItem>
        <DropdownMenuItem asChild>
          <Link to="/settings/toggles" className="flex w-full cursor-pointer items-center">
            <Settings className="mr-2 h-4 w-4" />
            Instellingen
          </Link>
        </DropdownMenuItem>
        {user.isManager && (
          <>
            <DropdownMenuSeparator />
            <DropdownMenuItem asChild>
              <Link to="/management" className="flex w-full cursor-pointer items-center">
                <Shield className="mr-2 h-4 w-4" />
                Beheer
              </Link>
            </DropdownMenuItem>
          </>
        )}
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={handleSignOut} className="text-red-600 cursor-pointer">
          <LogOut className="mr-2 h-4 w-4" />
          Uitloggen
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default LocalUserMenu;
