console.log('[DEBUG] test-supabase-integration.ts wordt geladen...');
/**
 * Test script voor de Supabase integratie.
 * Dit script test of de middleware laag correct werkt door data op te slaan en op te halen.
 */
import { supabase } from './supabase-client';
import { saveItem, loadItem, removeItem } from './local-storage';
import { migrateLocalStorageToSupabase } from './migrate-local-storage';

// Tabel waarin we de localStorage data opslaan
const STORAGE_TABLE = 'local_storage_data';

/**
 * Controleert of de benodigde tabel bestaat in Supabase.
 */
async function checkTableExists() {
  try {
    const { error } = await supabase
      .from(STORAGE_TABLE)
      .select('count')
      .limit(1);

    if (error && error.code === 'PGRST116') {
      console.error(`Tabel '${STORAGE_TABLE}' bestaat niet. Voer eerst de migratie uit.`);
      return false;
    } else if (error) {
      console.error('Fout bij controleren of tabel bestaat:', error);
      return false;
    }
    
    console.log(`Tabel '${STORAGE_TABLE}' bestaat.`);
    return true;
  } catch (error) {
    console.error('Onverwachte fout bij controleren of tabel bestaat:', error);
    return false;
  }
}

/**
 * Test de Supabase integratie door data op te slaan en op te halen.
 */
async function testSupabaseIntegration() {
  console.log('Start test van Supabase integratie...');
  
  // Controleer of de tabel bestaat
  const tableExists = await checkTableExists();
  if (!tableExists) {
    console.log('Tabel bestaat niet, migratie wordt uitgevoerd...');
    await migrateLocalStorageToSupabase();
  }
  
  // Test data
  const testKey = 'test_supabase_integration';
  const testValue = {
    message: 'Dit is een test',
    timestamp: new Date().toISOString(),
    random: Math.random()
  };
  
  console.log(`Test 1: Opslaan van data met sleutel '${testKey}'...`);
  const saveResult = saveItem(testKey, testValue);
  console.log('Resultaat van saveItem:', saveResult);
  
  // Wacht even om er zeker van te zijn dat de asynchrone operatie is voltooid
  console.log('Wachten op asynchrone operatie...');
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  console.log(`Test 2: Direct controleren in Supabase voor sleutel '${testKey}'...`);
  const { data, error } = await supabase
    .from(STORAGE_TABLE)
    .select('*')
    .eq('key', testKey)
    .single();
  
  if (error) {
    console.error('Fout bij direct controleren in Supabase:', error);
  } else {
    console.log('Data in Supabase:', data);
    console.log('Test 2 geslaagd: Data is correct opgeslagen in Supabase.');
  }
  
  console.log(`Test 3: Laden van data met sleutel '${testKey}'...`);
  const loadedValue = loadItem(testKey);
  console.log('Geladen waarde:', loadedValue);
  
  if (JSON.stringify(loadedValue) === JSON.stringify(testValue)) {
    console.log('Test 3 geslaagd: Geladen data komt overeen met opgeslagen data.');
  } else {
    console.error('Test 3 mislukt: Geladen data komt niet overeen met opgeslagen data.');
  }
  
  console.log(`Test 4: Verwijderen van data met sleutel '${testKey}'...`);
  const removeResult = removeItem(testKey);
  console.log('Resultaat van removeItem:', removeResult);
  
  // Wacht even om er zeker van te zijn dat de asynchrone operatie is voltooid
  console.log('Wachten op asynchrone operatie...');
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  console.log(`Test 5: Controleren of data is verwijderd uit Supabase voor sleutel '${testKey}'...`);
  const { data: dataAfterRemove, error: errorAfterRemove } = await supabase
    .from(STORAGE_TABLE)
    .select('*')
    .eq('key', testKey)
    .single();
  
  if (errorAfterRemove && errorAfterRemove.code === 'PGRST116') {
    console.log('Test 5 geslaagd: Data is correct verwijderd uit Supabase.');
  } else if (errorAfterRemove) {
    console.error('Fout bij controleren of data is verwijderd:', errorAfterRemove);
  } else {
    console.error('Test 5 mislukt: Data is niet verwijderd uit Supabase:', dataAfterRemove);
  }
  
  console.log('Alle tests zijn uitgevoerd.');
}

// Voer de test uit
testSupabaseIntegration()
  .then(() => {
    console.log('Test script voltooid.');
  })
  .catch(error => {
    console.error('Onverwachte fout tijdens testen:', error);
  });
