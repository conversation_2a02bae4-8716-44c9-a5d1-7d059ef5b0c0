-- Migration script to create the authorized_managers table

-- Create the table to store authorized user IDs
create table public.authorized_managers (
  user_id uuid not null references auth.users(id) on delete cascade,
  created_at timestamp with time zone not null default now(),

  primary key (user_id)
);

-- Enable Row Level Security (RLS) for the table
alter table public.authorized_managers enable row level security;

-- Policy: Allow authenticated users to read their own entry (if needed for client-side checks)
-- Note: Adjust this policy based on actual security requirements.
-- If checks are purely server-side or via Edge Functions, this might not be necessary
-- or could be restricted further (e.g., only allow reads by a specific role).
create policy "Allow authenticated users to read their own entry"
on public.authorized_managers for select
using ( auth.uid() = user_id );

-- Policy: Allow service_role or specific admin roles to manage entries (Example)
-- This policy is crucial for adding/removing users from the authorized list.
-- For now, we assume management happens via service_role key (e.g., in Edge Functions or backend).
-- If you need specific user roles (e.g., an 'admin' role) to manage this, adjust accordingly.
create policy "Allow service_role to manage entries"
on public.authorized_managers for all
using ( true ); -- Allows service_role full access implicitly

-- Add comments to the table and columns for clarity
comment on table public.authorized_managers is 'Stores the user IDs of individuals authorized to access restricted management sections.';
comment on column public.authorized_managers.user_id is 'References the user''s ID in auth.users.';
comment on column public.authorized_managers.created_at is 'Timestamp when the user was granted authorization.';