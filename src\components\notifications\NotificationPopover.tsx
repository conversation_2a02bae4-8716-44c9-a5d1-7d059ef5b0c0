import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Bell } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { useNotifications } from '@/context/NotificationContext';
import { format } from 'date-fns';
import { nl } from 'date-fns/locale';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Notification } from '@/types';

export const NotificationPopover: React.FC = () => {
  const { notifications, unreadCount, markAsRead, markAllAsRead, deleteNotification, clearAllNotifications } = useNotifications();
  const [open, setOpen] = React.useState(false);
  const [prevUnreadCount, setPrevUnreadCount] = React.useState(0);
  const navigate = useNavigate();

  // Automatically open popover when new notifications arrive
  React.useEffect(() => {
    if (unreadCount > prevUnreadCount && unreadCount > 0) {
      setOpen(true);
    }
    setPrevUnreadCount(unreadCount);
  }, [unreadCount, prevUnreadCount]);

  // Handle clicking on a notification
  const handleNotificationClick = async (notification: Notification) => {
    // Delete the notification
    await deleteNotification(notification.id);

    // Navigate based on notification type
    switch (notification.type) {
      case 'todo_completed':
        // Navigate to todo history
        navigate('/history');
        setOpen(false);
        break;
      case 'todo_assigned':
      case 'todo_expired':
        // Navigate to todo list
        navigate('/todos');
        setOpen(false);
        break;
      default:
        // No navigation for other types
        break;
    }
  };

  // Handle dismissing a notification
  const handleDismiss = async (e: React.MouseEvent, notification: Notification) => {
    e.stopPropagation();

    // Delete the notification regardless of type
    await deleteNotification(notification.id);
  };

  // Format the notification date
  const formatNotificationDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return format(date, 'dd MMM HH:mm', { locale: nl });
    } catch (e) {
      return 'Onbekende datum';
    }
  };

  // Get icon and color based on notification type
  const getNotificationStyle = (type: string) => {
    switch (type) {
      case 'todo_completed':
        return { bgColor: 'bg-green-50', textColor: 'text-green-700', borderColor: 'border-green-200' };
      case 'todo_assigned':
        return { bgColor: 'bg-blue-50', textColor: 'text-blue-700', borderColor: 'border-blue-200' };
      case 'todo_expired':
        return { bgColor: 'bg-red-50', textColor: 'text-red-700', borderColor: 'border-red-200' };
      default:
        return { bgColor: 'bg-gray-50', textColor: 'text-gray-700', borderColor: 'border-gray-200' };
    }
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button variant="ghost" size="icon" className="relative">
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <Badge
              className="absolute -top-1 -right-1 px-1.5 py-0.5 min-w-[18px] h-[18px] flex items-center justify-center"
              variant="destructive"
            >
              {unreadCount}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0" align="start">
        <div className="flex items-center justify-between p-3 border-b">
          <h3 className="font-medium">Notificaties</h3>
          {notifications.length > 0 && (
            <Button
              variant="ghost"
              size="sm"
              className="text-xs h-7"
              onClick={async () => {
                // Delete all notifications instead of just marking them as read
                await clearAllNotifications();
                setOpen(false); // Close popover after processing all notifications
              }}
            >
              Alles gelezen
            </Button>
          )}
        </div>
        <ScrollArea className="max-h-[400px]">
          {notifications.length === 0 ? (
            <div className="p-4 text-center text-sm text-gray-500">
              Geen notificaties
            </div>
          ) : (
            <div className="divide-y">
              {notifications.map((notification) => {
                const style = getNotificationStyle(notification.type);
                return (
                  <div
                    key={notification.id}
                    className={`p-3 cursor-pointer hover:bg-gray-50 ${!notification.read ? style.bgColor : ''}`}
                    onClick={() => handleNotificationClick(notification)}
                  >
                    <div className="flex justify-between items-start">
                      <div className={`text-sm ${style.textColor} ${!notification.read ? 'font-medium' : ''}`}>
                        {notification.message}
                      </div>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-6 w-6 -mt-1 -mr-1 text-gray-400 hover:text-gray-500"
                        onClick={(e) => handleDismiss(e, notification)}
                      >
                        &times;
                      </Button>
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      {formatNotificationDate(notification.created_at)}
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </ScrollArea>
      </PopoverContent>
    </Popover>
  );
};
