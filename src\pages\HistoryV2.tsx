import React, { useState, useMemo, useEffect } from 'react'; // Added useEffect
import { TodoItem } from '@/types';
import { useIsMobile } from '@/hooks/use-mobile'; // Import useIsMobile
import { useProduction } from '@/context/ProductionContext';
import { useAuth } from '@/context/AuthContext';

import { supabase } from '@/lib/supabase-client';
import { format, parseISO, isWithinInterval, startOfDay, endOfDay, subDays, isValid } from 'date-fns';
import { nl } from 'date-fns/locale';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem } from '@/components/ui/dropdown-menu'; // Import Dropdown components
import { ProductionLine, HistoryEntry, ProductionRow, Disruption, Incident, RcaEntry } from '@/types'; // Import RcaEntry
import {
  Calendar as CalendarIcon,
  Download,
  TrendingUp,
  Activity,
  List,
  BarChart,
  Search,
  SlidersHorizontal,
  X,
  History as HistoryIcon,
  Filter,
  CalendarDays,
  ChevronUp,
  ChevronDown,
  Sparkles, // AI Icon
  Menu, // Import Menu icon
  ClipboardList, // For Todo tab
  CheckCircle, // For completed todos
  Clock, // For due date
  MessageSquare, // For comments
  History
} from 'lucide-react';
import { StatisticsCards } from '@/components/history/StatisticsCards';
import { ProductionTable } from '@/components/history/ProductionTable';
import { DisruptionsTable } from '@/components/history/DisruptionsTable';
import { CalendarView } from '@/components/history/CalendarView';
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { SettingsSubNav } from '@/components/layout/SettingsSubNav';
import { useLocation } from 'react-router-dom';
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Label } from "@/components/ui/label"
import {
  Dialog, // Import Dialog components
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogTrigger,
  DialogClose
} from '@/components/ui/dialog';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger
} from "@/components/ui/accordion";
import { Badge } from "@/components/ui/badge";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { ResponsiveContainer, BarChart as RechartsBarChart, Bar, XAxis, YAxis, Tooltip, Legend, CartesianGrid } from 'recharts';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import { DateRange } from "react-day-picker";
import { Calendar } from "@/components/ui/calendar";

import { Combobox } from "@/components/ui/combobox"; // Added Combobox import
import { Textarea } from '@/components/ui/textarea'; // Added Textarea import

type DateFilter = '7d' | '14d' | '30d' | '90d' | 'all';
type YesNoAllFilter = 'yes' | 'no' | 'all'; // Type for the new filters

// Helper function to get line color classes (assuming it exists or define it)
const getLineColorClass = (line: ProductionLine, type: 'bg' | 'text' | 'border'): string => {
  const colors: Record<ProductionLine, { bg: string; text: string; border: string }> = {
    tl1: { bg: 'bg-blue-100', text: 'text-blue-800', border: 'border-blue-200' },
    tl2: { bg: 'bg-green-100', text: 'text-green-800', border: 'border-green-200' },
    p1: { bg: 'bg-yellow-100', text: 'text-yellow-800', border: 'border-yellow-200' },
    p2: { bg: 'bg-purple-100', text: 'text-purple-800', border: 'border-purple-200' },
    p3: { bg: 'bg-red-100', text: 'text-red-800', border: 'border-red-200' },
    Yard: { bg: 'bg-gray-100', text: 'text-gray-800', border: 'border-gray-200' },
    Koeling: { bg: 'bg-cyan-100', text: 'text-cyan-800', border: 'border-cyan-200' },
    Gebouw: { bg: 'bg-orange-100', text: 'text-orange-800', border: 'border-orange-200' },
    Overige: { bg: 'bg-slate-100', text: 'text-slate-800', border: 'border-slate-200' },
  };
  return colors[line]?.[type] || 'bg-gray-100 text-gray-800 border-gray-200'; // Default fallback
};

// Helper functie om de datum weergave te bepalen
const formatDateDisplay = (startDate: string, endDate: string) => {
  try {
    const start = parseISO(startDate);
    const end = parseISO(endDate);
    if (!isValid(start) || !isValid(end)) return "Ongeldige datum";
    if (startDate === endDate) {
      return format(start, 'EEEE d MMMM yyyy', { locale: nl });
    } else {
      return `Van ${format(start, 'd MMM', { locale: nl })} tot ${format(end, 'd MMM yyyy', { locale: nl })}`;
    }
  } catch (e) {
    return "Datumfout";
  }
};

// Helper function to format single dates or timestamps
const formatSingleDate = (dateString: string | undefined | null, includeTime = false): string => {
  if (!dateString) return '-';
  try {
    const date = parseISO(dateString);
    if (!isValid(date)) return 'Ongeldige datum';
    const formatString = includeTime ? 'd MMM yyyy, HH:mm' : 'd MMM yyyy';
    return format(date, formatString, { locale: nl });
  } catch (e) {
    return 'Datumfout';
  }
};


// Verplaats countTotalDisruptions naar boven
const countTotalDisruptions = (entry: HistoryEntry): number => {
  // Ensure entry.data exists and is an object before proceeding
  if (!entry || !entry.data || typeof entry.data !== 'object') {
    return 0;
  }
  return Object.values(entry.data).reduce((sum, lineData) => {
    // Ensure lineData and lineData.disruptions exist
    if (lineData && Array.isArray(lineData.disruptions)) {
      return sum + lineData.disruptions.length;
    }
    return sum;
  }, 0);
};


const lines: ProductionLine[] = ['tl1', 'tl2', 'p1', 'p2', 'p3']; // Define lines array

const HistoryV2 = () => {
  const {
    historyData,
    vkHistoryData,
    disruptionHistory,
    equipmentOptions // Get equipment options from context
  } = useProduction();
  const { user } = useAuth();
  const location = useLocation();
  const isMobile = useIsMobile(); // Get mobile status
  const [selectedLine, setSelectedLine] = useState<ProductionLine | 'all'>('all');
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: subDays(new Date(), 29),
    to: new Date(),
  });
  const [selectedMaterial, setSelectedMaterial] = useState<string | 'all'>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [openAccordionItems, setOpenAccordionItems] = useState<string[]>([]);
  const [activeTab, setActiveTab] = useState("rapport");
  const [vkDateRange, setVkDateRange] = useState<DateRange | undefined>(undefined);
  const [maxVkIncidentsToShow, setMaxVkIncidentsToShow] = useState<number>(10);
  const [maxReportsToShow, setMaxReportsToShow] = useState<number>(10); // State for report limit
  const [maxDisruptionsToShow, setMaxDisruptionsToShow] = useState<number>(10); // State for disruption limit
  const [vkSearchQuery, setVkSearchQuery] = useState<string>(''); // State for V&K search
  const [vkTypeFilter, setVkTypeFilter] = useState<'all' | 'safety' | 'quality'>('all'); // State for V&K type filter


  const [storingenLineFilter, setStoringenLineFilter] = useState<ProductionLine | 'all'>('all'); // State for disruption history line filter
  const [storingenDateRange, setStoringenDateRange] = useState<DateRange | undefined>(undefined); // State for disruption history date filter
  const [solutionFilter, setSolutionFilter] = useState<YesNoAllFilter>('all'); // State for solution filter
  // const [completionDateFilter, setCompletionDateFilter] = useState<YesNoAllFilter>('all'); // Removed state for completion date filter
  const [equipmentFilter, setEquipmentFilter] = useState<string>(''); // State for equipment filter
  const [aiHelpDialogOpen, setAiHelpDialogOpen] = useState(false); // State for AI Help dialog

  // State for AI Help Dialog Inputs
  const [aiHelpLineFilter, setAiHelpLineFilter] = useState<ProductionLine | 'all'>('all');
  const [aiHelpEquipmentFilter, setAiHelpEquipmentFilter] = useState<string>('');
  const [aiHelpQuestion, setAiHelpQuestion] = useState<string>('');
  const [aiHelpResponse, setAiHelpResponse] = useState<string>(''); // State for AI response
  const [aiHelpLoading, setAiHelpLoading] = useState<boolean>(false); // State for AI loading

  // State for Todo History
  const [completedTodos, setCompletedTodos] = useState<TodoItem[]>([]);
  const [todoDateRange, setTodoDateRange] = useState<DateRange | undefined>(undefined);
  const [todoSearchQuery, setTodoSearchQuery] = useState<string>('');
  const [todoTeamFilter, setTodoTeamFilter] = useState<string>('all');
  const [maxTodosToShow, setMaxTodosToShow] = useState<number>(25);


  const uniqueMaterials = useMemo(() => {
    const materials = new Set<string>();
    historyData.forEach(entry => {
      if (entry.data && typeof entry.data === 'object') {
        Object.values(entry.data).forEach(lineData => {
          if (lineData && lineData.rows) {
             lineData.rows.forEach(row => {
                if (row.od?.material) materials.add(row.od.material);
                if (row.md?.material) materials.add(row.md.material);
                if (row.nd?.material) materials.add(row.nd.material);
             });
          }
        });
      } else {
        console.warn("Skipping history entry due to invalid data structure:", entry);
      }
    });
    return Array.from(materials).sort();
  }, [historyData]);

  // Prepare equipment options for the main filter combobox based on the selected line
  const currentEquipmentOptionsForFilter = useMemo(() => {
    if (storingenLineFilter === 'all') {
      // Combine options from all lines if 'all' is selected
      const allOptions = new Map<string, string>(); // Use Map to handle potential duplicates by value
      lines.forEach(line => {
        const lineOptions = equipmentOptions[line];
        if (lineOptions) {
          Object.values(lineOptions).flat().forEach(opt => {
            if (!allOptions.has(opt.value)) {
              allOptions.set(opt.value, opt.label_nl);
            }
          });
        }
      });
      return Array.from(allOptions, ([value, label]) => ({ value, label })).sort((a, b) => a.label.localeCompare(b.label));
    }
    // Otherwise, get options for the specific selected line
    const lineOptions = equipmentOptions[storingenLineFilter];
    return lineOptions ? Object.values(lineOptions).flat().map(opt => ({ value: opt.value, label: opt.label_nl })).sort((a, b) => a.label.localeCompare(b.label)) : [];
  }, [storingenLineFilter, equipmentOptions]);

  // Prepare equipment options for the AI Help dialog combobox based on the selected line in the dialog
  const aiHelpEquipmentOptions = useMemo(() => {
    if (aiHelpLineFilter === 'all') {
      const allOptions = new Map<string, string>();
      lines.forEach(line => {
        const lineOptions = equipmentOptions[line];
        if (lineOptions) {
          Object.values(lineOptions).flat().forEach(opt => {
            if (!allOptions.has(opt.value)) {
              allOptions.set(opt.value, opt.label_nl);
            }
          });
        }
      });
      return Array.from(allOptions, ([value, label]) => ({ value, label })).sort((a, b) => a.label.localeCompare(b.label));
    }
    const lineOptions = equipmentOptions[aiHelpLineFilter];
    return lineOptions ? Object.values(lineOptions).flat().map(opt => ({ value: opt.value, label: opt.label_nl })).sort((a, b) => a.label.localeCompare(b.label)) : [];
  }, [aiHelpLineFilter, equipmentOptions]);


  const filteredData = useMemo(() => {
    let data = historyData.filter(entry => entry.data && typeof entry.data === 'object');

    if (dateRange?.from && dateRange?.to) {
       const rangeStart = startOfDay(dateRange.from);
       const rangeEnd = endOfDay(dateRange.to);
       data = data.filter(entry => {
         try {
           const entryStart = startOfDay(parseISO(entry.startDate));
           const entryEnd = endOfDay(parseISO(entry.endDate));
           return (
             isWithinInterval(entryStart, { start: rangeStart, end: rangeEnd }) ||
             isWithinInterval(entryEnd, { start: rangeStart, end: rangeEnd }) ||
             (entryStart <= rangeStart && entryEnd >= rangeEnd)
           );
         } catch (e) { return false; }
       });
    } else if (dateRange?.from) {
        const rangeStart = startOfDay(dateRange.from);
        data = data.filter(entry => {
          try {
            return endOfDay(parseISO(entry.endDate)) >= rangeStart;
          } catch (e) { return false; }
        });
    }

    data = data.filter(entry =>
      selectedLine === 'all' || entry.line === selectedLine
    );

    if (selectedMaterial !== 'all') {
      data = data.filter(entry => {
        const lineData = entry.data[entry.line];
        return lineData?.rows.some(row =>
          row.od.material === selectedMaterial ||
          row.md.material === selectedMaterial ||
          row.nd.material === selectedMaterial
        );
      });
    }

    if (searchQuery) {
       data = data.filter(entry => {
          const searchTerm = searchQuery.toLowerCase();
          try {
            if (format(parseISO(entry.startDate), 'd-M-yyyy', { locale: nl }).includes(searchTerm)) return true;
            if (format(parseISO(entry.endDate), 'd-M-yyyy', { locale: nl }).includes(searchTerm)) return true;
            if (formatDateDisplay(entry.startDate, entry.endDate).toLowerCase().includes(searchTerm)) return true;
            if (entry.data && typeof entry.data === 'object') {
              if (JSON.stringify(entry.data).toLowerCase().includes(searchTerm)) return true;
            }
          } catch (e) { /* ignore errors during search */ }
          return false;
        });
    }

    return data.sort((a, b) => {
      try {
        return parseISO(b.endDate).getTime() - parseISO(a.endDate).getTime();
      } catch (e) { return 0; }
    }).slice(0, maxReportsToShow); // Apply the limit here

  }, [historyData, dateRange, selectedLine, selectedMaterial, searchQuery, maxReportsToShow]); // Add maxReportsToShow dependency

  const handleExport = () => {
    const jsonStr = JSON.stringify(filteredData, null, 2);
    const blob = new Blob([jsonStr], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `productie-geschiedenis-${format(new Date(), 'yyyy-MM-dd')}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }

  const aggregateProductionData = (entry: HistoryEntry, lineFilter: ProductionLine | 'all') => {
    let totalProduction = 0;
    let yieldSum = 0;
    let yieldCount = 0;
    const shiftsData: { name: string; production: number }[] = [
      { name: 'OD', production: 0 },
      { name: 'MD', production: 0 },
      { name: 'ND', production: 0 }
    ];
    const productionDetails: { line: string; od: number; md: number; nd: number; total: number; avgYield: number }[] = [];

    Object.entries(entry.data).forEach(([line, data]) => {
      if (lineFilter !== 'all' && line !== lineFilter) return;

      let lineTotal = 0;
      let lineYieldSum = 0;
      let lineYieldCount = 0;
      let lineOD = 0, lineMD = 0, lineND = 0;

      data.rows.forEach(row => {
        const odProd = Number(row.od.production) || 0;
        const mdProd = Number(row.md.production) || 0;
        const ndProd = Number(row.nd.production) || 0;
        const odYield = Number(row.od.yield) || 0;
        const mdYield = Number(row.md.yield) || 0;
        const ndYield = Number(row.nd.yield) || 0;

        lineOD += odProd;
        lineMD += mdProd;
        lineND += ndProd;
        lineTotal += odProd + mdProd + ndProd;

        totalProduction += odProd + mdProd + ndProd;
        shiftsData[0].production += odProd;
        shiftsData[1].production += mdProd;
        shiftsData[2].production += ndProd;

        if (odYield > 0) { lineYieldSum += odYield; lineYieldCount++; yieldSum += odYield; yieldCount++; }
        if (mdYield > 0) { lineYieldSum += mdYield; lineYieldCount++; yieldSum += mdYield; yieldCount++; }
        if (ndYield > 0) { lineYieldSum += ndYield; lineYieldCount++; yieldSum += ndYield; yieldCount++; }
      });

      if (data.rows.length > 0) {
        productionDetails.push({
          line: line.toUpperCase(),
          od: lineOD,
          md: lineMD,
          nd: lineND,
          total: lineTotal,
          avgYield: lineYieldCount > 0 ? lineYieldSum / lineYieldCount : 0
        });
      }
    });

    const averageYield = yieldCount > 0 ? yieldSum / yieldCount : 0;

    return { totalProduction, averageYield, shiftsData, productionDetails };
  };

  const filteredVkHistory = useMemo(() => {
    let data = vkHistoryData;
    if (vkDateRange?.from && vkDateRange?.to) {
       const rangeStart = startOfDay(vkDateRange.from);
       const rangeEnd = endOfDay(vkDateRange.to);
       data = data.filter(incident => {
         try {
            const dateToSortBy = incident.createdAt || incident.resolveDate;
            if (!dateToSortBy) return false;
            const incidentDate = parseISO(dateToSortBy);
            return isWithinInterval(incidentDate, { start: rangeStart, end: rangeEnd });
         } catch (e) { return false; }
       });
    } else if (vkDateRange?.from) {
        const rangeStart = startOfDay(vkDateRange.from);
        data = data.filter(incident => {
          try {
            const dateToSortBy = incident.createdAt || incident.resolveDate;
            if (!dateToSortBy) return false;
            return parseISO(dateToSortBy) >= rangeStart;
          } catch (e) { return false; }
        });
    }

    // Apply Type Filter
    if (vkTypeFilter !== 'all') {
      data = data.filter(incident => incident.type === vkTypeFilter);
    }

    // Apply Search Query Filter
    if (vkSearchQuery) {
      const searchTerm = vkSearchQuery.toLowerCase();
      data = data.filter(incident => {
        const description = incident.description?.toLowerCase() || '';
        const location = incident.location?.toLowerCase() || '';
        const solution = incident.solution?.toLowerCase() || '';
        const followup = incident.followup?.toLowerCase() || '';
        const responsible = incident.responsible?.toLowerCase() || '';
        // Add any other relevant fields to search here
        return description.includes(searchTerm) ||
               location.includes(searchTerm) ||
               solution.includes(searchTerm) ||
               followup.includes(searchTerm) ||
               responsible.includes(searchTerm);
      });
    }

    // Sort the filtered data
    return data.sort((a, b) => {
       const dateAStr = a.createdAt || a.resolveDate;
       const dateBStr = b.createdAt || b.resolveDate;
       if (!dateAStr || !dateBStr) return 0;
       try {
         const dateA = parseISO(dateAStr);
         const dateB = parseISO(dateBStr);
         return dateB.getTime() - dateA.getTime();
       } catch (e) {
         console.error("Error parsing date in VK history sort:", e);
         return 0;
       }
    });
  }, [vkHistoryData, vkDateRange, vkSearchQuery, vkTypeFilter]); // Added vkSearchQuery and vkTypeFilter dependencies

  const historicalSafetyIncidents = filteredVkHistory.filter(i => i.type === 'safety');
  const historicalQualityIncidents = filteredVkHistory.filter(i => i.type === 'quality');

  const safetyIncidentsToShow = maxVkIncidentsToShow === -1
    ? historicalSafetyIncidents
    : historicalSafetyIncidents.slice(0, maxVkIncidentsToShow);

  const qualityIncidentsToShow = maxVkIncidentsToShow === -1
    ? historicalQualityIncidents
    : historicalQualityIncidents.slice(0, maxVkIncidentsToShow);

  // Filter completed todos
  const filteredTodos = useMemo(() => {
    return completedTodos
      .filter(todo => {
        // Apply search filter if any
        if (todoSearchQuery && !todo.title.toLowerCase().includes(todoSearchQuery.toLowerCase()) &&
            !(todo.description && todo.description.toLowerCase().includes(todoSearchQuery.toLowerCase()))) {
          return false;
        }

        // Apply team filter if not 'all'
        if (todoTeamFilter !== 'all') {
          if (!todo.assigned_teams || !todo.assigned_teams.includes(todoTeamFilter)) {
            return false;
          }
        }

        return true;
      })
      .slice(0, maxTodosToShow);
  }, [completedTodos, todoSearchQuery, todoTeamFilter, maxTodosToShow]);

  // Format date for todo items
  const formatTodoDate = (dateString: string | null | undefined) => {
    if (!dateString) return 'Onbekend';
    try {
      return format(new Date(dateString), 'dd MMM yyyy HH:mm', { locale: nl });
    } catch (e) {
      return 'Ongeldige datum';
    }
  };



  // Filter disruption history based on the selected line filter and date range - Updated
  const filteredDisruptionHistory = useMemo(() => {
    let data = disruptionHistory;

    // Line Filter
    if (storingenLineFilter !== 'all') {
      data = data.filter(d => d.line === storingenLineFilter);
    }

    // Date Range Filter
    if (storingenDateRange?.from && storingenDateRange?.to) {
       const rangeStart = startOfDay(storingenDateRange.from);
       const rangeEnd = endOfDay(storingenDateRange.to);
       data = data.filter(d => {
         try {
            // Filter based on resolvedAt, fallback to createdAt if not resolved
            const dateToSortBy = d.resolvedAt || d.createdAt;
            if (!dateToSortBy) return false;
            const disruptionDate = parseISO(dateToSortBy);
            return isWithinInterval(disruptionDate, { start: rangeStart, end: rangeEnd });
         } catch (e) { return false; }
       });
    } else if (storingenDateRange?.from) {
        const rangeStart = startOfDay(storingenDateRange.from);
        data = data.filter(d => {
          try {
            const dateToSortBy = d.resolvedAt || d.createdAt;
            if (!dateToSortBy) return false;
            return parseISO(dateToSortBy) >= rangeStart;
          } catch (e) { return false; }
        });
    }

    // Solution Filter
    if (solutionFilter !== 'all') {
      const hasSolution = solutionFilter === 'yes';
      // Check if 'oplossingen' field is truthy (not null, undefined, empty string)
      data = data.filter(d => !!d.oplossingen === hasSolution);
    }

    // Completion Date Filter - REMOVED
    // if (completionDateFilter !== 'all') {
    //   const hasCompletionDate = completionDateFilter === 'yes';
    //   // Check if 'gereedmelddatum' field is truthy
    //   data = data.filter(d => !!d.gereedmelddatum === hasCompletionDate);
    // }

    // Equipment Filter
    if (equipmentFilter) {
      data = data.filter(d => d.equipment === equipmentFilter);
    }

    // Already sorted by resolvedAt in the hook, no need to re-sort unless criteria change
    // Apply the limit
    return data.slice(0, maxDisruptionsToShow);
  }, [disruptionHistory, storingenLineFilter, storingenDateRange, solutionFilter, /* completionDateFilter, */ equipmentFilter, maxDisruptionsToShow]); // Add maxDisruptionsToShow dependency

  // Reset equipment filter when line filter changes
  useEffect(() => {
    setEquipmentFilter('');
  }, [storingenLineFilter]);

  // Reset AI help equipment filter when AI help line filter changes
  useEffect(() => {
    setAiHelpEquipmentFilter('');
  }, [aiHelpLineFilter]);

  // Fetch completed todos
  useEffect(() => {
    const abortController = new AbortController();

    const fetchCompletedTodos = async () => {
      try {
        let query = supabase
          .from('todos')
          .select('*')
          .eq('completed', true)
          .order('completed_at', { ascending: false });

        // Apply date range filter if set (handle both single-ended and full ranges)
        if (todoDateRange?.from) {
          const fromDate = format(todoDateRange.from, 'yyyy-MM-dd');
          query = query.gte('completed_at', `${fromDate}T00:00:00`);

          if (todoDateRange.to) {
            const toDate = format(todoDateRange.to, 'yyyy-MM-dd');
            query = query.lte('completed_at', `${toDate}T23:59:59`);
          }
        }

        // Apply team filter if set
        if (todoTeamFilter && todoTeamFilter !== 'all') {
          query = query.contains('assigned_teams', [todoTeamFilter]);
        }

        // Apply limit if set
        if (maxTodosToShow > 0) {
          query = query.limit(maxTodosToShow);
        }

        const { data, error } = await query;

        // Check if request was aborted
        if (abortController.signal.aborted) {
          return;
        }

        if (error) {
          console.error('Error fetching completed todos:', error);
          return;
        }

        setCompletedTodos(data || []);
      } catch (error) {
        if (!abortController.signal.aborted) {
          console.error('Error in fetchCompletedTodos:', error);
        }
      }
    };

    fetchCompletedTodos();

    // Cleanup function to abort request if component unmounts or dependencies change
    return () => {
      abortController.abort();
    };
  }, [todoDateRange, todoTeamFilter, maxTodosToShow, user]);

  // Handler for AI Help Submit (Placeholder)
  const handleAiHelpSubmit = () => {
    console.log("AI Help Requested:", {
      line: aiHelpLineFilter,
      equipment: aiHelpEquipmentFilter,
      question: aiHelpQuestion
    });
    setAiHelpLoading(true); // Show loading state
    setAiHelpResponse(''); // Clear previous response

    // Simulate AI response after a delay
    setTimeout(() => {
      setAiHelpResponse(`Dit is een placeholder antwoord voor de vraag: "${aiHelpQuestion}" over lijn ${aiHelpLineFilter} en onderdeel ${aiHelpEquipmentFilter || 'Alle'}. De echte AI integratie moet nog worden geïmplementeerd.`);
      setAiHelpLoading(false); // Hide loading state
    }, 1500);

    // TODO: Implement actual AI call here using the state variables
    // setAiHelpDialogOpen(false); // Keep dialog open to show response
  };


  return (
    <div className="space-y-6 animate-fade-in">
      <Tabs value={ location.pathname === '/history' ? 'geschiedenis' : '' }>
        <SettingsSubNav />
      </Tabs>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        {/* Stack tabs vertically on mobile, horizontally on larger screens */}
        {/* Desktop Tabs */}
        {!isMobile && (
          <TabsList className="grid w-full grid-cols-4 gap-1 mb-4">
            <TabsTrigger value="rapport" className="text-xs sm:text-sm px-2 py-1.5">Rapport Geschiedenis</TabsTrigger>
            <TabsTrigger value="vk" className="text-xs sm:text-sm px-2 py-1.5">V&K Geschiedenis</TabsTrigger>
            <TabsTrigger value="storingen" className="text-xs sm:text-sm px-2 py-1.5">Storingen Geschiedenis</TabsTrigger>
            <TabsTrigger value="todos" className="text-xs sm:text-sm px-2 py-1.5">Todo Geschiedenis</TabsTrigger>
          </TabsList>
        )}

        {/* Mobile Dropdown */}
        {isMobile && (
          <div className="mb-4">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="w-full justify-between">
                  <span>
                    {activeTab === 'rapport' ? 'Rapport Geschiedenis' :
                     activeTab === 'vk' ? 'V&K Geschiedenis' :
                     activeTab === 'storingen' ? 'Storingen Geschiedenis' :
                     activeTab === 'todos' ? 'Todo Geschiedenis' :
                     'Selecteer Weergave'}
                  </span>
                  <Menu className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-[--radix-dropdown-menu-trigger-width]">
                <DropdownMenuItem onSelect={() => setActiveTab('rapport')}>Rapport Geschiedenis</DropdownMenuItem>
                <DropdownMenuItem onSelect={() => setActiveTab('vk')}>V&K Geschiedenis</DropdownMenuItem>
                <DropdownMenuItem onSelect={() => setActiveTab('storingen')}>Storingen Geschiedenis</DropdownMenuItem>
                <DropdownMenuItem onSelect={() => setActiveTab('todos')}>Todo Geschiedenis</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        )}
        <TabsContent value="rapport">
          <Card className="p-3 bg-gray-50/70 border-dashed mb-6">
            <div className="flex flex-wrap items-end justify-between gap-4">
              <div>
                <Label className="text-xs font-medium mb-1 block text-gray-600">Productielijn</Label>
                <Select value={selectedLine} onValueChange={(v) => setSelectedLine(v as ProductionLine | 'all')}>
                  <SelectTrigger className="h-9 text-xs">
                    <SelectValue placeholder="Selecteer lijn" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Alle lijnen</SelectItem>
                    {['tl1', 'tl2', 'p1', 'p2', 'p3'].map(line => (
                      <SelectItem key={line} value={line}>{line.toUpperCase()}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label className="text-xs font-medium mb-1 block text-gray-600">Datumbereik</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      id="date"
                      variant={"outline"}
                      className={cn(
                        "h-9 w-full justify-start text-left font-normal text-xs min-w-[280px]",
                        !dateRange && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-1.5 h-3.5 w-3.5" />
                      {dateRange?.from ? (
                        dateRange.to ? (
                          <>
                            {format(dateRange.from, "LLL dd, y")} -{" "}
                            {format(dateRange.to, "LLL dd, y")}
                          </>
                        ) : (
                          format(dateRange.from, "LLL dd, y")
                        )
                      ) : (
                        <span>Kies datums</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      initialFocus
                      mode="range"
                      defaultMonth={dateRange?.from}
                      selected={dateRange}
                      onSelect={setDateRange}
                      numberOfMonths={2}
                      locale={nl}
                    />
                  </PopoverContent>
                </Popover>
              </div>

              <div>
                <Label className="text-xs font-medium mb-1 block text-gray-600">Materiaal</Label>
                <Select value={selectedMaterial} onValueChange={(v) => setSelectedMaterial(v)}>
                  <SelectTrigger className="h-9 text-xs">
                    <SelectValue placeholder="Filter materiaal" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Alle materialen</SelectItem>
                    {uniqueMaterials.map(material => (
                      <SelectItem key={material} value={material}>{material}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="relative">
                <Label className="text-xs font-medium mb-1 block text-gray-600">Zoeken</Label>
                <Search className="absolute left-2.5 bottom-2.5 h-3.5 w-3.5 text-gray-400" />
                <Input
                  placeholder="Filter data..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-8 w-full h-9 text-xs"
                />
                {searchQuery && (
                  <Button
                    variant="ghost"
                    size="icon"
                    className="absolute right-1 bottom-1 h-7 w-7"
                    onClick={() => setSearchQuery('')}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                )}
              </div>

              {/* Report Limit Filter */}
              {/* Max Rapporten Select - MOVED HERE */}
              <div className="flex-grow md:flex-grow-0 md:w-[120px]">
                <Label htmlFor="report-limit" className="text-xs font-medium text-gray-600">Max Rapporten</Label>
                <Select value={maxReportsToShow.toString()} onValueChange={(v) => setMaxReportsToShow(Number(v))}>
                  <SelectTrigger id="report-limit" className="h-9 text-sm">
                    <SelectValue placeholder="Aantal" />
                  </SelectTrigger>
                  <SelectContent>
                    {[10, 25, 50, 75, 100, 125, 150, 175, 200, 225, 250].map(num => (
                      <SelectItem key={num} value={num.toString()}>{num}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Export Button */}
              <div className="flex-none flex items-end">
                <Button
                  variant="outline"
                  onClick={handleExport}
                  disabled={filteredData.length === 0} // Keep disabled logic
                  size="icon" // Change size to icon
                  className="h-9 w-9" // Adjust size if needed, remove gap
                  aria-label="Exporteren" // Add accessibility label
                >
                  <Download className="h-4 w-4" /> {/* Adjust icon size if needed */}
                  {/* Removed text "Exporteren" */}
                </Button>
              </div>
            </div>
          </Card>

          <Accordion
            type="multiple"
            value={openAccordionItems}
            onValueChange={setOpenAccordionItems}
            className="w-full space-y-2"
          >
            {filteredData.length === 0 && (
              <p className="text-center text-gray-500 py-10">Geen geschiedenis gevonden voor de geselecteerde filters.</p>
            )}
            {filteredData.map((entry, index) => {
              const totalDisruptions = countTotalDisruptions(entry);
              const displayDatePeriod = formatDateDisplay(entry.startDate, entry.endDate);
              const accordionItemId = `${entry.line}-${entry.endDate}-${index}`;

              return (
                <AccordionItem key={accordionItemId} value={accordionItemId} className="border bg-white rounded-lg shadow-sm px-4">
                  <AccordionTrigger className="text-sm font-medium text-gray-700 hover:no-underline py-3">
                    <div className="flex justify-between items-center w-full pr-2">
                      <span className="flex items-center gap-2">
                        <span className="text-xs px-1.5 py-0.5 rounded bg-blue-100 text-blue-800 font-semibold uppercase">
                          {entry.line}
                        </span>
                        {displayDatePeriod}
                      </span>
                      {totalDisruptions > 0 && (
                        <Badge variant="destructive" className="ml-auto">{totalDisruptions} {totalDisruptions === 1 ? 'Storing' : 'Storingen'}</Badge>
                      )}
                    </div>
                  </AccordionTrigger>
                  <AccordionContent className="px-4 pb-4 pt-2 border-t bg-white">
                    {(() => {
                      const relevantLineData = entry.data[entry.line];
                      const allRows: ProductionRow[] = relevantLineData?.rows || [];
                      const allDisruptions: Disruption[] = relevantLineData?.disruptions || [];

                      // Toon eerst alle storingen in een aparte sectie
                      const disruptionsSection = allDisruptions.length > 0 ? (
                        <div className="mb-6 pt-2">
                          <div className="flex items-center justify-between mb-3">
                            <h4 className="text-base font-semibold text-gray-800">Storingen / Downtime</h4>
                            <Badge variant="destructive">
                              {allDisruptions.length} {allDisruptions.length === 1 ? 'storing' : 'storingen'}
                            </Badge>
                          </div>
                          <div className="grid gap-3">
                            {allDisruptions.map((d, idx) => (
                              <div key={`${d.id || 'disruption'}-${idx}`}
                                   className="bg-red-50 border border-red-100 rounded-lg p-4">
                                <div className="flex justify-between items-start">
                                  <div className="space-y-2">
                                    <p className="text-sm font-medium">{d.description}</p>
                                    <p className="text-xs text-gray-600">
                                      {d.equipment ? `Onderdeel: ${d.equipment}` : ''}
                                      {d.duration ? ` | Duur: ${d.duration}` : ''}
                                    </p>
                                  </div>
                                  {/* Optional: Add more details or actions */}
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      ) : null;

                      // Aggregate and prepare data for daily view
                      const dailyData: { [date: string]: { rows: ProductionRow[], shiftsData: { name: string; production: number }[] } } = {};
                      allRows.forEach(row => {
                        if (!dailyData[row.date]) {
                          dailyData[row.date] = { rows: [], shiftsData: [{ name: 'OD', production: 0 }, { name: 'MD', production: 0 }, { name: 'ND', production: 0 }] };
                        }
                        dailyData[row.date].rows.push(row);
                        dailyData[row.date].shiftsData[0].production += Number(row.od.production) || 0;
                        dailyData[row.date].shiftsData[1].production += Number(row.md.production) || 0;
                        dailyData[row.date].shiftsData[2].production += Number(row.nd.production) || 0;
                      });

                      const sortedDates = Object.keys(dailyData).sort((a, b) => parseISO(b).getTime() - parseISO(a).getTime());

                      const dailyAccordionElement = sortedDates.length > 0 ? (
                        <div className="pt-4">
                          <h4 className="text-base font-semibold text-gray-800 mb-3">Dagelijkse Productie</h4>
                          <Accordion type="single" collapsible className="w-full space-y-2">
                            {sortedDates.map(dateStr => {
                              const { rows: dailyRows, shiftsData: dailyShiftsData } = dailyData[dateStr];
                              let dailyTotalProd = 0;
                              let dailyYieldSum = 0;
                              let dailyYieldCount = 0;

                              dailyRows.forEach(row => {
                                const odProd = Number(row.od.production) || 0;
                                const mdProd = Number(row.md.production) || 0;
                                const ndProd = Number(row.nd.production) || 0;
                                const odYield = Number(row.od.yield) || 0;
                                const mdYield = Number(row.md.yield) || 0;
                                const ndYield = Number(row.nd.yield) || 0;

                                dailyTotalProd += odProd + mdProd + ndProd;
                                if (odYield > 0) { dailyYieldSum += odYield; dailyYieldCount++; }
                                if (mdYield > 0) { dailyYieldSum += mdYield; dailyYieldCount++; }
                                if (ndYield > 0) { dailyYieldSum += ndYield; dailyYieldCount++; }
                              });
                              const dailyAvgYield = dailyYieldCount > 0 ? (dailyYieldSum / dailyYieldCount).toFixed(1) : 'N/A';

                              return (
                                <AccordionItem key={dateStr} value={dateStr} className="border bg-gray-50 rounded-md shadow-sm px-3">
                                  <AccordionTrigger className="text-sm font-medium text-gray-700 hover:no-underline py-2">
                                    <div className="flex justify-between items-center w-full pr-2">
                                      <span>{format(parseISO(dateStr), 'EEEE d MMM yyyy', { locale: nl })}</span>
                                      <span className="text-xs text-gray-500">
                                        Totaal: {dailyTotalProd} | Gem. Yield: {dailyAvgYield}%
                                      </span>
                                    </div>
                                  </AccordionTrigger>
                                  <AccordionContent className="px-3 pb-3 pt-2 border-t bg-white">
                                    <Table className="text-xs mb-3">
                                      <TableHeader>
                                        <TableRow>
                                          <TableHead>Shift</TableHead>
                                          <TableHead>Materiaal</TableHead>
                                          <TableHead className="text-right">Productie</TableHead>
                                          <TableHead className="text-right">Yield (%)</TableHead>
                                        </TableRow>
                                      </TableHeader>
                                      <TableBody>
                                        <TableRow>
                                          <TableCell>Ochtend</TableCell>
                                          <TableCell>{dailyRows[0]?.od.material || '-'}</TableCell>
                                          <TableCell className="text-right">{dailyRows[0]?.od.production || '-'}</TableCell>
                                          <TableCell className="text-right">{dailyRows[0]?.od.yield || '-'}</TableCell>
                                        </TableRow>
                                        <TableRow>
                                          <TableCell>Middag</TableCell>
                                          <TableCell>{dailyRows[0]?.md.material || '-'}</TableCell>
                                          <TableCell className="text-right">{dailyRows[0]?.md.production || '-'}</TableCell>
                                          <TableCell className="text-right">{dailyRows[0]?.md.yield || '-'}</TableCell>
                                        </TableRow>
                                        <TableRow>
                                          <TableCell>Nacht</TableCell>
                                          <TableCell>{dailyRows[0]?.nd.material || '-'}</TableCell>
                                          <TableCell className="text-right">{dailyRows[0]?.nd.production || '-'}</TableCell>
                                          <TableCell className="text-right">{dailyRows[0]?.nd.yield || '-'}</TableCell>
                                        </TableRow>
                                        <TableRow className="bg-gray-100 font-bold">
                                          <TableCell colSpan={2}>Totaal / Gemiddelde</TableCell>
                                          <TableCell className="text-right">{dailyTotalProd}</TableCell>
                                          <TableCell className="text-right">{dailyAvgYield}</TableCell>
                                        </TableRow>
                                      </TableBody>
                                    </Table>
                                    {/* Optional: Add shift bar chart */}
                                    <div className="h-[150px] mt-4">
                                      <ResponsiveContainer width="100%" height="100%">
                                        <RechartsBarChart data={dailyShiftsData} margin={{ top: 5, right: 5, left: -25, bottom: 5 }}>
                                          <CartesianGrid strokeDasharray="3 3" vertical={false} />
                                          <XAxis dataKey="name" fontSize={10} tickLine={false} axisLine={false} />
                                          <YAxis fontSize={10} tickLine={false} axisLine={false} />
                                          <Tooltip contentStyle={{ fontSize: '10px', padding: '2px 5px' }} />
                                          <Bar dataKey="production" fill="#8884d8" name="Productie" radius={[3, 3, 0, 0]} />
                                        </RechartsBarChart>
                                      </ResponsiveContainer>
                                    </div>
                                  </AccordionContent>
                                </AccordionItem>
                              );
                            })}
                          </Accordion>
                        </div>
                      ) : null;

                      return (
                        <>
                          {disruptionsSection}
                          {dailyAccordionElement || <p className="text-sm text-gray-500 italic">Geen productiedata beschikbaar.</p>}
                        </>
                      );
                    })()}
                  </AccordionContent>
                </AccordionItem>
              );
            })}
          </Accordion>
        </TabsContent>

        <TabsContent value="vk">
          <Card className="p-3 bg-gray-50/70 border-dashed mb-6">
             <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 items-end">
               {/* Date Range Filter for VK */}
               <div className="flex-grow min-w-[280px]">
                 <Label className="text-xs font-medium mb-1 block text-gray-600">Datumbereik (V&K)</Label>
                 <Popover>
                   <PopoverTrigger asChild>
                     <Button
                       id="vk-date"
                       variant={"outline"}
                       className={cn(
                         "h-9 w-full justify-start text-left font-normal text-xs",
                         !vkDateRange && "text-muted-foreground"
                       )}
                     >
                       <CalendarIcon className="mr-1.5 h-3.5 w-3.5" />
                       {vkDateRange?.from ? (
                         vkDateRange.to ? (
                           <>
                             {format(vkDateRange.from, "LLL dd, y")} -{" "}
                             {format(vkDateRange.to, "LLL dd, y")}
                           </>
                         ) : (
                           format(vkDateRange.from, "LLL dd, y")
                         )
                       ) : (
                         <span>Alle datums</span>
                       )}
                     </Button>
                   </PopoverTrigger>
                   <PopoverContent className="w-auto p-0" align="start">
                     <Calendar
                       initialFocus
                       mode="range"
                       defaultMonth={vkDateRange?.from}
                       selected={vkDateRange}
                       onSelect={setVkDateRange}
                       numberOfMonths={2}
                       locale={nl}
                     />
                   </PopoverContent>
                 </Popover>
               </div>
               {/* Max items filter */}
               <div className="flex-grow min-w-[150px]">
                 <Label className="text-xs font-medium mb-1 block text-gray-600">Max Items</Label>
                 <Select
                   value={maxVkIncidentsToShow === -1 ? 'all' : maxVkIncidentsToShow.toString()}
                   onValueChange={(v) => setMaxVkIncidentsToShow(v === 'all' ? -1 : parseInt(v))}
                 >
                   <SelectTrigger className="h-9 text-xs">
                     <SelectValue placeholder="Aantal items" />
                   </SelectTrigger>
                   <SelectContent>
                     <SelectItem value="10">Laatste 10</SelectItem>
                     <SelectItem value="25">Laatste 25</SelectItem>
                     <SelectItem value="50">Laatste 50</SelectItem>
                     <SelectItem value="all">Alles</SelectItem>
                   </SelectContent>
                 </Select>
               </div>
               {/* Search Filter */}
               <div className="relative flex-grow min-w-[180px]">
                 <Label className="text-xs font-medium mb-1 block text-gray-600">Zoeken (V&K)</Label>
                 <Search className="absolute left-2.5 bottom-2.5 h-3.5 w-3.5 text-gray-400" />
                 <Input
                   placeholder="Zoek in V&K..."
                   value={vkSearchQuery}
                   onChange={(e) => setVkSearchQuery(e.target.value)}
                   className="pl-8 w-full h-9 text-xs"
                 />
                 {vkSearchQuery && (
                   <Button
                     variant="ghost"
                     size="icon"
                     className="absolute right-1 bottom-1 h-7 w-7"
                     onClick={() => setVkSearchQuery('')}
                   >
                     <X className="h-4 w-4" />
                   </Button>
                 )}
               </div>

               {/* Type Filter */}
               <div className="flex-grow min-w-[150px]">
                 <Label className="text-xs font-medium mb-1 block text-gray-600">Type</Label>
                 <Select value={vkTypeFilter} onValueChange={(v) => setVkTypeFilter(v as 'all' | 'safety' | 'quality')}>
                   <SelectTrigger className="h-9 text-xs">
                     <SelectValue placeholder="Filter type" />
                   </SelectTrigger>
                   <SelectContent>
                     <SelectItem value="all">Alle Types</SelectItem>
                     <SelectItem value="safety">Veiligheid</SelectItem>
                     <SelectItem value="quality">Kwaliteit</SelectItem>
                   </SelectContent>
                 </Select>
               </div>

               {/* Spacer/Reset Button Container */}
               <div className="lg:col-span-1 flex justify-end items-end"> {/* Adjusted span */}
                 <Button variant="outline" size="sm" className="h-9 text-xs" onClick={() => { setVkDateRange(undefined); setMaxVkIncidentsToShow(10); setVkSearchQuery(''); setVkTypeFilter('all'); }}>
                   Reset Filters
                 </Button>
               </div>
             </div>
           </Card>

           <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
             {/* Safety Incidents */}
             <Card>
               <CardHeader>
                 <CardTitle className="text-lg text-red-700">Veiligheid</CardTitle>
               </CardHeader>
               <CardContent>
                 {safetyIncidentsToShow.length === 0 ? (
                   <p className="text-sm text-gray-500 italic">Geen veiligheidsincidenten gevonden.</p>
                 ) : (
                    <Accordion type="multiple" className="w-full space-y-2">
                      {safetyIncidentsToShow.map(incident => (
                         <AccordionItem key={incident.id} value={incident.id} className="border border-red-200 bg-white rounded-lg shadow-sm">
                           <AccordionTrigger className="px-4 py-3 text-sm font-medium hover:no-underline text-left">
                             <div className="flex justify-between items-center w-full">
                               <span className="truncate pr-2">{incident.description}</span>
                               <span className="text-xs text-gray-500 flex-shrink-0">
                                 {formatSingleDate(incident.createdAt || incident.resolveDate)}
                               </span>
                             </div>
                           </AccordionTrigger>
                           <AccordionContent className="px-4 pb-4 pt-2 border-t border-red-100 bg-white rounded-b-lg">
                             <div className="space-y-2 text-xs">
                               <p><span className="font-semibold">Locatie:</span> {incident.location}</p>
                               <p><span className="font-semibold">Oplossing:</span> {incident.solution}</p>
                               <p><span className="font-semibold">Gerapporteerd:</span> {incident.reported}</p>
                               <p><span className="font-semibold">Opvolging:</span> {incident.followup}</p>
                               <p><span className="font-semibold">Verantwoordelijke:</span> {incident.responsible}</p>
                               <p><span className="font-semibold">Oplosdatum:</span> {formatSingleDate(incident.resolveDate)}</p>
                               <p><span className="font-semibold">LTI Gerapporteerd:</span> {incident.ltiReported ? 'Ja' : 'Nee'}</p>
                               <p><span className="font-semibold">Ongeval Gerapporteerd:</span> {incident.accidentReported ? 'Ja' : 'Nee'}</p>
                             </div>
                           </AccordionContent>
                         </AccordionItem>
                      ))}
                    </Accordion>
                  )}
               </CardContent>
             </Card>

             {/* Quality Incidents */}
             <Card>
               <CardHeader>
                 <CardTitle className="text-lg text-green-700">Kwaliteit</CardTitle>
               </CardHeader>
               <CardContent>
                 {qualityIncidentsToShow.length === 0 ? (
                   <p className="text-sm text-gray-500 italic">Geen kwaliteitsincidenten gevonden.</p>
                 ) : (
                     <Accordion type="multiple" className="w-full space-y-2">
                       {qualityIncidentsToShow.map(incident => (
                          <AccordionItem key={incident.id} value={incident.id} className="border border-green-200 bg-white rounded-lg shadow-sm">
                            <AccordionTrigger className="px-4 py-3 text-sm font-medium hover:no-underline text-left">
                              <div className="flex justify-between items-center w-full">
                                <span className="truncate pr-2">{incident.description}</span>
                                <span className="text-xs text-gray-500 flex-shrink-0">
                                  {formatSingleDate(incident.createdAt || incident.resolveDate)}
                                </span>
                              </div>
                            </AccordionTrigger>
                            <AccordionContent className="px-4 pb-4 pt-2 border-t border-green-100 bg-white rounded-b-lg">
                              <div className="space-y-2 text-xs">
                                <p><span className="font-semibold">Locatie:</span> {incident.location}</p>
                                <p><span className="font-semibold">Oplossing:</span> {incident.solution}</p>
                                <p><span className="font-semibold">Gerapporteerd:</span> {incident.reported}</p>
                                <p><span className="font-semibold">Opvolging:</span> {incident.followup}</p>
                                <p><span className="font-semibold">Verantwoordelijke:</span> {incident.responsible}</p>
                                <p><span className="font-semibold">Oplosdatum:</span> {formatSingleDate(incident.resolveDate)}</p>
                                <p><span className="font-semibold">IV Buiten Spec:</span> {incident.ivOutOfSpec ? 'Ja' : 'Nee'}</p>
                                <p><span className="font-semibold">Korrel Buiten Spec:</span> {incident.korrelOutOfSpec ? 'Ja' : 'Nee'}</p>
                                <p><span className="font-semibold">Machine Buiten Spec:</span> {incident.machineOutOfSpec ? 'Ja' : 'Nee'}</p>
                              </div>
                            </AccordionContent>
                          </AccordionItem>
                       ))}
                     </Accordion>
                   )}
               </CardContent>
             </Card>
           </div>
        </TabsContent>

        <TabsContent value="storingen">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between"> {/* Flex layout for title and button */}
              <CardTitle>Gearchiveerde Storingen</CardTitle>
              {/* AI Help Button */}
              <Dialog open={aiHelpDialogOpen} onOpenChange={setAiHelpDialogOpen}>
                <DialogTrigger asChild>
                  <Button variant="outline" size="sm" className="ml-auto">
                    <Sparkles className="h-4 w-4 mr-2 text-purple-500" />
                    AI Help
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-lg"> {/* Increased width */}
                  <DialogHeader>
                    <DialogTitle>AI Assistent voor Storingen</DialogTitle>
                    <DialogDescription>
                      Selecteer criteria of stel een vraag over de gearchiveerde storingen.
                    </DialogDescription>
                  </DialogHeader>
                  {/* AI Help Form Inputs */}
                  <div className="py-4 space-y-4">
                     <div className="grid grid-cols-2 gap-4">
                        <div>
                           <Label htmlFor="ai-help-line" className="text-xs">Lijn</Label>
                           <Select value={aiHelpLineFilter} onValueChange={(v) => setAiHelpLineFilter(v as ProductionLine | 'all')}>
                             <SelectTrigger id="ai-help-line" className="h-9 text-xs w-full">
                               <SelectValue placeholder="Lijn" />
                             </SelectTrigger>
                             <SelectContent>
                               <SelectItem value="all">Alle Lijnen</SelectItem>
                               {lines.map(line => (
                                 <SelectItem key={line} value={line}>{line.toUpperCase()}</SelectItem>
                               ))}
                             </SelectContent>
                           </Select>
                        </div>
                        <div>
                           <Label htmlFor="ai-help-equipment" className="text-xs">Onderdeel</Label>
                           <Combobox
                              options={[{ value: '', label: 'Alle Onderdelen' }, ...aiHelpEquipmentOptions]}
                              value={aiHelpEquipmentFilter}
                              onValueChange={setAiHelpEquipmentFilter}
                              placeholder="Selecteer onderdeel..."
                              searchPlaceholder="Zoek onderdeel..."
                              notFoundMessage="Geen onderdeel gevonden."
                           />
                        </div>
                     </div>
                     <div>
                        <Label htmlFor="ai-help-question" className="text-xs">Vraag</Label>
                        <Textarea
                           id="ai-help-question"
                           placeholder="Stel een vraag over de geselecteerde storingen..."
                           value={aiHelpQuestion}
                           onChange={(e) => setAiHelpQuestion(e.target.value)}
                           rows={3}
                        />
                     </div>
                     {/* Placeholder for AI Response */}
                     {(aiHelpResponse || aiHelpLoading) && (
                       <div className="mt-4 p-3 border rounded bg-gray-50 min-h-[100px]">
                         <Label className="text-xs font-semibold text-gray-600">Antwoord AI:</Label>
                         {aiHelpLoading ? (
                           <p className="text-sm text-gray-500 italic">Aan het denken...</p>
                         ) : (
                           <p className="text-sm whitespace-pre-wrap">{aiHelpResponse}</p>
                         )}
                       </div>
                     )}
                  </div>
                  <DialogFooter>
                    <DialogClose asChild>
                      <Button type="button" variant="secondary">Sluiten</Button>
                    </DialogClose>
                    <Button type="button" onClick={handleAiHelpSubmit} disabled={aiHelpLoading}>
                      {aiHelpLoading ? 'Verwerken...' : 'Vraag AI'}
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </CardHeader>
            <CardContent>
              {/* Filters for Storingen - Updated */}
              <div className="flex flex-wrap gap-2 mb-4 pb-4 border-b items-end">
                 {/* Line Filter */}
                 <div className="flex-grow min-w-[120px]">
                    <Label className="text-xs font-medium mb-1 block text-gray-600">Lijn</Label>
                    <Select value={storingenLineFilter} onValueChange={(v) => setStoringenLineFilter(v as ProductionLine | 'all')}>
                      <SelectTrigger className="h-9 text-xs w-full">
                        <SelectValue placeholder="Lijn" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">Alle Lijnen</SelectItem>
                        {lines.map(line => (
                          <SelectItem key={line} value={line}>{line.toUpperCase()}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                 </div>
                 {/* Equipment Filter */}
                 <div className="flex-grow min-w-[180px]">
                    <Label className="text-xs font-medium mb-1 block text-gray-600">Onderdeel</Label>
                    <Combobox
                       options={[{ value: '', label: 'Alle Onderdelen' }, ...currentEquipmentOptionsForFilter]} // Add 'All' option
                       value={equipmentFilter}
                       onValueChange={setEquipmentFilter}
                       placeholder="Selecteer onderdeel..."
                       searchPlaceholder="Zoek onderdeel..."
                       notFoundMessage="Geen onderdeel gevonden."
                       // Apply width via wrapper if needed, or directly if Combobox supports it
                    />
                 </div>
                 {/* Date Range Filter */}
                 <div className="flex-grow min-w-[240px]">
                    <Label className="text-xs font-medium mb-1 block text-gray-600">Datumbereik</Label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          id="storingen-date"
                          variant={"outline"}
                          className={cn(
                            "h-9 w-full justify-start text-left font-normal text-xs",
                            !storingenDateRange && "text-muted-foreground"
                          )}
                        >
                          <CalendarIcon className="mr-1.5 h-3.5 w-3.5" />
                          {storingenDateRange?.from ? (
                            storingenDateRange.to ? (
                              <>
                                {format(storingenDateRange.from, "LLL dd, y")} -{" "}
                                {format(storingenDateRange.to, "LLL dd, y")}
                              </>
                            ) : (
                              format(storingenDateRange.from, "LLL dd, y")
                            )
                          ) : (
                            <span>Alle Datums</span>
                          )}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          initialFocus
                          mode="range"
                          defaultMonth={storingenDateRange?.from}
                          selected={storingenDateRange}
                          onSelect={setStoringenDateRange}
                          numberOfMonths={2}
                          locale={nl}
                        />
                      </PopoverContent>
                    </Popover>
                 </div>
                 {/* Solution Filter */}
                 <div className="flex-grow min-w-[120px]">
                    <Label className="text-xs font-medium mb-1 block text-gray-600">Oplossing?</Label>
                    <Select value={solutionFilter} onValueChange={(v) => setSolutionFilter(v as YesNoAllFilter)}>
                      <SelectTrigger className="h-9 text-xs w-full">
                        <SelectValue placeholder="Oplossing?" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">Alle</SelectItem>
                        <SelectItem value="yes">Ja</SelectItem>
                        <SelectItem value="no">Nee</SelectItem>
                      </SelectContent>
                    </Select>
                 </div>
                 {/* Removed Completion Date Filter */}
                 {/* Reset Button */}
                 {/* Max Storingen Select */}
                 <div className="flex-grow md:flex-grow-0 md:w-[120px]">
                    <Label htmlFor="disruption-limit" className="text-xs font-medium text-gray-600">Max Storingen</Label>
                    <Select value={maxDisruptionsToShow.toString()} onValueChange={(v) => setMaxDisruptionsToShow(Number(v))}>
                      <SelectTrigger id="disruption-limit" className="h-9 text-sm">
                        <SelectValue placeholder="Aantal" />
                      </SelectTrigger>
                      <SelectContent>
                        {[10, 25, 50, 75, 100, 125, 150, 175, 200, 225, 250].map(num => (
                          <SelectItem key={num} value={num.toString()}>{num}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                 </div>
                 {/* Reset Button */}
                 <Button variant="outline" size="sm" className="h-9 text-xs" onClick={() => { setStoringenLineFilter('all'); setStoringenDateRange(undefined); setSolutionFilter('all'); /* setCompletionDateFilter('all'); */ setEquipmentFilter(''); setMaxDisruptionsToShow(10); }}>
                   Reset Filters
                 </Button>
              </div>

              {/* Disruption History List */}
              {filteredDisruptionHistory.length === 0 ? (
                <p className="text-center text-gray-500 italic py-8">Geen gearchiveerde storingen gevonden voor de geselecteerde filters.</p>
              ) : (
                <div className="space-y-3">
                  {filteredDisruptionHistory.map((d) => (
                    <Collapsible
                      key={d.id}
                      className="border rounded-lg bg-white shadow-sm"
                    >
                      <CollapsibleTrigger asChild>
                        <Button variant="ghost" className="w-full flex justify-between items-center px-4 py-3 text-left h-auto">
                          <div className="flex-grow">
                            <span className={`inline-block px-1.5 py-0.5 text-xs ${getLineColorClass(d.line, 'bg')} ${getLineColorClass(d.line, 'text')} rounded uppercase font-medium mr-2`}>{d.line}</span>
                            <span className="text-sm font-medium mr-2">{d.description}</span>
                            {d.equipment && <span className="text-xs text-gray-500">({d.equipment})</span>}
                          </div>
                          <div className="flex items-center gap-2 flex-shrink-0 ml-4">
                             <span className="text-xs text-gray-500">{formatSingleDate(d.resolvedAt || d.createdAt, true)}</span>
                             <ChevronDown className="h-4 w-4 transition-transform duration-200 group-data-[state=open]:rotate-180" />
                          </div>
                        </Button>
                      </CollapsibleTrigger>
                      <CollapsibleContent className="p-4 space-y-3 border-t border-gray-100 bg-gray-50/50">
                        {/* Standard Details */}
                        {d.rootCause && <div><Label className="text-xs font-semibold">Kernoorzaak:</Label><p className="text-sm">{d.rootCause}</p></div>}
                        {d.actions && <div><Label className="text-xs font-semibold">Acties:</Label><p className="text-sm">{d.actions}</p></div>}
                        {d.oplossingen && <div><Label className="text-xs font-semibold">Oplossing:</Label><p className="text-sm">{d.oplossingen}</p></div>}
                        {d.actionOwner && <div><Label className="text-xs font-semibold">Actiehouder:</Label><p className="text-sm">{d.actionOwner}</p></div>}
                        {d.lastUpdate && <div><Label className="text-xs font-semibold">Update door:</Label><p className="text-sm">{d.lastUpdate}</p></div>}
                        {d.resolveDate && <div><Label className="text-xs font-semibold">Leverdatum:</Label><p className="text-sm">{formatSingleDate(d.resolveDate)}</p></div>}
                        {/* Removed Gereedmelddatum display */}

                        {/* Display RCA Data Array */}
                        {d.rootCauseAnalysis && d.rootCauseAnalysis.length > 0 && (
                          <div className="pt-3 mt-3 border-t border-gray-200">
                            <h5 className="text-sm font-semibold mb-2 text-gray-700">Root Cause Analyses:</h5>
                            {d.rootCauseAnalysis.map((rcaEntry) => ( // Use rcaEntry.id as key
                               <div key={rcaEntry.id} className="p-3 border rounded bg-white mb-2 shadow-sm">
                                 <p className="text-xs text-gray-500 mb-2 font-medium">Analyse van: {formatSingleDate(rcaEntry.timestamp, true)}</p>
                                 <div className="space-y-1 text-xs">
                                   {rcaEntry.who && <p><span className="font-medium">Wie:</span> {rcaEntry.who}</p>}
                                   {rcaEntry.what && <p><span className="font-medium">Wat:</span> {rcaEntry.what}</p>}
                                   {rcaEntry.where && <p><span className="font-medium">Waar:</span> {rcaEntry.where}</p>}
                                   {rcaEntry.how && <p><span className="font-medium">Hoe:</span> {rcaEntry.how}</p>}
                                   {rcaEntry.followUp && <p><span className="font-medium">Vervolg:</span> {rcaEntry.followUp}</p>}
                                   {/* Removed RCA Solution display */}
                                 </div>
                               </div>
                            ))}
                          </div>
                        )}
                      </CollapsibleContent>
                    </Collapsible>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Todo History Tab */}
        <TabsContent value="todos">
          <Card>
            <CardHeader>
              <CardTitle>Voltooide Taken</CardTitle>
            </CardHeader>
            <CardContent>
              {/* Filters for Todo History */}
              <div className="flex flex-wrap gap-2 mb-4 pb-4 border-b items-end">
                {/* Date Range Filter */}
                <div className="flex-grow min-w-[240px]">
                  <Label className="text-xs font-medium mb-1 block text-gray-600">Datumbereik</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        id="todo-date"
                        variant={"outline"}
                        className={cn(
                          "h-9 w-full justify-start text-left font-normal text-xs",
                          !todoDateRange && "text-muted-foreground"
                        )}
                      >
                        <CalendarIcon className="mr-1.5 h-3.5 w-3.5" />
                        {todoDateRange?.from ? (
                          todoDateRange.to ? (
                            <>
                              {format(todoDateRange.from, "LLL dd, y")} -{" "}
                              {format(todoDateRange.to, "LLL dd, y")}
                            </>
                          ) : (
                            format(todoDateRange.from, "LLL dd, y")
                          )
                        ) : (
                          <span>Alle Datums</span>
                        )}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        initialFocus
                        mode="range"
                        defaultMonth={todoDateRange?.from}
                        selected={todoDateRange}
                        onSelect={setTodoDateRange}
                        numberOfMonths={2}
                        locale={nl}
                      />
                    </PopoverContent>
                  </Popover>
                </div>

                {/* Search Filter */}
                <div className="relative flex-grow min-w-[180px]">
                  <Label className="text-xs font-medium mb-1 block text-gray-600">Zoeken</Label>
                  <Search className="absolute left-2.5 bottom-2.5 h-3.5 w-3.5 text-gray-400" />
                  <Input
                    placeholder="Zoek in todos..."
                    value={todoSearchQuery}
                    onChange={(e) => setTodoSearchQuery(e.target.value)}
                    className="pl-8 w-full h-9 text-xs"
                  />
                  {todoSearchQuery && (
                    <Button
                      variant="ghost"
                      size="icon"
                      className="absolute right-1 bottom-1 h-7 w-7"
                      onClick={() => setTodoSearchQuery('')}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  )}
                </div>

                {/* Team Filter */}
                <div className="flex-grow md:flex-grow-0 md:w-[150px]">
                  <Label htmlFor="todo-team" className="text-xs font-medium text-gray-600">Ploeg</Label>
                  <Select
                    value={todoTeamFilter}
                    onValueChange={setTodoTeamFilter}
                  >
                    <SelectTrigger id="todo-team" className="h-9 text-sm">
                      <SelectValue placeholder="Alle ploegen" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Alle ploegen</SelectItem>
                      <SelectItem value="Blauw">Blauw</SelectItem>
                      <SelectItem value="Wit">Wit</SelectItem>
                      <SelectItem value="Geel">Geel</SelectItem>
                      <SelectItem value="Groen">Groen</SelectItem>
                      <SelectItem value="Rood">Rood</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Max Items Filter */}
                <div className="flex-grow md:flex-grow-0 md:w-[120px]">
                  <Label htmlFor="todo-limit" className="text-xs font-medium text-gray-600">Max Items</Label>
                  <Select
                    value={maxTodosToShow.toString()}
                    onValueChange={(v) => setMaxTodosToShow(parseInt(v))}
                  >
                    <SelectTrigger id="todo-limit" className="h-9 text-sm">
                      <SelectValue placeholder="Aantal" />
                    </SelectTrigger>
                    <SelectContent>
                      {[10, 25, 50, 75, 100].map(num => (
                        <SelectItem key={num} value={num.toString()}>{num}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Reset Button */}
                <Button variant="outline" size="sm" className="h-9 text-xs" onClick={() => {
                  setTodoDateRange(undefined);
                  setTodoSearchQuery('');
                  setTodoTeamFilter('all');
                  setMaxTodosToShow(25);
                }}>
                  Reset Filters
                </Button>
              </div>

              {/* Todo List */}
              {filteredTodos.length === 0 ? (
                <p className="text-center text-gray-500 italic py-8">Geen voltooide taken gevonden voor de geselecteerde filters.</p>
              ) : (
                <div className="space-y-3">
                  {filteredTodos.map((todo) => (
                    <Collapsible
                      key={todo.id}
                      className="border rounded-lg bg-white shadow-sm"
                    >
                      <CollapsibleTrigger asChild>
                        <Button variant="ghost" className="w-full flex justify-between items-center px-4 py-3 text-left h-auto">
                          <div className="flex-grow flex items-center gap-2">
                            <CheckCircle className="h-4 w-4 text-green-600" />
                            <span className="text-sm font-medium">{todo.title}</span>
                            {todo.priority && (
                              <Badge variant={todo.priority === 'high' ? 'destructive' : todo.priority === 'medium' ? 'default' : 'outline'} className="ml-2">
                                {todo.priority === 'high' ? 'Hoog' : todo.priority === 'medium' ? 'Middel' : 'Laag'}
                              </Badge>
                            )}
                          </div>
                          <div className="flex items-center gap-2 flex-shrink-0 ml-4">
                            <span className="text-xs text-gray-500">{formatTodoDate(todo.completed_at)}</span>
                            <ChevronDown className="h-4 w-4 transition-transform duration-200 group-data-[state=open]:rotate-180" />
                          </div>
                        </Button>
                      </CollapsibleTrigger>
                      <CollapsibleContent className="p-4 space-y-3 border-t border-gray-100 bg-gray-50/50">
                        {/* Todo Details */}
                        {todo.description && (
                          <div>
                            <Label className="text-xs font-semibold">Beschrijving:</Label>
                            <p className="text-sm whitespace-pre-wrap">{todo.description}</p>
                          </div>
                        )}

                        {todo.comments && (
                          <div>
                            <Label className="text-xs font-semibold">Opmerkingen:</Label>
                            <p className="text-sm whitespace-pre-wrap">{todo.comments}</p>
                          </div>
                        )}

                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-3">
                          <div className="space-y-2">
                            <div>
                              <Label className="text-xs font-semibold">Toegewezen aan teams:</Label>
                              <div className="flex flex-wrap gap-1 mt-1">
                                {todo.assigned_teams && todo.assigned_teams.length > 0 ? (
                                  todo.assigned_teams.map((team, index) => (
                                    <Badge key={index} variant="outline" className="text-xs">{team}</Badge>
                                  ))
                                ) : (
                                  <span className="text-sm text-gray-500">Geen teams</span>
                                )}
                              </div>
                            </div>

                            {todo.due_date && (
                              <div>
                                <Label className="text-xs font-semibold">Deadline:</Label>
                                <p className="text-sm">{format(new Date(todo.due_date), 'dd MMM yyyy', { locale: nl })}</p>
                              </div>
                            )}
                          </div>

                          <div className="space-y-2">
                            <div>
                              <Label className="text-xs font-semibold">Aangemaakt op:</Label>
                              <p className="text-sm">{formatTodoDate(todo.created_at)}</p>
                            </div>

                            <div>
                              <Label className="text-xs font-semibold">Voltooid op:</Label>
                              <p className="text-sm">{formatTodoDate(todo.completed_at)}</p>
                            </div>
                          </div>
                        </div>
                      </CollapsibleContent>
                    </Collapsible>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default HistoryV2;