// Corrected src/pages/Materials.tsx
import React from 'react';
import { Outlet, useLocation } from 'react-router-dom'; // Added useLocation
import { Tabs, TabsContent } from '@/components/ui/tabs'; // Re-added Tabs and added TabsContent
// Removed import for SettingsSubNav
import { MaterialsSubNav } from '@/components/materials/MaterialsSubNav';

const Materials: React.FC = () => {
  const location = useLocation();

  // Determine the default tab based on the current nested route
  const getDefaultTab = () => {
    if (location.pathname.endsWith('/comparison')) {
      return 'comparison';
    }
    return 'management'; // Default to management otherwise
  };

  return (
    <div className="p-6 space-y-6">
      {/* Re-added Tabs wrapper */}
      <Tabs defaultValue={getDefaultTab()} className="w-full">
        {/* Render the sub-navigation */}
        <MaterialsSubNav />
        {/* Render the active nested route's component within TabsContent */}
        {/* Note: We might not strictly need TabsContent here if <PERSON><PERSON> handles everything,
             but it aligns with the typical structure of using Tabs/TabsList/TabsContent */}
        <TabsContent value="management"> {/* Content for management tab */}
           <div className="mt-6">
             <Outlet />
           </div>
        </TabsContent>
        <TabsContent value="comparison"> {/* Content for comparison tab */}
           <div className="mt-6">
             <Outlet />
           </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Materials;
