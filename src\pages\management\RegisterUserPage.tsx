// src/pages/management/RegisterUserPage.tsx
import React, { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase-client';
import { useAuth } from '@/context/AuthContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { toast } from 'sonner';
import { UserPlus, Loader2, ShieldAlert, Users } from 'lucide-react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

const RegisterUserPage: React.FC = () => {
  const { user } = useAuth();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isManager, setIsManager] = useState(false);
  const [selectedTeam, setSelectedTeam] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [isCheckingAuth, setIsCheckingAuth] = useState(true);

  // Beschikbare ploegen
  const teams = ['Blauw', 'Wit', 'Geel', 'Groen', 'Rood'];

  // Controleer of de huidige gebruiker een beheerder is
  useEffect(() => {
    const checkAuthorization = async () => {
      if (!user) {
        console.log('Geen gebruiker gevonden, autorisatie mislukt');
        setIsAuthorized(false);
        setIsCheckingAuth(false);
        return;
      }

      try {
        console.log('Controleren of gebruiker een beheerder is:', user.id);
        const { data, error } = await supabase
          .from('authorized_managers')
          .select('user_id')
          .eq('user_id', user.id)
          .maybeSingle();

        if (error) {
          console.error('Fout bij controleren van autorisatie:', error);
          toast.error(`Fout bij controleren van autorisatie: ${error.message}`);
          setIsAuthorized(false);
        } else {
          console.log('Autorisatie resultaat:', data);
          setIsAuthorized(!!data);
        }
      } catch (err) {
        console.error('Onverwachte fout bij autorisatiecontrole:', err);
        toast.error('Onverwachte fout bij autorisatiecontrole');
        setIsAuthorized(false);
      } finally {
        setIsCheckingAuth(false);
      }
    };

    checkAuthorization();
  }, [user]);

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();

    // Controleer of wachtwoorden overeenkomen
    if (password !== confirmPassword) {
      toast.error('Wachtwoorden komen niet overeen');
      return;
    }

    setIsLoading(true);
    setSuccessMessage('');

    try {
      // 1. Registreer de gebruiker met de standaard Supabase signup methode
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            registered_by: user?.email || 'admin',
            registration_date: new Date().toISOString()
          }
        }
      });

      if (authError) {
        throw new Error(`Fout bij registreren: ${authError.message}`);
      }

      if (!authData.user) {
        throw new Error('Gebruiker kon niet worden aangemaakt');
      }

      // 2. Bevestig de e-mail van de gebruiker handmatig via SQL
      const { error: confirmError } = await supabase.rpc('confirm_user_email_v2', {
        user_email: email
      });

      if (confirmError) {
        console.warn('Kon e-mail niet handmatig bevestigen, maar gebruiker is wel aangemaakt:', confirmError);
        // We gaan door, omdat de gebruiker wel is aangemaakt
      }

      // 3. Als isManager is aangevinkt, voeg de gebruiker toe aan de authorized_managers tabel
      if (isManager) {
        const { error: managerError } = await supabase
          .from('authorized_managers')
          .insert([{ user_id: authData.user.id }]);

        if (managerError) {
          throw new Error(`Gebruiker aangemaakt, maar kon niet als beheerder worden ingesteld: ${managerError.message}`);
        }
      }

      // 4. Als een ploeg is geselecteerd (en niet 'none'), voeg de gebruiker toe aan de user_teams tabel
      if (selectedTeam && selectedTeam !== 'none') {
        const { error: teamError } = await supabase
          .from('user_teams')
          .insert([{ user_id: authData.user.id, team: selectedTeam }]);

        if (teamError) {
          throw new Error(`Gebruiker aangemaakt, maar kon niet aan ploeg worden toegewezen: ${teamError.message}`);
        }
      }

      // Succes!
      let successMsg = `Gebruiker ${email} succesvol geregistreerd`;
      if (isManager) successMsg += ' als beheerder';
      if (selectedTeam && selectedTeam !== 'none') successMsg += ` in ploeg ${selectedTeam}`;
      successMsg += '! De gebruiker kan direct inloggen zonder e-mailbevestiging.';

      setSuccessMessage(successMsg);
      toast.success('Gebruiker succesvol geregistreerd');

      // Reset het formulier
      setEmail('');
      setPassword('');
      setConfirmPassword('');
      setIsManager(false);
      setSelectedTeam('');
    } catch (error) {
      console.error('Registratiefout:', error);
      toast.error(error instanceof Error ? error.message : 'Er is een fout opgetreden bij het registreren');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="max-w-md mx-auto">
      <h1 className="text-2xl font-bold mb-4">Nieuwe Gebruiker Registreren</h1>
      <p className="mb-6">Hier kunt u een nieuwe gebruiker registreren voor de applicatie.</p>

      {isCheckingAuth ? (
        <div className="flex justify-center items-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-3">Autorisatie controleren...</span>
        </div>
      ) : !isAuthorized ? (
        <Card className="border-red-200 bg-red-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-red-700">
              <ShieldAlert className="h-5 w-5" />
              Geen toegang
            </CardTitle>
            <CardDescription className="text-red-600">
              U heeft geen beheerdersrechten om nieuwe gebruikers te registreren.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-red-600">
              Neem contact op met een beheerder om toegang te krijgen tot deze functie.
            </p>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <UserPlus className="h-5 w-5" />
              Registratieformulier
            </CardTitle>
            <CardDescription>
              Vul de gegevens in om een nieuwe gebruiker aan te maken. De gebruiker kan direct inloggen zonder e-mailbevestiging.
            </CardDescription>
          </CardHeader>

          <form onSubmit={handleRegister}>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">E-mailadres</Label>
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="<EMAIL>"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="password">Wachtwoord</Label>
              <Input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Wachtwoord"
                required
                minLength={6}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="confirmPassword">Bevestig wachtwoord</Label>
              <Input
                id="confirmPassword"
                type="password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                placeholder="Bevestig wachtwoord"
                required
                minLength={6}
              />
            </div>

            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="isManager"
                  checked={isManager}
                  onCheckedChange={(checked) => {
                    const isChecked = checked === true;
                    setIsManager(isChecked);
                    // Als beheerder wordt aangevinkt, zet team automatisch op 'none'
                    if (isChecked) {
                      setSelectedTeam('none');
                    }
                  }}
                />
                <Label htmlFor="isManager" className="cursor-pointer">Gebruiker is beheerder</Label>
              </div>

              <div className="space-y-2">
                <Label htmlFor="team" className="flex items-center gap-1">
                  <Users className="h-4 w-4" />
                  Ploeg
                </Label>
                <Select value={selectedTeam} onValueChange={setSelectedTeam}>
                  <SelectTrigger id="team" className="w-full">
                    <SelectValue placeholder="Selecteer een ploeg" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">{isManager ? "Geen specifieke ploeg (beheerder)" : "Selecteer een ploeg"}</SelectItem>
                    {teams.map(team => (
                      <SelectItem key={team} value={team}>{team}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground">
                  {isManager ?
                    "Beheerders hebben toegang tot alle ploegen. Selectie van een ploeg is optioneel." :
                    "Gebruikers kunnen alleen acties uitvoeren voor hun eigen ploeg"}
                </p>
              </div>
            </div>
          </CardContent>

          <CardFooter className="flex justify-between">
            <Button type="button" variant="outline" onClick={() => {
              setEmail('');
              setPassword('');
              setConfirmPassword('');
              setIsManager(false);
              setSelectedTeam('');
            }}>
              Annuleren
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Bezig met registreren...
                </>
              ) : (
                'Registreren'
              )}
            </Button>
          </CardFooter>
        </form>
      </Card>
      )}

      {successMessage && (
        <div className="mt-4 p-4 border border-green-400 bg-green-50 text-green-700 rounded-md">
          {successMessage}
        </div>
      )}
    </div>
  );
};

export default RegisterUserPage;