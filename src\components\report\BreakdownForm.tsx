import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { v4 as uuidv4 } from 'uuid'; // Import uuid voor unieke IDs
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { ChevronsUpDown, Plus, Trash } from 'lucide-react';
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command";
import { BreakdownEntry, EquipmentEntry, ProductionRow, ProductionLine } from '@/types';

interface BreakdownFormProps {
  selectedLine: ProductionLine;
  equipmentOptions: Record<string, Record<string, EquipmentEntry[]>>;
  activeRow: ProductionRow | null;
  updateProductionRow: (line: ProductionLine, date: string, updatedRow: ProductionRow) => void;
}

const BreakdownForm: React.FC<BreakdownFormProps> = ({
  selectedLine,
  equipmentOptions,
  activeRow,
  updateProductionRow
}) => {
  const [currentBreakdowns, setCurrentBreakdowns] = useState<BreakdownEntry[]>([]);
  const [popoverOpen, setPopoverOpen] = useState<Record<string, boolean>>({});
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [directInput, setDirectInput] = useState<Record<string, string>>({});

  useEffect(() => {
    if (activeRow && activeRow.breakdowns) {
      const breakdownsWithId = activeRow.breakdowns.map(b => ({
        ...b,
        id: b.id || uuidv4()
      }));
      setCurrentBreakdowns(breakdownsWithId);
    } else {
      setCurrentBreakdowns([]);
    }
    setPopoverOpen({});
  }, [activeRow]);

  const filteredEquipment = useMemo(() => {
    const lineOptions = equipmentOptions[selectedLine] || {};
    if (!searchQuery) {
      return lineOptions;
    }
    const lowercaseQuery = searchQuery.toLowerCase();
    const filtered: Record<string, EquipmentEntry[]> = {};
    Object.entries(lineOptions).forEach(([area, options]) => {
      const matchingOptions = options.filter(
        option => 
          option.label_nl.toLowerCase().includes(lowercaseQuery) ||
          option.value.toLowerCase().includes(lowercaseQuery)
      );
      if (matchingOptions.length > 0) {
        filtered[area] = matchingOptions;
      }
    });
    return filtered;
  }, [searchQuery, equipmentOptions, selectedLine]);

  const handleUpdateBreakdown = useCallback((id: string, field: keyof BreakdownEntry, value: string) => {
    if (!activeRow) return;

    let finalValue = value;

    if (field === 'duration') {
        const digitsAndColon = value.replace(/[^0-9:]/g, '');
        const colonIndex = digitsAndColon.indexOf(':');
        let processedValue = digitsAndColon;
        if (colonIndex !== -1) {
            processedValue = digitsAndColon.substring(0, colonIndex + 1) + digitsAndColon.substring(colonIndex + 1).replace(/:/g, '');
        }
        const parts = processedValue.split(':');
        let hoursStr = parts[0] || '';
        let minutesStr = parts[1] !== undefined ? parts[1] : '';
        hoursStr = hoursStr.slice(0, 2);
        minutesStr = minutesStr.slice(0, 2);
        if (colonIndex !== -1) {
             finalValue = `${hoursStr}:${minutesStr}`;
        } else {
             finalValue = hoursStr;
        }
         if (finalValue.length > 5) finalValue = finalValue.slice(0,5);
         
         const originalBreakdown = currentBreakdowns.find(b => b.id === id);
         if(originalBreakdown && originalBreakdown.duration === finalValue) {
             return; 
         }
    }

    const updatedBreakdowns = currentBreakdowns.map(b =>
        b.id === id ? { ...b, [field]: finalValue } : b
    );
    
    setCurrentBreakdowns(updatedBreakdowns);
    
    updateProductionRow(selectedLine, activeRow.date, { 
      ...activeRow, 
      breakdowns: updatedBreakdowns 
    });

  }, [currentBreakdowns, activeRow, selectedLine, updateProductionRow]);

  const handlePopoverOpenChange = useCallback((id: string, isOpen: boolean) => {
    setPopoverOpen(prev => ({ ...prev, [id]: isOpen }));
    if (!isOpen) {
      const currentBreakdown = currentBreakdowns.find(b => b.id === id);
      const currentDirectInput = directInput[id];
      if (activeRow && currentDirectInput && currentBreakdown && currentBreakdown.equipment !== currentDirectInput) {
        const existsInOptions = Object.values(equipmentOptions[selectedLine] || {}).flat().some(opt => opt.value === currentDirectInput);
        if (!existsInOptions) {
          handleUpdateBreakdown(id, 'equipment', currentDirectInput); 
        } 
      }
      setDirectInput(prev => { const newState = {...prev}; delete newState[id]; return newState; });
      setSearchQuery('');
    }
  }, [currentBreakdowns, directInput, equipmentOptions, selectedLine, handleUpdateBreakdown, activeRow]);

  const handleSearchQueryChange = useCallback((id: string, query: string) => {
    setSearchQuery(query);
    setDirectInput(prev => ({ ...prev, [id]: query }));
  }, []);

  const handleAddBreakdown = useCallback(() => {
    if (!activeRow) return;

    const newBreakdown: BreakdownEntry = {
      id: uuidv4(),
      equipment: '',
      duration: '00:00',
      description: '',
    };
    const updatedBreakdowns = [...currentBreakdowns, newBreakdown];
    
    setCurrentBreakdowns(updatedBreakdowns);
    
    updateProductionRow(selectedLine, activeRow.date, { 
      ...activeRow, 
      breakdowns: updatedBreakdowns 
    });
  }, [currentBreakdowns, activeRow, selectedLine, updateProductionRow]);

  const handleRemoveBreakdown = useCallback((id: string) => {
    if (!activeRow) return;

    const updatedBreakdowns = currentBreakdowns.filter(b => b.id !== id);
    
    setCurrentBreakdowns(updatedBreakdowns);
    
    updateProductionRow(selectedLine, activeRow.date, { 
      ...activeRow, 
      breakdowns: updatedBreakdowns 
    });
  }, [currentBreakdowns, activeRow, selectedLine, updateProductionRow]);

  const calculateTotalDowntime = useMemo(() => {
    return currentBreakdowns.reduce((total, breakdown) => {
      const timeComponents = breakdown.duration.split(':');
      if (timeComponents.length === 2) {
        const hours = parseInt(timeComponents[0], 10) || 0;
        const minutes = parseInt(timeComponents[1], 10) || 0;
        return total + hours * 60 + minutes;
      }
      return total;
    }, 0);
  }, [currentBreakdowns]);

  const formatMinutes = (totalMinutes: number): string => {
    const hours = Math.floor(totalMinutes / 60);
    const minutes = totalMinutes % 60;
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
  };

  if (!activeRow) {
    return <div className="dashboard-card p-4 mt-6 text-center text-gray-500">Selecteer een dag om storingen in te voeren.</div>;
  }

  return (
    <div className="dashboard-card p-4 mt-6">
      <div className="flex justify-between items-center mb-4 border-b pb-2">
         <h2 className="text-lg font-semibold">Storingen / Downtime ({formatMinutes(calculateTotalDowntime)})</h2>
         <Button size="sm" variant="outline" onClick={handleAddBreakdown} >
             <Plus className="h-4 w-4 mr-1" /> Storing Toevoegen
         </Button>
      </div>
      <div className="space-y-4">
        {currentBreakdowns.map((breakdown) => (
          <div key={breakdown.id} className="border rounded p-3 bg-gray-50/50 relative"> 
            <Button 
              variant="ghost" 
              size="icon"
              className="absolute top-1 right-1 h-6 w-6 text-red-500 hover:bg-red-100"
              onClick={() => handleRemoveBreakdown(breakdown.id)}
            >
                <Trash className="h-4 w-4" />
            </Button>
            <div className="grid grid-cols-12 gap-6">
              <div className="col-span-1">
                <Label htmlFor={`breakdown-${breakdown.id}-duration`}>Downtime</Label>
                <Input
                  type="text"
                  id={`breakdown-${breakdown.id}-duration`}
                  placeholder="00:00"
                  value={breakdown.duration}
                  onChange={(e) => handleUpdateBreakdown(breakdown.id, 'duration', e.target.value)}
                  className="mt-1 w-[90%]"
                />
              </div>
              <div className="col-span-3">
                <Label htmlFor={`breakdown-${breakdown.id}-equipment`}>Onderdeel</Label>
                <Popover 
                  open={popoverOpen[breakdown.id]} 
                  onOpenChange={(open) => handlePopoverOpenChange(breakdown.id, open)}
                >
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      role="combobox"
                      aria-expanded={popoverOpen[breakdown.id]}
                      className="w-full justify-between mt-1"
                    >
                      {popoverOpen[breakdown.id] && directInput[breakdown.id] 
                        ? directInput[breakdown.id]
                        : breakdown.equipment
                          ? Object.values(equipmentOptions[selectedLine] || {}).flat().find(opt => opt.value === breakdown.equipment)?.label_nl || breakdown.equipment
                          : "Selecteer of typ onderdeel..."}
                      <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-[300px] p-0">
                    <Command>
                      <CommandInput 
                        placeholder="Zoek of typ onderdeel..." 
                        value={searchQuery || directInput[breakdown.id] || ''}
                        onValueChange={(query) => handleSearchQueryChange(breakdown.id, query)}
                      />
                      <CommandList>
                        <CommandEmpty>{"Geen bestaande onderdelen gevonden."}</CommandEmpty>
                        {Object.keys(filteredEquipment).length === 0 && searchQuery && !directInput[breakdown.id] ? (
                            <div className="p-4 text-sm text-muted-foreground">Geen resultaten voor "{searchQuery}".</div>
                        ) : (
                            Object.entries(filteredEquipment).map(([area, options]) => (
                              <CommandGroup key={area} heading={area}>
                                {options.map((option) => (
                                  <CommandItem
                                    key={option.value}
                                    value={option.value}
                                    onSelect={(currentValue) => {
                                      const selectedOption = Object.values(equipmentOptions[selectedLine] || {}).flat().find(opt => opt.value === currentValue);
                                      const displayValue = selectedOption ? selectedOption.label_nl : currentValue;
                                      handleUpdateBreakdown(breakdown.id, 'equipment', currentValue);
                                      setDirectInput(prev => ({...prev, [breakdown.id]: displayValue}));
                                      setPopoverOpen(prev => ({ ...prev, [breakdown.id]: false }));
                                      setSearchQuery('');
                                    }}
                                  >
                                    {option.label_nl}
                                  </CommandItem>
                                ))}
                              </CommandGroup>
                            ))
                        )}
                      </CommandList>
                    </Command>
                  </PopoverContent>
                </Popover>
              </div>
              <div className="col-span-6">
                <Label htmlFor={`breakdown-${breakdown.id}-description`}>Pareto</Label>
                <Textarea
                  id={`breakdown-${breakdown.id}-description`}
                  value={breakdown.description || ''}
                  onChange={(e) => handleUpdateBreakdown(breakdown.id, 'description', e.target.value)}
                  className="mt-1 w-[90%]"
                  rows={1}
                />
              </div>
            </div>
          </div>
        ))}
        {currentBreakdowns.length === 0 && (
            <div className="text-center text-gray-500 py-4">Nog geen storingen toegevoegd voor deze dag.</div>
        )}
      </div>
    </div>
  );
};

export default BreakdownForm;
