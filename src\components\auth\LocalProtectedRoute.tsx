// src/components/auth/LocalProtectedRoute.tsx
import React from 'react';
import { Navigate } from 'react-router-dom';
import { useLocalAuth } from '@/context/LocalAuthContext';
import { Loader2 } from 'lucide-react';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireManager?: boolean;
}

const LocalProtectedRoute: React.FC<ProtectedRouteProps> = ({ 
  children, 
  requireManager = false 
}) => {
  const { user, loading } = useLocalAuth();

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
        <p className="ml-2">Laden...</p>
      </div>
    );
  }

  if (!user) {
    return <Navigate to="/auth" replace />;
  }

  if (requireManager && !user.isManager) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4">
        <h1 className="text-2xl font-bold mb-2">Geen toegang</h1>
        <p className="text-center mb-4">
          U heeft geen toestemming om deze pagina te bekijken. Alleen beheerders hebben toegang.
        </p>
        <Navigate to="/" replace />
      </div>
    );
  }

  return <>{children}</>;
};

export default LocalProtectedRoute;
