import React from 'react';
import { format } from 'date-fns';
import { nl } from 'date-fns/locale';
import { Button } from '@/components/ui/button';
import { Trash, Edit } from 'lucide-react';
import { ProductionLine, ProductionRow } from '@/types';

interface DaySelectorProps {
  lineData: {
    rows: ProductionRow[];
  };
  activeRowIndex: number;
  onRowChange: (index: number) => void;
  onAddDay?: () => void;
  onDeleteDay: (line: ProductionLine, date: string, index: number, event: React.MouseEvent) => void;
  onEditDay: (index: number) => void;
  selectedLine: ProductionLine;
}

const DaySelector: React.FC<DaySelectorProps> = ({
  lineData,
  activeRowIndex,
  onRowChange,
  onDeleteDay,
  onEditDay,
  selectedLine
}) => {
  return (
    <div className="space-y-2 pr-2">
      {lineData.rows && lineData.rows.map((row, index) => (
        <Button
          key={row.date + '-' + index}
          variant="outline"
          className={`w-full justify-start text-left h-auto py-2 flex items-center justify-between group ${index === activeRowIndex ? 'border-b-2 border-primary' : ''}`}
          onClick={() => onRowChange(index)}
        >
          <span>
            {format(new Date(row.date), 'dd-MM-yyyy', { locale: nl })}
          </span>
          <div className="flex items-center opacity-0 group-hover:opacity-100 transition-opacity">
            <div 
              className="h-7 w-7 mr-1 text-blue-600 hover:bg-blue-100 rounded-md flex items-center justify-center cursor-pointer"
              onClick={(e) => {
                e.stopPropagation();
                onEditDay(index);
              }}
            >
              <Edit className="h-4 w-4"/>
            </div>
            <div 
              className="h-7 w-7 text-red-500 hover:bg-red-100 rounded-md flex items-center justify-center cursor-pointer"
              onClick={(e) => {
                e.stopPropagation();
                onDeleteDay(selectedLine, row.date, index, e);
              }}
            >
              <Trash className="h-4 w-4"/>
            </div>
          </div>
        </Button>
      ))}
    </div>
  );
};

export default DaySelector;
