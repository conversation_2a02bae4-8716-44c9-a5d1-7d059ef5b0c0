import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase-client';
import { useAuth } from '@/context/AuthContext';

export interface UserTeamData {
  team: string | null;
  isLoading: boolean;
  isManager: boolean;
  hasTeamAccess: (team: string) => boolean;
}

export const useUserTeam = (): UserTeamData => {
  const { user } = useAuth();
  const [team, setTeam] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isManager, setIsManager] = useState(false);

  useEffect(() => {
    const fetchUserTeam = async () => {
      if (!user) {
        setIsLoading(false);
        return;
      }

      setIsLoading(true);

      try {
        // Controleer of de gebruiker een beheerder is
        const { data: managerData, error: managerError } = await supabase
          .from('authorized_managers')
          .select('user_id')
          .eq('user_id', user.id)
          .maybeSingle();

        if (managerError) {
          console.error('Fout bij ophalen beheerdersstatus:', managerError);
        } else {
          setIsManager(!!managerData);
        }

        // Haal de ploeg van de gebruiker op
        const { data: teamData, error: teamError } = await supabase
          .from('user_teams')
          .select('team')
          .eq('user_id', user.id)
          .maybeSingle();

        if (teamError) {
          console.error('Fout bij ophalen ploeg:', teamError);
        } else if (teamData) {
          setTeam(teamData.team);
        }
      } catch (error) {
        console.error('Onverwachte fout bij ophalen gebruikersgegevens:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserTeam();
  }, [user]);

  // Functie om te controleren of de gebruiker toegang heeft tot een bepaalde ploeg
  const hasTeamAccess = (teamToCheck: string): boolean => {
    // Beheerders hebben altijd toegang
    if (isManager) return true;
    
    // Gebruikers hebben alleen toegang tot hun eigen ploeg
    return team === teamToCheck;
  };

  return { team, isLoading, isManager, hasTeamAccess };
};
