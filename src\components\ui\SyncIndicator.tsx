import React from 'react';
import { useProduction } from '@/context/ProductionContext';

const SyncIndicator: React.FC = () => {
  const { isSyncing } = useProduction();

  return (
    <div className="flex items-center">
      {isSyncing ? (
        <div className="animate-spin h-3 w-3 border border-t-blue-500 border-blue-200 rounded-full" />
      ) : (
        <div className="h-2 w-2 rounded-full bg-green-500" />
      )}
    </div>
  );
};

export default SyncIndicator;
