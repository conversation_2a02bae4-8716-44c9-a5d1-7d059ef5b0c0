import React, { createContext, useContext, useState, useEffect } from 'react';
import { Notification } from '@/types';
import { supabase } from '@/lib/supabase-client';
import { useAuth } from './AuthContext';
import { toast } from 'sonner';

interface NotificationContextType {
  notifications: Notification[];
  unreadCount: number;
  markAsRead: (notificationId: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  deleteNotification: (notificationId: string) => Promise<void>;
  refreshNotifications: () => Promise<void>;
  clearAllNotifications: () => Promise<void>;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

export const NotificationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user } = useAuth();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);

  // Fetch notifications for the current user
  const fetchNotifications = async () => {
    if (!user) {
      setNotifications([]);
      setUnreadCount(0);
      return;
    }

    try {
      const { data, error } = await supabase
        .from('notifications')
        .select('*')
        .eq('user_id', user.id)
        .eq('read', false) // Only get unread notifications
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching notifications:', error);
        return;
      }

      setNotifications(data || []);

      // Set unread count to the number of unread notifications
      setUnreadCount((data || []).length);
    } catch (error) {
      console.error('Unexpected error fetching notifications:', error);
    }
  };

  // Set up subscription for notifications and todos
  useEffect(() => {
    if (!user) return;

    fetchNotifications();

    // Subscribe to changes in the notifications table
    const notificationsChannel = supabase
      .channel('notifications_changes')
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'notifications', filter: `user_id=eq.${user.id}` },
        (payload) => {
          console.log('Notification change received:', payload);
          fetchNotifications();
        }
      )
      .subscribe();

    // Subscribe to changes in the todos table to automatically update notifications
    // when a todo is completed
    const todosChannel = supabase
      .channel('todos_completed_changes')
      .on('postgres_changes',
        { event: 'UPDATE', schema: 'public', table: 'todos', filter: `completed=eq.true` },
        async (payload) => {
          console.log('Todo completed:', payload);
          const todoId = payload.new.id;

          try {
            // First, get all notifications for this todo
            const { data: todoNotifications, error: fetchError } = await supabase
              .from('notifications')
              .select('*')
              .eq('user_id', user.id)
              .in('type', ['todo_assigned', 'todo_expired']) // Include expired notifications
              .eq('related_id', todoId);

            if (fetchError) {
              console.error('Error fetching todo notifications:', fetchError);
              return;
            }

            if (!todoNotifications || todoNotifications.length === 0) {
              return; // No notifications to update
            }

            // Update the notifications to mark them as read
            const { error } = await supabase
              .from('notifications')
              .update({ read: true })
              .eq('user_id', user.id)
              .in('type', ['todo_assigned', 'todo_expired']) // Include expired notifications
              .eq('related_id', todoId);

            if (error) {
              console.error('Error updating todo notifications:', error);
            } else {
              // Refresh notifications after update
              fetchNotifications();
            }
          } catch (error) {
            console.error('Unexpected error updating todo notifications:', error);
          }
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(notificationsChannel);
      supabase.removeChannel(todosChannel);
    };
  }, [user]);

  // Mark a notification as read
  const markAsRead = async (notificationId: string) => {
    try {
      // Get the notification to check its type
      const notification = notifications.find(n => n.id === notificationId);
      if (!notification) return;

      // Update the notification in the database
      const { error } = await supabase
        .from('notifications')
        .update({ read: true })
        .eq('id', notificationId);

      if (error) {
        console.error('Error marking notification as read:', error);
        return;
      }

      // Update local state
      setNotifications(prev =>
        prev.map(n => n.id === notificationId ? { ...n, read: true } : n)
      );

      // Recalculate unread count
      // For todo notifications, we keep them in the count even if they're read
      const newUnreadCount = notifications.filter(n =>
        (!n.read && n.id !== notificationId) || // Still unread and not the one we just marked
        (n.id === notificationId && (n.type === 'todo_assigned' || n.type === 'todo_expired')) || // The one we just marked if it's a todo notification
        (n.id !== notificationId && (n.type === 'todo_assigned' || n.type === 'todo_expired')) // Other todo notifications
      ).length;

      setUnreadCount(newUnreadCount);
    } catch (error) {
      console.error('Unexpected error marking notification as read:', error);
    }
  };

  // Mark all notifications as read
  const markAllAsRead = async () => {
    if (!user || notifications.length === 0) return;

    try {
      // Get all todo notifications that should keep the counter
      const todoNotifications = notifications.filter(n =>
        n.type === 'todo_assigned' || n.type === 'todo_expired'
      );

      // Get all non-todo notifications that should be marked as read
      const nonTodoNotifications = notifications.filter(n =>
        n.type !== 'todo_assigned' && n.type !== 'todo_expired'
      );

      // Mark only non-todo notifications as read in the database
      if (nonTodoNotifications.length > 0) {
        const nonTodoIds = nonTodoNotifications.map(n => n.id);
        const { error } = await supabase
          .from('notifications')
          .update({ read: true })
          .eq('user_id', user.id)
          .in('id', nonTodoIds);

        if (error) {
          console.error('Error marking non-todo notifications as read:', error);
        }
      }

      // Update local state
      setNotifications(prev => {
        return prev.map(n => {
          // Only mark non-todo notifications as read
          if (n.type !== 'todo_assigned' && n.type !== 'todo_expired') {
            return { ...n, read: true };
          }
          return n;
        });
      });

      // Count unread notifications after update
      const unreadCount = notifications.filter(n =>
        !n.read || (n.type === 'todo_assigned' || n.type === 'todo_expired')
      ).length;

      // Update unread count
      setUnreadCount(unreadCount);
    } catch (error) {
      console.error('Unexpected error marking all notifications as read:', error);
    }
  };

  // Delete a notification
  const deleteNotification = async (notificationId: string) => {
    try {
      const { error } = await supabase
        .from('notifications')
        .delete()
        .eq('id', notificationId);

      if (error) {
        console.error('Error deleting notification:', error);
        return;
      }

      // Update local state
      const deletedNotification = notifications.find(n => n.id === notificationId);
      setNotifications(prev => prev.filter(n => n.id !== notificationId));

      if (deletedNotification && !deletedNotification.read) {
        setUnreadCount(prev => Math.max(0, prev - 1));
      }
    } catch (error) {
      console.error('Unexpected error deleting notification:', error);
    }
  };

  // Manually refresh notifications
  const refreshNotifications = async () => {
    return fetchNotifications();
  };

  // Clear all notifications
  const clearAllNotifications = async () => {
    if (!user) return;

    try {
      const { error } = await supabase
        .from('notifications')
        .delete()
        .eq('user_id', user.id);

      if (error) {
        console.error('Error clearing all notifications:', error);
        toast.error('Fout bij het verwijderen van alle notificaties');
        return;
      }

      // Update local state
      setNotifications([]);
      setUnreadCount(0);
      toast.success('Alle notificaties zijn verwijderd');
    } catch (error) {
      console.error('Unexpected error clearing all notifications:', error);
      toast.error('Onverwachte fout bij het verwijderen van alle notificaties');
    }
  };

  const value = {
    notifications,
    unreadCount,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    refreshNotifications,
    clearAllNotifications
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
};

export const useNotifications = () => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};
