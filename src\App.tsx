// Corrected src/App.tsx
import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'; // Added Navigate
import Layout from '@/components/layout/Layout';
// import LocalLayout from '@/components/layout/LocalLayout'; // Unused import
import Dashboard from '@/pages/Results';
// import Report from '@/pages/Report'; // Unused import
import Disruptions from '@/pages/Disruptions'; // Re-import Disruptions
import OverdrachtPage from '@/pages/OverdrachtPage'; // Import new page
import LogbookHistoryPage from '@/pages/LogbookHistoryPage'; // Import Logbook History page
import SafetyQuality from '@/pages/SafetyQuality';
import Materials from '@/pages/Materials';
import EquipmentCodes from '@/pages/EquipmentCodes';
import HistoryV2 from '@/pages/HistoryV2';
import Settings from '@/pages/Settings';
import Auth from '@/pages/Auth';
import NewAuthPage from '@/pages/NewAuthPage';
import SimpleAuthPage from '@/pages/SimpleAuthPage';
import LocalAuthPage from '@/pages/LocalAuthPage';
import AccountPage from '@/pages/AccountPage'; // Import Account page
import TdLogbookPage from '@/pages/TdLogbookPage'; // Import TD Logbook page
import TdLogbookHistoryPage from '@/pages/TdLogbookHistoryPage'; // Import TD Logbook History page
import TodoListPage from '@/pages/TodoListPage'; // Import Todo List page
import TodoHistoryPage from '@/pages/TodoHistoryPage'; // Import Todo History page
import { ProductionProvider } from '@/context/ProductionContext';
// AuthProvider is already in main.tsx
// import { LocalAuthProvider } from '@/context/LocalAuthContext'; // Unused import
import { TransactionProvider } from '@/context/TransactionContext';
import { Toaster } from '@/components/ui/toaster';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
// import LocalProtectedRoute from '@/components/auth/LocalProtectedRoute'; // Unused import

// Import Management section components
import ManagementLayout from '@/pages/management/ManagementLayout';
import TargetsYieldsPage from '@/pages/management/TargetsYieldsPage'; // Import the new page
// Removed imports for placeholder pages (MaterialsPage, EquipmentPage)
// import RegisterUserPage from '@/pages/management/RegisterUserPage'; // Unused import
import NewRegisterPage from '@/pages/management/NewRegisterPage'; // Import new registration page
import SimpleRegisterPage from '@/pages/management/SimpleRegisterPage'; // Import simple registration page
import UserManagementPage from '@/pages/management/UserManagementPage'; // Import user management page

// Importeer de nieuwe materialen componenten (Existing)
import { MaterialManagement } from '@/components/materials/MaterialManagement';
import { MaterialComparison } from '@/components/materials/MaterialComparison';

// Configureer de toekomstige flags
const router = {
  future: {
    v7_startTransition: true,
    v7_relativeSplatPath: true
  }
};

const App: React.FC = () => {
  return (
    <Router {...router}>
        <TransactionProvider>
          <ProductionProvider>
            <Layout>
              <Routes>
                <Route path="/auth" element={<Auth />} />
                <Route path="/auth-local" element={<LocalAuthPage />} />
                <Route path="/auth-simple" element={<SimpleAuthPage />} />
                <Route path="/auth-magic" element={<NewAuthPage />} />
                <Route path="/" element={
                  <ProtectedRoute>
                    <Dashboard />
                  </ProtectedRoute>
                } />
                {/* Removed /report route */}
                <Route path="/results" element={
                  <ProtectedRoute>
                    <Dashboard />
                  </ProtectedRoute>
                } />
                {/* Restored old /disruptions route */}
                 <Route path="/disruptions" element={
                  <ProtectedRoute>
                    <Disruptions />
                  </ProtectedRoute>
                } />
                {/* Updated Route for Overdracht */}
                <Route path="/overdracht" element={
                  <ProtectedRoute>
                    <OverdrachtPage />
                  </ProtectedRoute>
                } />
                {/* Redirect /logboek to /overdracht for backward compatibility */}
                <Route path="/logboek" element={<Navigate to="/overdracht" replace />} />
                <Route path="/safety-quality" element={
                  <ProtectedRoute>
                    <SafetyQuality />
                  </ProtectedRoute>
                } />
                {/* Account page */}
                <Route path="/account" element={<ProtectedRoute><AccountPage /></ProtectedRoute>} />
                {/* Redirect base /settings to /settings/toggles */}
                <Route path="/settings" element={<Navigate to="/settings/toggles" replace />} />
                {/* Only keep /settings/toggles under the general settings */}
                <Route path="/settings/toggles" element={<ProtectedRoute><Settings /></ProtectedRoute>} />
                {/* Removed /settings/targets, /settings/equipment, /settings/materials routes */}
                <Route path="/history" element={
                  <ProtectedRoute>
                    <HistoryV2 />
                  </ProtectedRoute>
                } />
               {/* Logbook History Route */}
               <Route path="/logboek-historie" element={
                <ProtectedRoute>
                  <LogbookHistoryPage />
                </ProtectedRoute>
              } />
              {/* TD Logbook Route */}
              <Route path="/td-logboek" element={
                <ProtectedRoute>
                  <TdLogbookPage />
                </ProtectedRoute>
              } />
              {/* TD Logbook History Route */}
              <Route path="/td-logboek/historie" element={
                <ProtectedRoute>
                  <TdLogbookHistoryPage />
                </ProtectedRoute>
              } />
              {/* Todo List Route */}
              <Route path="/todos" element={
                <ProtectedRoute>
                  <TodoListPage />
                </ProtectedRoute>
              } />
              {/* Todo History Route */}
              <Route path="/todos/historie" element={
                <ProtectedRoute>
                  <TodoHistoryPage />
                </ProtectedRoute>
              } />

              {/* Management Section Routes */}
              <Route
                path="/management"
                element={
                  <ProtectedRoute>
                    <ManagementLayout />
                  </ProtectedRoute>
                }
              >
                {/* Default route for /management could redirect or show an overview */}
                {/* <Route index element={<Navigate to="/management/targets" replace />} /> */}
                {/* Point management routes to the original components */}
                <Route path="targets" element={<TargetsYieldsPage />} /> {/* Point to the new dedicated component */}
                <Route path="materials" element={<Materials />}> {/* Materialen uses Materials component */}
                  {/* Nested routes for Materials section */}
                  <Route index element={<MaterialManagement />} /> {/* Default to management */}
                  <Route path="management" element={<MaterialManagement />} />
                  <Route path="comparison" element={<MaterialComparison />} />
                </Route>
                <Route path="equipment" element={<EquipmentCodes />} /> {/* Apparaat beheer uses EquipmentCodes component */}
                <Route path="register" element={<SimpleRegisterPage />} /> {/* Use simple register page */}
                <Route path="register-local" element={<NewRegisterPage />} /> {/* Keep new register page as backup */}
                <Route path="users" element={<UserManagementPage />} /> {/* Add user management page */}
              </Route>

            </Routes>
            </Layout>
            <Toaster />
          </ProductionProvider>
        </TransactionProvider>
    </Router>
  );
};

export default App;
