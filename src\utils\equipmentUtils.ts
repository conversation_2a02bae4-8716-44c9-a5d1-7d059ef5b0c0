
import { ProductionLine, EquipmentEntry } from '@/types';

/**
 * Utility functions for equipment management
 * These functions can be used as fallbacks when the context's functions are not available
 */

export const addEquipmentOption = (
  line: ProductionLine, 
  areaCode: string, 
  option: EquipmentEntry,
  equipmentOptions: Record<ProductionLine, Record<string, EquipmentEntry[]>>
): Record<ProductionLine, Record<string, EquipmentEntry[]>> => {
  // Create a deep copy of the current options
  const newOptions = JSON.parse(JSON.stringify(equipmentOptions));
  
  // Ensure the line and area exist
  if (!newOptions[line]) {
    newOptions[line] = {};
  }
  
  if (!newOptions[line][areaCode]) {
    newOptions[line][areaCode] = [];
  }
  
  // Add the new option
  newOptions[line][areaCode].push(option);
  
  return newOptions;
};

export const removeEquipmentOption = (
  line: ProductionLine, 
  areaCode: string, 
  optionId: string,
  equipmentOptions: Record<ProductionLine, Record<string, EquipmentEntry[]>>
): Record<ProductionLine, Record<string, EquipmentEntry[]>> => {
  // Create a deep copy of the current options
  const newOptions = JSON.parse(JSON.stringify(equipmentOptions));
  
  // Check if the line and area exist
  if (!newOptions[line] || !newOptions[line][areaCode]) {
    return equipmentOptions;
  }
  
  // Filter out the option to remove
  newOptions[line][areaCode] = newOptions[line][areaCode].filter(
    (opt: EquipmentEntry) => opt.id !== optionId
  );
  
  return newOptions;
};

export const editEquipmentOption = (
  line: ProductionLine, 
  areaCode: string, 
  optionId: string, 
  updatedOption: EquipmentEntry,
  equipmentOptions: Record<ProductionLine, Record<string, EquipmentEntry[]>>
): Record<ProductionLine, Record<string, EquipmentEntry[]>> => {
  // Create a deep copy of the current options
  const newOptions = JSON.parse(JSON.stringify(equipmentOptions));
  
  // Check if the line and area exist
  if (!newOptions[line] || !newOptions[line][areaCode]) {
    return equipmentOptions;
  }
  
  // Find and update the option
  newOptions[line][areaCode] = newOptions[line][areaCode].map(
    (opt: EquipmentEntry) => opt.id === optionId ? updatedOption : opt
  );
  
  return newOptions;
};

export const addEquipmentArea = (
  line: ProductionLine, 
  areaCode: string, 
  areaLabel: string,
  equipmentOptions: Record<ProductionLine, Record<string, EquipmentEntry[]>>
): Record<ProductionLine, Record<string, EquipmentEntry[]>> => {
  // Create a deep copy of the current options
  const newOptions = JSON.parse(JSON.stringify(equipmentOptions));
  
  // Ensure the line exists
  if (!newOptions[line]) {
    newOptions[line] = {};
  }
  
  // Add the new area if it doesn't exist
  if (!newOptions[line][areaCode]) {
    newOptions[line][areaCode] = [];
  }
  
  return newOptions;
};

export const removeEquipmentArea = (
  line: ProductionLine, 
  areaCode: string,
  equipmentOptions: Record<ProductionLine, Record<string, EquipmentEntry[]>>
): Record<ProductionLine, Record<string, EquipmentEntry[]>> => {
  // Create a deep copy of the current options
  const newOptions = JSON.parse(JSON.stringify(equipmentOptions));
  
  // Check if the line exists
  if (!newOptions[line]) {
    return equipmentOptions;
  }
  
  // Remove the area
  if (newOptions[line][areaCode]) {
    delete newOptions[line][areaCode];
  }
  
  return newOptions;
};

export const editEquipmentArea = (
  line: ProductionLine, 
  areaCode: string, 
  updatedAreaCode: string, 
  updatedAreaLabel: string,
  equipmentOptions: Record<ProductionLine, Record<string, EquipmentEntry[]>>
): Record<ProductionLine, Record<string, EquipmentEntry[]>> => {
  // Create a deep copy of the current options
  const newOptions = JSON.parse(JSON.stringify(equipmentOptions));
  
  // Check if the line and area exist
  if (!newOptions[line] || !newOptions[line][areaCode]) {
    return equipmentOptions;
  }
  
  // Save the current options for the area
  const areaOptions = newOptions[line][areaCode];
  
  // Remove the old area
  delete newOptions[line][areaCode];
  
  // Create the new area with the existing options
  newOptions[line][updatedAreaCode] = areaOptions;
  
  return newOptions;
};
