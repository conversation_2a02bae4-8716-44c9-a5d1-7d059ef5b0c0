// Script om alle todo's te verwijderen
const { createClient } = require('@supabase/supabase-js');

// Supabase configuratie
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_ANON_KEY;

if (!supabaseUrl) {
  console.error('Supabase URL is niet ingesteld. Stel SUPABASE_URL in als omgevingsvariabele.');
  process.exit(1);
}

if (!supabaseKey) {
  console.error('Supabase API key is niet ingesteld. Stel SUPABASE_ANON_KEY in als omgevingsvariabele.');
  process.exit(1);
}

// Supabase client initialiseren
const supabase = createClient(supabaseUrl, supabaseKey);

async function clearTodos() {
  console.log('Alle todo\'s verwijderen...');

  try {
    const { error } = await supabase
      .from('todos')
      .delete()
      .is('id', 'not.null'); // Verwijder alle rijen

    if (error) {
      console.error('Fout bij het verwijderen van todo\'s:', error);
    } else {
      console.log('Alle todo\'s zijn succesvol verwijderd!');
    }
  } catch (error) {
    console.error('Onverwachte fout:', error);
  }
}

// Voer de functie uit
clearTodos();
