import React, { useState, useEffect, useMemo } from 'react';
import { ProductionRow, ProductionLine, EquipmentEntry, BreakdownEntry } from '@/types';
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from '@/components/ui/table';
import { loadItem } from '@/lib/local-storage';

interface TableOverviewProps {
  formData: ProductionRow | null;
  selectedLine: ProductionLine;
  equipmentOptions: Record<string, Record<string, EquipmentEntry[]>>;
  calculateTotalProduction: () => number;
  formatForDisplay: (value: string | number) => string;
}

const TableOverview: React.FC<TableOverviewProps> = ({
  formData,
  selectedLine,
  equipmentOptions,
  calculateTotalProduction,
  formatForDisplay
}) => {
  const [breakdownData, setBreakdownData] = useState<BreakdownEntry[]>([]);

  useEffect(() => {
    if (formData) {
      const breakdownStorageKey = `breakdowns_${formData.line}_${formData.date}`;
      const loadedBreakdowns = loadItem<BreakdownEntry[]>(breakdownStorageKey, []);
      setBreakdownData(loadedBreakdowns || []);
    } else {
      setBreakdownData([]);
    }
  }, [formData]);

  const getEquipmentLabel = (value: string) => {
    if (!value) return '';
    
    for (const area in equipmentOptions[selectedLine]) {
      const found = equipmentOptions[selectedLine][area].find((option) => option.value === value);
      if (found) return found.label_nl;
    }
    
    return value;
  };
  
  const calculateTotalDowntime = useMemo(() => {
    return breakdownData.reduce((total, breakdown) => {
      const timeComponents = breakdown.duration.split(':');
      if (timeComponents.length === 2) {
        const hours = parseInt(timeComponents[0], 10) || 0;
        const minutes = parseInt(timeComponents[1], 10) || 0;
        return total + hours * 60 + minutes;
      }
      return total;
    }, 0);
  }, [breakdownData]);

  const formatMinutes = (totalMinutes: number): string => {
    const hours = Math.floor(totalMinutes / 60);
    const minutes = totalMinutes % 60;
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
  };

  if (!formData) {
      return <div className="text-center p-4">Selecteer een dag om het overzicht te zien.</div>;
  }

  return (
    <div className="overflow-x-auto">
      <Table className="border">
        <TableHeader>
          <TableRow>
            <TableHead className="font-bold">Categorie</TableHead>
            <TableHead className="font-bold">Ochtenddienst</TableHead>
            <TableHead className="font-bold">Middagdienst</TableHead>
            <TableHead className="font-bold">Nachtdienst</TableHead>
            <TableHead className="font-bold">Totaal</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow>
            <TableCell className="font-medium">Productie</TableCell>
            <TableCell>{formatForDisplay(formData.od.production || 0)}</TableCell>
            <TableCell>{formatForDisplay(formData.md.production || 0)}</TableCell>
            <TableCell>{formatForDisplay(formData.nd.production || 0)}</TableCell>
            <TableCell className="font-bold">{formatForDisplay(calculateTotalProduction())}</TableCell>
          </TableRow>
          <TableRow>
            <TableCell className="font-medium">Yield (%)</TableCell>
            <TableCell>{formatForDisplay(formData.od.yield || 0)}</TableCell>
            <TableCell>{formatForDisplay(formData.md.yield || 0)}</TableCell>
            <TableCell>{formatForDisplay(formData.nd.yield || 0)}</TableCell>
            <TableCell>-</TableCell>
          </TableRow>
          <TableRow>
            <TableCell className="font-medium">Materiaal</TableCell>
            <TableCell>{formData.od.material === "none" ? "Geen materiaal" : formData.od.material || "Geen materiaal"}</TableCell>
            <TableCell>{formData.md.material === "none" ? "Geen materiaal" : formData.md.material || "Geen materiaal"}</TableCell>
            <TableCell>{formData.nd.material === "none" ? "Geen materiaal" : formData.nd.material || "Geen materiaal"}</TableCell>
            <TableCell>-</TableCell>
          </TableRow>
          <TableRow>
            <TableCell className="font-medium">Transitie</TableCell>
            <TableCell>{formData.od.isTransition ? 'Ja' : 'Nee'}</TableCell>
            <TableCell>{formData.md.isTransition ? 'Ja' : 'Nee'}</TableCell>
            <TableCell>{formData.nd.isTransition ? 'Ja' : 'Nee'}</TableCell>
            <TableCell>-</TableCell>
          </TableRow>
          <TableRow className="bg-gray-50">
            <TableCell className="font-bold">Storingen</TableCell>
            <TableCell colSpan={4}>
              {breakdownData.length === 0 ? (
                 <div className="text-sm text-gray-500 italic py-1">Geen storingen voor deze dag.</div>
              ) : (
                 <div className="space-y-2 py-1">
                   {breakdownData.map((breakdown) => (
                     <div key={breakdown.id} className="grid grid-cols-3 gap-2 text-sm">
                       <div>
                         <span className="font-medium">Tijd:</span> {breakdown.duration}
                       </div>
                       <div>
                         <span className="font-medium">Onderdeel:</span> {getEquipmentLabel(breakdown.equipment || '')}
                       </div>
                       <div>
                         <span className="font-medium">Oorzaak:</span> {breakdown.pareto}
                       </div>
                     </div>
                   ))}
                 </div>
              )}
            </TableCell>
          </TableRow>
          <TableRow>
            <TableCell className="font-bold">Downtime</TableCell>
            <TableCell colSpan={3}></TableCell>
            <TableCell className="font-bold">{formatMinutes(calculateTotalDowntime)}</TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </div>
  );
};

export default TableOverview;
