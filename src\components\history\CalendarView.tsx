import React from 'react';
import { format } from 'date-fns';
import { nl } from 'date-fns/locale';
import { Card, CardContent } from '@/components/ui/card';
import { ProductionLine, HistoryEntry } from '@/types';
import { useProduction } from '@/context/ProductionContext';

export interface CalendarViewProps {
  data: HistoryEntry[];
  selectedLine: ProductionLine | 'all';
}

export const CalendarView: React.FC<CalendarViewProps> = ({ data, selectedLine }) => {
  const { TARGETS } = useProduction();

  // Groepeer data per datum
  const groupedData = data.reduce((acc, entry) => {
    Object.entries(entry.data).forEach(([line, lineData]) => {
      if (selectedLine !== 'all' && line !== selectedLine) return;

      lineData.rows.forEach(row => {
        const date = format(new Date(row.date), 'yyyy-MM-dd');
        if (!acc[date]) {
          acc[date] = {
            date: row.date,
            lines: {}
          };
        }

        const dayTotal = 
          (Number(row.od.production) || 0) +
          (Number(row.md.production) || 0) +
          (Number(row.nd.production) || 0);

        const dailyTarget = TARGETS[line as ProductionLine] * 3;
        const percentage = (dayTotal / dailyTarget) * 100;

        acc[date].lines[line] = {
          total: dayTotal,
          target: dailyTarget,
          percentage
        };
      });
    });
    return acc;
  }, {} as Record<string, any>);

  // Sorteer op datum (nieuwste eerst)
  const sortedDates = Object.keys(groupedData).sort((a, b) => 
    new Date(b).getTime() - new Date(a).getTime()
  );

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      {sortedDates.map(date => (
        <Card key={date} className={
          Object.values(groupedData[date].lines).every((line: any) => line.percentage >= 100)
            ? 'border-green-200 bg-green-50'
            : Object.values(groupedData[date].lines).every((line: any) => line.percentage < 100)
              ? 'border-red-200 bg-red-50'
              : ''
        }>
          <CardContent className="p-4">
            <div className="font-medium mb-2">
              {format(new Date(groupedData[date].date), 'EEEE d MMMM', { locale: nl })}
            </div>
            <div className="space-y-2">
              {Object.entries(groupedData[date].lines).map(([line, data]: [string, any]) => (
                <div key={line} className="flex justify-between items-center text-sm">
                  <span className="font-medium">{line.toUpperCase()}</span>
                  <div className="flex items-center gap-2">
                    <span>{data.total.toLocaleString('nl-NL')}</span>
                    <span className={`font-medium ${
                      data.percentage >= 100 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {data.percentage.toFixed(0)}%
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      ))}
      {sortedDates.length === 0 && (
        <div className="col-span-full text-center text-muted-foreground py-8">
          Geen productiedata gevonden voor de geselecteerde periode
        </div>
      )}
    </div>
  );
}; 