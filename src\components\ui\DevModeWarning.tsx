// src/components/ui/DevModeWarning.tsx
import React from 'react';
import { AlertTriangle } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

const DevModeWarning: React.FC = () => {
  return (
    <Alert variant="destructive" className="mb-4">
      <AlertTriangle className="h-4 w-4" />
      <AlertTitle>Waarschuwing: Ontwikkelmodus</AlertTitle>
      <AlertDescription>
        <strong>SECURITY WARNING:</strong> Deze applicatie draait in ontwikkelmodus.
        Lokale authenticatie is uitgeschakeld om security redenen.
        <br />
        <strong>NIET GEBRUIKEN IN PRODUCTIE!</strong>
        <br />
        Gebruik alleen Supabase authenticatie voor productie omgevingen.
      </AlertDescription>
    </Alert>
  );
};

export default DevModeWarning;
