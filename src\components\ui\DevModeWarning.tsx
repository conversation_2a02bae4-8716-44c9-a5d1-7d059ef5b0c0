// src/components/ui/DevModeWarning.tsx
import React from 'react';
import { AlertTriangle } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

const DevModeWarning: React.FC = () => {
  return (
    <Alert variant="destructive" className="mb-4">
      <AlertTriangle className="h-4 w-4" />
      <AlertTitle>Waarschuwing: Ontwikkelmodus</AlertTitle>
      <AlertDescription>
        Deze applicatie draait momenteel in ontwikkelmodus met lokale authenticatie. 
        Dit is ALLEEN bedoeld voor ontwikkeling en testen. 
        <strong> NIET GEBRUIKEN IN PRODUCTIE!</strong> Team-gebaseerde permissies werken mogelijk niet correct.
      </AlertDescription>
    </Alert>
  );
};

export default DevModeWarning;
