import React, { useRef, useEffect } from 'react';
import { cn } from '@/lib/utils'; // Import cn utility
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { ProductionShiftData } from '@/types';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

export interface ShiftFormProps {
  shift: 'od' | 'md' | 'nd';
  shiftLabel: string;
  shiftData: ProductionShiftData;
  materialOptions: string[];
  isDisabled: boolean;
  onShiftChange: (shift: 'od' | 'md' | 'nd', field: string, value: string | number | boolean, cursorPosition?: number) => number | void;
  formatForDisplay: (value: string | number) => string;
  onPasteProduction?: () => void;
  onYieldChange?: (value: string) => void;
  shiftTarget?: number; // Add optional shift target prop
}

const ShiftForm: React.FC<ShiftFormProps> = ({
  shift,
  shiftLabel,
  shiftData,
  materialOptions,
  isDisabled,
  onShiftChange,
  formatForDisplay,
  onPasteProduction = () => {},
  onYieldChange = () => {},
  shiftTarget // Destructure the new prop
}) => {
  // Refs to store cursor positions
  const productionInputRef = useRef<HTMLInputElement>(null);
  const yieldInputRef = useRef<HTMLInputElement>(null);

  // Refs to store cursor positions
  const productionCursorPositionRef = useRef<number | null>(null);
  const yieldCursorPositionRef = useRef<number | null>(null);

  // Effect to restore cursor position after render
  useEffect(() => {
    // Restore production cursor position
    if (productionCursorPositionRef.current !== null && productionInputRef.current) {
      productionInputRef.current.setSelectionRange(
        productionCursorPositionRef.current,
        productionCursorPositionRef.current
      );
      productionCursorPositionRef.current = null; // Reset after applying
    }

    // Restore yield cursor position
    if (yieldCursorPositionRef.current !== null && yieldInputRef.current) {
      yieldInputRef.current.setSelectionRange(
        yieldCursorPositionRef.current,
        yieldCursorPositionRef.current
      );
      yieldCursorPositionRef.current = null; // Reset after applying
    }
  });

  // Handle production input change with cursor position tracking
  const handleProductionChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const cursorPosition = e.target.selectionStart;
    const newCursorPosition = onShiftChange(shift, 'production', e.target.value, cursorPosition);

    // Store the new cursor position for restoration after render
    if (newCursorPosition !== undefined) {
      productionCursorPositionRef.current = newCursorPosition;
    } else if (cursorPosition !== null) {
      productionCursorPositionRef.current = cursorPosition;
    }
  };

  // Handle yield input change with cursor position tracking
  const handleYieldChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const cursorPosition = e.target.selectionStart;
    const newCursorPosition = onShiftChange(shift, 'yield', e.target.value, cursorPosition);

    // Store the new cursor position for restoration after render
    if (newCursorPosition !== undefined) {
      yieldCursorPositionRef.current = newCursorPosition;
    } else if (cursorPosition !== null) {
      yieldCursorPositionRef.current = cursorPosition;
    }
  };

  // Handle paste event for production field
  const handlePaste = (event: React.ClipboardEvent<HTMLInputElement>) => {
    event.preventDefault();
    // Calling the onPasteProduction prop
    onPasteProduction();

    // Default implementation in case the prop is not provided with functionality
    const pasteValue = event.clipboardData.getData('text').replace(',', '.');
    const numericValue = parseFloat(pasteValue);

    if (!isNaN(numericValue)) {
      const newCursorPosition = onShiftChange(shift, 'production', numericValue.toString());
      if (newCursorPosition !== undefined) {
        productionCursorPositionRef.current = newCursorPosition;
      }
    }
  };

  return (
    <Card className="border">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg">
          {shiftLabel} {shiftTarget !== undefined && <span className="text-sm font-normal text-gray-500">(Doel: {shiftTarget.toLocaleString('nl-NL')})</span>}
        </CardTitle>
      </CardHeader>
      <CardContent className="grid md:grid-cols-4 gap-4">
        <div>
          <Label htmlFor={`${shift}-production`}>Productie</Label>
          <Input
            id={`${shift}-production`}
            ref={productionInputRef}
            value={formatForDisplay(shiftData.production || '')}
            onChange={handleProductionChange}
            onPaste={handlePaste}
            disabled={isDisabled}
            className={cn(
              "mt-1",
              shiftTarget !== undefined && shiftData.production && Number(String(shiftData.production).replace(',', '.')) // Check if production has a value and target exists
                ? (Number(String(shiftData.production).replace(',', '.')) >= shiftTarget ? 'text-green-700 font-semibold' : 'text-red-600 font-semibold')
                : ''
            )}
            autoComplete="off"
          />
        </div>
        <div>
          <Label htmlFor={`${shift}-yield`}>Yield (%)</Label>
          <Input
            id={`${shift}-yield`}
            ref={yieldInputRef}
            value={formatForDisplay(shiftData.yield || '')}
            onChange={handleYieldChange}
            disabled={isDisabled}
            className="mt-1 max-w-[100px]"
            autoComplete="off"
            inputMode="decimal" // Added for better mobile experience
          />
        </div>
        <div>
          <Label htmlFor={`${shift}-material`}>Materiaal</Label>
          <Select
            value={shiftData.material || 'none'}
            onValueChange={(value) => onShiftChange(shift, 'material', value)}
            disabled={isDisabled}
          >
            <SelectTrigger id={`${shift}-material`} className="mt-1">
              <SelectValue placeholder="Selecteer materiaal">
                {shiftData.material || 'Geen materiaal'}
              </SelectValue>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="none">Geen materiaal</SelectItem>
              {Array.isArray(materialOptions) && materialOptions.map((material) => (
                <SelectItem key={material} value={material}>
                  {material}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="flex items-end">
          <div className="flex items-center space-x-2 mb-1">
            <Switch
              id={`${shift}-transition`}
              checked={shiftData.isTransition || false}
              onCheckedChange={(checked) => onShiftChange(shift, 'isTransition', checked)}
              disabled={isDisabled}
            />
            <Label htmlFor={`${shift}-transition`}>Materiaal Overgang</Label>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ShiftForm;
