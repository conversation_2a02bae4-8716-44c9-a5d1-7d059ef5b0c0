import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:h-4 [&_svg]:w-4 [&_svg]:min-h-4 [&_svg]:min-w-4 [&_svg]:shrink-0 hover:[&_svg]:h-4 hover:[&_svg]:w-4 hover:[&_svg]:min-h-4 hover:[&_svg]:min-w-4",
  {
    variants: {
      variant: {
        default: "btn-text-gradient hover:scale-105",
        destructive:
          "bg-destructive text-destructive-foreground hover:bg-destructive/90 shadow-sm hover:shadow-lg hover:scale-100",
        outline: "btn-text-gradient hover:scale-105",
        secondary:
          "bg-secondary text-primary hover:bg-secondary/80 shadow-sm hover:shadow-lg hover:scale-100",
        ghost: "hover:bg-accent/20 hover:text-accent-foreground hover:scale-100",
        link: "btn-text-gradient hover:scale-105",
        gradient: "bg-gradient-to-r from-primary to-secondary text-white shadow-sm hover:shadow-lg hover:scale-100",
        success: "bg-green-500 text-white hover:bg-green-600", // Added success variant
      },
      size: {
        default: "h-10 px-5 py-2",
        sm: "h-9 rounded-md px-4",
        lg: "h-12 rounded-md px-8 text-base",
        icon: "h-10 w-10 hover:scale-100",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }
