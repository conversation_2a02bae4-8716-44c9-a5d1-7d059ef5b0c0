-- Migration script to consolidate redundant RLS policies for local_storage_data table
-- This removes overlapping and conflicting policies and replaces them with clean, consolidated ones

-- Drop all existing policies for local_storage_data table to avoid conflicts
DROP POLICY IF EXISTS "Enable read access for all users" ON public.local_storage_data;
DROP POLICY IF EXISTS "Enable insert for authenticated users only" ON public.local_storage_data;
DROP POLICY IF EXISTS "Enable read access for all" ON public.local_storage_data;
DROP POLICY IF EXISTS "Enable insert access for all" ON public.local_storage_data;
DROP POLICY IF EXISTS "Enable update access for all" ON public.local_storage_data;
DROP POLICY IF EXISTS "Enable delete access for all" ON public.local_storage_data;
DROP POLICY IF EXISTS "local_storage_data" ON public.local_storage_data;
DROP POLICY IF EXISTS "Authenticated users can do everything" ON public.local_storage_data;
DROP POLICY IF EXISTS "Anon users can only read" ON public.local_storage_data;

-- Create consolidated policies to replace the redundant ones

-- Policy: Enable all access for authenticated users
-- This replaces multiple overlapping policies for authenticated users
CREATE POLICY "Enable all access for authenticated users" 
ON public.local_storage_data 
FOR ALL 
TO authenticated 
USING (true) 
WITH CHECK (true);

-- Policy: Enable read access for anonymous users
-- This allows anonymous users to read data but not modify it
CREATE POLICY "Enable read access for anonymous users" 
ON public.local_storage_data 
FOR SELECT 
TO anon 
USING (true);

-- Ensure RLS is enabled on the table
ALTER TABLE public.local_storage_data ENABLE ROW LEVEL SECURITY;

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON TABLE public.local_storage_data TO authenticated;
GRANT SELECT ON TABLE public.local_storage_data TO anon;
GRANT ALL ON TABLE public.local_storage_data TO service_role;
