import React from 'react';
import { ProductionLine } from '@/types';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';

interface LineSelectorProps {
  selectedLine: ProductionLine;
  onChange: (line: ProductionLine) => void;
  className?: string;
  filterLines?: ProductionLine[];
}

const LineSelector: React.FC<LineSelectorProps> = ({ 
  selectedLine, 
  onChange, 
  className,
  filterLines 
}) => {
  const allLines: Array<{value: ProductionLine; label: string}> = [
    { value: 'tl1', label: 'TL1' },
    { value: 'tl2', label: 'TL2' },
    { value: 'p1', label: 'P1' },
    { value: 'p2', label: 'P2' },
    { value: 'p3', label: 'P3' },
  ];

  // Filter lines if filterLines is provided
  const lines = filterLines 
    ? allLines.filter(line => filterLines.includes(line.value))
    : allLines;

  return (
    <div className={cn("flex flex-wrap gap-2", className)}>
      {lines.map((line) => (
        <Button
          key={line.value}
          variant={selectedLine === line.value ? "secondary" : "outline"}
          onClick={() => onChange(line.value)}
        >
          {line.label}
        </Button>
      ))}
    </div>
  );
};

export default LineSelector;
