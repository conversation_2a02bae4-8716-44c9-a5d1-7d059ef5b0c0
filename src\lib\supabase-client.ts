import { createClient } from '@supabase/supabase-js';

// Supabase client setup
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL as string;
const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY as string;

// Removed diagnostic logs

if (!supabaseUrl || !supabaseKey) {
  console.error('Supabase URL of API key ontbreekt in .env bestand'); // Reverted to original error message
}

export const supabase = createClient(supabaseUrl, supabaseKey, {
  realtime: {
    params: {
      eventsPerSecond: 5, // Default is 10, reduce slightly
    },
  },
  global: {
    fetch: fetch, // Ensure fetch is explicitly passed if needed in certain environments
    headers: { 'x-client-info': 'dagstart-app/1.0.0' }, // Optional: Identify client
  },
  // Optional: Adjust timeouts if network issues are suspected
  // auth: {
  //   autoRefreshToken: true,
  //   persistSession: true,
  //   detectSessionInUrl: true
  // },
});
