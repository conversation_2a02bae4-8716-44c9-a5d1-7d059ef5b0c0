import { createClient } from '@supabase/supabase-js';

// Supabase client setup with enhanced validation
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL as string;
const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY as string;

// Enhanced validation
if (!supabaseUrl || !supabaseKey) {
  console.error('Supabase URL of API key ontbreekt in .env bestand');
  throw new Error('Missing required environment variables: VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY');
}

// Validate URL format
try {
  new URL(supabaseUrl);
} catch {
  throw new Error('Invalid VITE_SUPABASE_URL format');
}

// Validate key format (basic JWT structure check)
if (!supabaseKey.startsWith('eyJ') || supabaseKey.split('.').length !== 3) {
  throw new Error('Invalid VITE_SUPABASE_ANON_KEY format');
}

export const supabase = createClient(supabaseUrl, supabaseKey, {
  realtime: {
    params: {
      eventsPerSecond: 5, // Default is 10, reduce slightly
    },
  },
  global: {
    fetch: fetch, // Ensure fetch is explicitly passed if needed in certain environments
    headers: { 'x-client-info': 'dagstart-app/1.0.0' }, // Optional: Identify client
  },
  // Optional: Adjust timeouts if network issues are suspected
  // auth: {
  //   autoRefreshToken: true,
  //   persistSession: true,
  //   detectSessionInUrl: true
  // },
});
