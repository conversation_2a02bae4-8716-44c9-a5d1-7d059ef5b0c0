import React, { useState, useEffect } from 'react';
import { useAuth } from '@/context/AuthContext';
import { supabase } from '@/lib/supabase-client';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { Loader2, User, KeyRound, BadgeInfo } from 'lucide-react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';

const AccountPage: React.FC = () => {
  const { user } = useAuth();
  const [displayName, setDisplayName] = useState('');
  const [jobTitle, setJobTitle] = useState('');
  const [currentPassword, setCurrentPassword] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingProfile, setIsLoadingProfile] = useState(true);
  const [isResetEmailSending, setIsResetEmailSending] = useState(false);

  // Laad gebruikersgegevens bij het laden van de pagina
  useEffect(() => {
    const loadUserProfile = async () => {
      if (!user) {
        setIsLoadingProfile(false);
        return;
      }

      try {
        // Haal gebruikersprofiel op uit de database
        const { data, error } = await supabase
          .from('user_profiles')
          .select('display_name, job_title')
          .eq('user_id', user.id)
          .maybeSingle();

        if (error) {
          console.error('Fout bij ophalen gebruikersprofiel:', error);
          toast.error('Kon gebruikersprofiel niet laden');
        } else if (data) {
          // Als er profielgegevens zijn, vul de velden in
          setDisplayName(data.display_name || '');
          setJobTitle(data.job_title || '');
        }
      } catch (err) {
        console.error('Onverwachte fout bij laden profiel:', err);
        toast.error('Er is een fout opgetreden bij het laden van uw profiel');
      } finally {
        setIsLoadingProfile(false);
      }
    };

    loadUserProfile();
  }, [user]);

  const handleSendPasswordResetEmail = async () => {
    if (!user?.email) {
      toast.error('Geen e-mailadres beschikbaar');
      return;
    }

    setIsResetEmailSending(true);

    try {
      const { error } = await supabase.auth.resetPasswordForEmail(user.email, {
        redirectTo: `${window.location.origin}/account`,
      });

      if (error) {
        throw new Error(error.message);
      }

      toast.success('Wachtwoordherstel e-mail verzonden');
    } catch (error) {
      console.error('Fout bij verzenden wachtwoordherstel e-mail:', error);
      toast.error(error instanceof Error ? error.message : 'Kon geen wachtwoordherstel e-mail verzenden');
    } finally {
      setIsResetEmailSending(false);
    }
  };

  const handleUpdateProfile = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user) {
      toast.error('U bent niet ingelogd');
      return;
    }

    setIsLoading(true);

    try {
      // Update gebruikersprofiel
      const { error: profileError } = await supabase
        .from('user_profiles')
        .upsert({
          user_id: user.id,
          display_name: displayName,
          job_title: jobTitle,
          updated_at: new Date().toISOString()
        });

      if (profileError) {
        throw new Error(`Fout bij bijwerken profiel: ${profileError.message}`);
      }

      // Als er een wachtwoord is ingevuld, update het wachtwoord
      if (password) {
        if (password !== confirmPassword) {
          throw new Error('Wachtwoorden komen niet overeen');
        }

        if (password.length < 6) {
          throw new Error('Wachtwoord moet minimaal 6 tekens bevatten');
        }

        if (!currentPassword) {
          throw new Error('Voer uw huidige wachtwoord in om uw wachtwoord te wijzigen');
        }

        // Verifieer het huidige wachtwoord door in te loggen
        const { error: signInError } = await supabase.auth.signInWithPassword({
          email: user.email || '',
          password: currentPassword
        });

        if (signInError) {
          throw new Error('Huidig wachtwoord is onjuist');
        }

        const { error: passwordError } = await supabase.auth.updateUser({
          password: password
        });

        if (passwordError) {
          throw new Error(`Fout bij bijwerken wachtwoord: ${passwordError.message}`);
        }

        // Reset wachtwoordvelden
        setCurrentPassword('');
        setPassword('');
        setConfirmPassword('');
      }

      toast.success('Profiel succesvol bijgewerkt');
    } catch (error) {
      console.error('Fout bij bijwerken profiel:', error);
      toast.error(error instanceof Error ? error.message : 'Er is een fout opgetreden bij het bijwerken van uw profiel');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="max-w-md mx-auto">
      <h1 className="text-2xl font-bold mb-4">Mijn Account</h1>
      <p className="mb-6">Hier kunt u uw accountgegevens beheren.</p>

      {isLoadingProfile ? (
        <div className="flex justify-center items-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-3">Profiel laden...</span>
        </div>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Profielgegevens
            </CardTitle>
            <CardDescription>
              Werk uw profielgegevens bij
            </CardDescription>
          </CardHeader>

          <form onSubmit={handleUpdateProfile}>
            <CardContent className="space-y-4">
              <div className="space-y-1">
                <Label htmlFor="email">E-mailadres</Label>
                <Input
                  id="email"
                  type="email"
                  value={user?.email || ''}
                  disabled
                  className="bg-gray-50"
                />
                <p className="text-xs text-muted-foreground">E-mailadres kan niet worden gewijzigd</p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="displayName" className="flex items-center gap-1">
                  <BadgeInfo className="h-4 w-4" />
                  Naam
                </Label>
                <Input
                  id="displayName"
                  type="text"
                  value={displayName}
                  onChange={(e) => setDisplayName(e.target.value)}
                  placeholder="Uw volledige naam"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="jobTitle" className="flex items-center gap-1">
                  <BadgeInfo className="h-4 w-4" />
                  Functie
                </Label>
                <Input
                  id="jobTitle"
                  type="text"
                  value={jobTitle}
                  onChange={(e) => setJobTitle(e.target.value)}
                  placeholder="Uw functietitel"
                />
              </div>

              <div className="pt-4 border-t">
                <h3 className="text-sm font-medium flex items-center gap-1 mb-3">
                  <KeyRound className="h-4 w-4" />
                  Wachtwoord wijzigen
                </h3>

                <div className="space-y-2">
                  <Label htmlFor="currentPassword">Huidig wachtwoord</Label>
                  <Input
                    id="currentPassword"
                    type="password"
                    value={currentPassword}
                    onChange={(e) => setCurrentPassword(e.target.value)}
                    placeholder="Voer uw huidige wachtwoord in"
                    required={!!password || !!confirmPassword}
                  />
                  <div className="text-xs text-right mt-1">
                    <button
                      type="button"
                      onClick={handleSendPasswordResetEmail}
                      className="text-primary hover:underline"
                      disabled={isResetEmailSending}
                    >
                      {isResetEmailSending ? 'E-mail verzenden...' : 'Wachtwoord vergeten?'}
                    </button>
                  </div>
                </div>

                <div className="space-y-2 mt-3">
                  <Label htmlFor="password">Nieuw wachtwoord</Label>
                  <Input
                    id="password"
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="Laat leeg om niet te wijzigen"
                    minLength={6}
                  />
                </div>

                <div className="space-y-2 mt-2">
                  <Label htmlFor="confirmPassword">Bevestig wachtwoord</Label>
                  <Input
                    id="confirmPassword"
                    type="password"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    placeholder="Bevestig nieuw wachtwoord"
                    minLength={6}
                  />
                </div>
              </div>
            </CardContent>

            <CardFooter className="flex justify-end">
              <Button type="submit" disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Bezig met opslaan...
                  </>
                ) : (
                  'Opslaan'
                )}
              </Button>
            </CardFooter>
          </form>
        </Card>
      )}
    </div>
  );
};

export default AccountPage;
