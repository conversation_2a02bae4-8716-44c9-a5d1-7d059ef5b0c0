import React from 'react';
import { format } from 'date-fns';
import { nl } from 'date-fns/locale';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { CheckCircle, AlertTriangle } from 'lucide-react';
import { ProductionLine, HistoryEntry } from '@/types';

export interface ProductionTableProps {
  data: HistoryEntry[];
  selectedLine: ProductionLine | 'all';
}

export const ProductionTable: React.FC<ProductionTableProps> = ({ data, selectedLine }) => {
  const flattenedRows = data.flatMap(entry => 
    Object.entries(entry.data).flatMap(([line, lineData]) => {
      if (selectedLine !== 'all' && line !== selectedLine) return [];
      
      return lineData.rows.map(row => ({
        date: row.date,
        line: line as ProductionLine,
        od: row.od,
        md: row.md,
        nd: row.nd
      }));
    })
  );

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[100px]">Datum</TableHead>
            {selectedLine === 'all' && <TableHead>Lijn</TableHead>}
            <TableHead>Ochtend</TableHead>
            <TableHead>Middag</TableHead>
            <TableHead>Nacht</TableHead>
            <TableHead>Totaal</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {flattenedRows.map((row, index) => {
            const dayTotal = 
              (Number(row.od.production) || 0) +
              (Number(row.md.production) || 0) +
              (Number(row.nd.production) || 0);

            return (
              <TableRow key={index}>
                <TableCell>{format(new Date(row.date), 'dd MMM', { locale: nl })}</TableCell>
                {selectedLine === 'all' && (
                  <TableCell className="font-medium">{row.line.toUpperCase()}</TableCell>
                )}
                <TableCell>{row.od.production || '-'}</TableCell>
                <TableCell>{row.md.production || '-'}</TableCell>
                <TableCell>{row.nd.production || '-'}</TableCell>
                <TableCell className="font-medium">{dayTotal.toLocaleString('nl-NL')}</TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
}; 