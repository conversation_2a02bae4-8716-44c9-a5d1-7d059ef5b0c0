import React, { useState, useEffect } from 'react';
import { Disruption, RcaEntry } from '@/types'; // Import RcaEntry
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter, DialogClose } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { v4 as uuidv4 } from 'uuid'; // Import uuid

interface RcaFormDialogProps {
  disruption: Disruption | null; // Disruption context
  rcaEntryToEdit?: RcaEntry | null; // Optional entry to edit
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (rcaEntry: RcaEntry) => void; // Expect a complete RcaEntry
}

const RcaFormDialog: React.FC<RcaFormDialogProps> = ({
  disruption,
  rcaEntryToEdit,
  open,
  onOpenChange,
  onSave,
}) => {
  const [who, setWho] = useState('');
  const [what, setWhat] = useState('');
  const [where, setWhere] = useState('');
  const [how, setHow] = useState('');
  const [followUp, setFollowUp] = useState('');
  // const [solution, setSolution] = useState(''); // Removed solution state

  const isEditing = !!rcaEntryToEdit;

  useEffect(() => {
    if (open) {
      if (isEditing && rcaEntryToEdit) {
        // Populate form with existing data if editing
        setWho(rcaEntryToEdit.who || '');
        setWhat(rcaEntryToEdit.what || '');
        setWhere(rcaEntryToEdit.where || '');
        setHow(rcaEntryToEdit.how || '');
        setFollowUp(rcaEntryToEdit.followUp || '');
        // setSolution(rcaEntryToEdit.solution || ''); // Removed solution population
      } else {
        // Reset form if adding new
        setWho('');
        setWhat('');
        setWhere('');
        setHow('');
        setFollowUp('');
        // setSolution(''); // Removed solution reset
      }
    }
  }, [open, rcaEntryToEdit, isEditing]); // Depend on open and entryToEdit

  const handleSave = () => {
    const savedEntry: RcaEntry = {
      id: isEditing ? rcaEntryToEdit!.id : uuidv4(), // Use existing ID or generate new
      timestamp: isEditing ? rcaEntryToEdit!.timestamp : new Date().toISOString(), // Keep original timestamp if editing, else new
      who: who.trim() || undefined,
      what: what.trim() || undefined,
      where: where.trim() || undefined,
      how: how.trim() || undefined,
      followUp: followUp.trim() || undefined,
      // solution: solution.trim() || undefined, // Removed solution saving
    };
    onSave(savedEntry); // Pass the complete entry object
    onOpenChange(false); // Close dialog after save
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>{isEditing ? 'Root Cause Analyse Bewerken' : 'Nieuwe Root Cause Analyse Toevoegen'}</DialogTitle>
          <DialogDescription>
            {isEditing ? 'Wijzig de analyse voor de storing.' : 'Beantwoord de vragen om een nieuwe analyse voor de storing toe te voegen.'}
            <br />
            Storing: {disruption?.description} ({disruption?.equipment || 'N/A'})
            {isEditing && rcaEntryToEdit?.timestamp && (
              <span className="block text-xs text-gray-500 mt-1">Analyse van: {new Date(rcaEntryToEdit.timestamp).toLocaleString('nl-NL')}</span>
            )}
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4 max-h-[60vh] overflow-y-auto pr-2">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="rca-who" className="text-right col-span-1">
              Wie?
            </Label>
            <Textarea
              id="rca-who"
              value={who}
              onChange={(e) => setWho(e.target.value)}
              className="col-span-3"
              rows={2}
              placeholder="Wie was betrokken of heeft het opgemerkt?"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="rca-what" className="text-right col-span-1">
              Wat?
            </Label>
            <Textarea
              id="rca-what"
              value={what}
              onChange={(e) => setWhat(e.target.value)}
              className="col-span-3"
              rows={3}
              placeholder="Wat is er precies gebeurd? Wat was het effect?"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="rca-where" className="text-right col-span-1">
              Waar?
            </Label>
            <Textarea
              id="rca-where"
              value={where}
              onChange={(e) => setWhere(e.target.value)}
              className="col-span-3"
              rows={2}
              placeholder="Waar vond het plaats?"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="rca-how" className="text-right col-span-1">
              Hoe?
            </Label>
            <Textarea
              id="rca-how"
              value={how}
              onChange={(e) => setHow(e.target.value)}
              className="col-span-3"
              rows={3}
              placeholder="Hoe kon dit gebeuren? Wat waren de directe oorzaken?"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="rca-followup" className="text-right col-span-1">
              Vervolg?
            </Label>
            <Textarea
              id="rca-followup"
              value={followUp}
              onChange={(e) => setFollowUp(e.target.value)}
              className="col-span-3"
              rows={2}
              placeholder="Welke vervolgstappen zijn nodig?"
            />
          </div>
          {/* Removed Solution Textarea */}
          {/* <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="rca-solution" className="text-right col-span-1">
              Oplossing?
            </Label>
            <Textarea
              id="rca-solution"
              value={solution}
              onChange={(e) => setSolution(e.target.value)}
              className="col-span-3"
              rows={3}
              placeholder="Wat is de (voorgestelde) structurele oplossing?"
            />
          </div> */}
        </div>
        <DialogFooter>
          <DialogClose asChild>
             <Button type="button" variant="outline">Annuleren</Button>
          </DialogClose>
          <Button type="button" onClick={handleSave}>{isEditing ? 'Wijzigingen Opslaan' : 'Analyse Opslaan'}</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default RcaFormDialog;