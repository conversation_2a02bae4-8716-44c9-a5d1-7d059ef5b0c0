// src/pages/management/UserManagementPage.tsx
import React, { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase-client';
import { useAuth } from '@/context/AuthContext';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';
import { Loader2, UserX, Users, Shield, RefreshCw } from 'lucide-react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';

interface User {
  id: string;
  email: string;
  created_at: string;
  is_manager: boolean;
  team: string | null;
}

const UserManagementPage: React.FC = () => {
  const { user } = useAuth();
  const [users, setUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [isCheckingAuth, setIsCheckingAuth] = useState(true);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [userToDelete, setUserToDelete] = useState<User | null>(null);

  // Check if current user is authorized to manage users
  useEffect(() => {
    const checkAuthorization = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession();

        if (!session) {
          setIsAuthorized(false);
          setIsCheckingAuth(false);
          return;
        }

        const { data: authData, error: authError } = await supabase
          .from('authorized_managers')
          .select('user_id')
          .eq('user_id', session.user.id)
          .single();

        if (authError) {
          console.error('Autorisatiefout:', authError);
          setIsAuthorized(false);
        } else {
          console.log('Autorisatie resultaat:', authData);
          setIsAuthorized(true);
        }
      } catch (error) {
        console.error('Fout bij controleren autorisatie:', error);
        setIsAuthorized(false);
      } finally {
        setIsCheckingAuth(false);
      }
    };

    checkAuthorization();
  }, []);

  // Fetch users
  const fetchUsers = async () => {
    setIsLoading(true);
    try {
      // Get all users from auth.users
      const { data: authUsers, error: authError } = await supabase
        .from('auth_users_view')
        .select('*')
        .order('created_at', { ascending: false });

      if (authError) {
        throw new Error(`Fout bij ophalen gebruikers: ${authError.message}`);
      }

      // Get all managers
      const { data: managers, error: managersError } = await supabase
        .from('authorized_managers')
        .select('user_id');

      if (managersError) {
        console.warn('Kon beheerders niet ophalen:', managersError);
      }

      // Get all team assignments
      const { data: teamAssignments, error: teamError } = await supabase
        .from('user_teams')
        .select('user_id, team');

      if (teamError) {
        console.warn('Kon team toewijzingen niet ophalen:', teamError);
      }

      // Combine the data
      const managerIds = managers ? managers.map(m => m.user_id) : [];
      const teamMap = teamAssignments ? teamAssignments.reduce((acc, curr) => {
        acc[curr.user_id] = curr.team;
        return acc;
      }, {} as Record<string, string>) : {};

      const combinedUsers = authUsers.map(user => ({
        ...user,
        is_manager: managerIds.includes(user.id),
        team: teamMap[user.id] || null
      }));

      setUsers(combinedUsers);
    } catch (error) {
      console.error('Fout bij ophalen gebruikers:', error);
      toast.error('Fout bij ophalen gebruikers', {
        description: error instanceof Error ? error.message : 'Er is een fout opgetreden bij het ophalen van gebruikers'
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (isAuthorized) {
      fetchUsers();
    }
  }, [isAuthorized]);

  const handleDeleteUser = async () => {
    if (!userToDelete) return;

    setIsDeleting(true);
    try {
      // Call the delete_user function
      const { data, error } = await supabase
        .rpc('delete_user', {
          p_user_email: userToDelete.email
        });

      if (error) {
        throw new Error(`Fout bij verwijderen gebruiker: ${error.message}`);
      }

      toast.success('Gebruiker verwijderd', {
        description: `Gebruiker ${userToDelete.email} is succesvol verwijderd.`
      });

      // Refresh the user list
      fetchUsers();
    } catch (error) {
      console.error('Fout bij verwijderen gebruiker:', error);
      toast.error('Fout bij verwijderen gebruiker', {
        description: error instanceof Error ? error.message : 'Er is een fout opgetreden bij het verwijderen van de gebruiker'
      });
    } finally {
      setIsDeleting(false);
      setDeleteDialogOpen(false);
      setUserToDelete(null);
    }
  };

  if (isCheckingAuth) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
        <p className="ml-2">Autorisatie controleren...</p>
      </div>
    );
  }

  if (!isAuthorized) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4">
        <Shield className="h-16 w-16 text-red-500 mb-4" />
        <h1 className="text-2xl font-bold mb-2">Geen toegang</h1>
        <p className="text-center mb-4">
          U heeft geen toestemming om deze pagina te bekijken. Alleen beheerders kunnen gebruikers beheren.
        </p>
        <Button onClick={() => window.history.back()}>Terug</Button>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <Users className="h-6 w-6 mr-2" />
          <h1 className="text-2xl font-bold">Gebruikersbeheer</h1>
        </div>
        <Button onClick={fetchUsers} variant="outline" size="sm">
          <RefreshCw className="h-4 w-4 mr-2" />
          Vernieuwen
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Gebruikers</CardTitle>
          <CardDescription>
            Beheer gebruikers van het Dagstart systeem
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center items-center py-8">
              <Loader2 className="h-8 w-8 animate-spin" />
              <p className="ml-2">Gebruikers laden...</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>E-mailadres</TableHead>
                  <TableHead>Rol</TableHead>
                  <TableHead>Ploeg</TableHead>
                  <TableHead>Aangemaakt op</TableHead>
                  <TableHead className="text-right">Acties</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {users.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-8 text-muted-foreground">
                      Geen gebruikers gevonden
                    </TableCell>
                  </TableRow>
                ) : (
                  users.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell>{user.email}</TableCell>
                      <TableCell>
                        {user.is_manager ? (
                          <Badge variant="outline" className="bg-amber-100 text-amber-600 hover:bg-amber-100">
                            <Shield className="h-3 w-3 mr-1" /> Beheerder
                          </Badge>
                        ) : (
                          <Badge variant="outline" className="bg-blue-100 text-blue-600 hover:bg-blue-100">
                            Gebruiker
                          </Badge>
                        )}
                      </TableCell>
                      <TableCell>
                        {user.team ? (
                          <Badge variant="outline" className="bg-green-100 text-green-600 hover:bg-green-100">
                            {user.team}
                          </Badge>
                        ) : (
                          <span className="text-muted-foreground text-sm">Geen ploeg</span>
                        )}
                      </TableCell>
                      <TableCell>{new Date(user.created_at).toLocaleString('nl-NL')}</TableCell>
                      <TableCell className="text-right">
                        <AlertDialog open={deleteDialogOpen && userToDelete?.id === user.id} onOpenChange={setDeleteDialogOpen}>
                          <AlertDialogTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-red-500 hover:text-red-700 hover:bg-red-50"
                              onClick={() => setUserToDelete(user)}
                            >
                              <UserX className="h-4 w-4 mr-1" />
                              Verwijderen
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>Gebruiker verwijderen</AlertDialogTitle>
                              <AlertDialogDescription>
                                Weet u zeker dat u de gebruiker <strong>{userToDelete?.email}</strong> wilt verwijderen?
                                Deze actie kan niet ongedaan worden gemaakt.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel onClick={() => setUserToDelete(null)}>Annuleren</AlertDialogCancel>
                              <AlertDialogAction
                                onClick={handleDeleteUser}
                                className="bg-red-500 hover:bg-red-600"
                                disabled={isDeleting}
                              >
                                {isDeleting ? (
                                  <>
                                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                    Bezig met verwijderen...
                                  </>
                                ) : (
                                  'Verwijderen'
                                )}
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          )}
        </CardContent>
        <CardFooter className="flex justify-between">
          <div className="text-sm text-muted-foreground">
            Totaal aantal gebruikers: {users.length}
          </div>
          <Button onClick={() => window.location.href = '/management/register'} variant="outline">
            Nieuwe gebruiker
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
};

export default UserManagementPage;
