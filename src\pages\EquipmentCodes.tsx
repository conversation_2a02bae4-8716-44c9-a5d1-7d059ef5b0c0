// Corrected src/pages/EquipmentCodes.tsx
import React, { useState, useMemo, useEffect, useCallback } from 'react';
import { useProduction } from '@/context/ProductionContext';
import { ProductionLine, EquipmentEntry } from '@/types';
import LineSelector from '@/components/common/LineSelector';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger
} from '@/components/ui/accordion';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog';
import { Plus, Trash, Edit, Save, X, Loader2, Upload, FileText } from 'lucide-react';
import { toast } from 'sonner';
import * as equipmentUtils from '@/utils/equipmentUtils';
// Removed import for SettingsSubNav
// Removed import for Tabs
import { translateText } from '@/lib/translation';
import { useIsMobile } from '@/hooks/use-mobile';
import {
  CalendarDays, ClockIcon, PlusCircle, Trash2, Check, Calendar as CalendarIcon,
  Pencil, ChevronUp, ChevronDown, ChevronsUpDown, Check as CheckIcon
} from 'lucide-react';
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";

const EquipmentCodes: React.FC = () => {
  const {
    equipmentOptions,
    // Removed updateEquipmentOptions from context destructuring
    addEquipmentOption: contextAddEquipmentOption,
    removeEquipmentOption: contextRemoveEquipmentOption,
    editEquipmentOption: contextEditEquipmentOption,
    addEquipmentArea: contextAddEquipmentArea,
    removeEquipmentArea: contextRemoveEquipmentArea,
    editEquipmentArea: contextEditEquipmentArea,
    isFlyLocked
  } = useProduction();

  const [selectedLine, setSelectedLine] = useState<ProductionLine>('tl1');
  const [openAreaDialog, setOpenAreaDialog] = useState(false);
  const [openEquipmentDialog, setOpenEquipmentDialog] = useState(false);
  const [openEditAreaDialog, setOpenEditAreaDialog] = useState(false);
  const [openEditEquipmentDialog, setOpenEditEquipmentDialog] = useState(false);
  const [selectedArea, setSelectedArea] = useState<string | null>(null);
  const [selectedEquipment, setSelectedEquipment] = useState<EquipmentEntry | null>(null);

  // Form states
  const [newAreaCode, setNewAreaCode] = useState('');
  const [newAreaLabel, setNewAreaLabel] = useState('');
  const [newEquipmentCode, setNewEquipmentCode] = useState('');
  const [newEquipmentLabelNL, setNewEquipmentLabelNL] = useState('');
  const [newEquipmentLabelEN, setNewEquipmentLabelEN] = useState('');
  const [isAddingEquipment, setIsAddingEquipment] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  const [isDraggingOver, setIsDraggingOver] = useState(false);

  // Current line's equipment options
  const lineEquipment = useMemo(() => {
    return equipmentOptions[selectedLine] || {};
  }, [equipmentOptions, selectedLine]);

  // Function to add a new equipment area
  const handleAddArea = () => {
    if (!newAreaLabel) {
      toast.error('Vul de gebied beschrijving in');
      return;
    }
    if (lineEquipment[newAreaLabel]) {
      toast.error('Dit gebied bestaat al voor deze lijn');
      return;
    }
    // Use context function directly
    contextAddEquipmentArea(selectedLine, newAreaLabel, newAreaLabel);
    toast.success(`Gebied ${newAreaLabel} toegevoegd`);
    setNewAreaLabel('');
    setOpenAreaDialog(false);
  };

  // Function to add a new equipment
  const handleAddEquipment = async () => {
    if (!selectedArea || !newEquipmentCode || !newEquipmentLabelNL) {
      toast.error('Vul alle verplichte velden in');
      return;
    }
    const exists = lineEquipment[selectedArea]?.some(
      eq => eq.value === newEquipmentCode
    );
    if (exists) {
      toast.error('Deze onderdeel code bestaat al');
      return;
    }
    setIsAddingEquipment(true);
    let translatedLabelEN = newEquipmentLabelNL;
    try {
      translatedLabelEN = await translateText(newEquipmentLabelNL, 'en');
    } catch (error) {
      console.error("Error during translation:", error);
      toast.error("Vertaling mislukt, Engelse naam is hetzelfde als Nederlands.");
      translatedLabelEN = newEquipmentLabelNL;
    }
    const newEquipment: EquipmentEntry = {
      id: `${Date.now()}`,
      value: newEquipmentCode,
      label_nl: newEquipmentLabelNL,
      label_en: translatedLabelEN
    };
    // Use context function directly
    contextAddEquipmentOption(selectedLine, selectedArea, newEquipment);
    toast.success(`Onderdeel ${newEquipmentCode} toegevoegd`);
    setNewEquipmentCode('');
    setNewEquipmentLabelNL('');
    setNewEquipmentLabelEN('');
    setOpenEquipmentDialog(false);
    setIsAddingEquipment(false);
  };

  // Function to remove an equipment area
  const handleRemoveArea = (areaCode: string) => {
    if (confirm(`Weet je zeker dat je gebied ${areaCode} wilt verwijderen? Alle onderdelen in dit gebied wordt ook verwijderd.`)) {
      // Use context function directly
      contextRemoveEquipmentArea(selectedLine, areaCode);
      toast.success(`Gebied ${areaCode} verwijderd`);
    }
  };

  // Function to remove an equipment
  const handleRemoveEquipment = (areaCode: string, equipment: EquipmentEntry) => {
    if (confirm(`Weet je zeker dat je onderdeel ${equipment.value} wilt verwijderen?`)) {
      // Use context function directly
      contextRemoveEquipmentOption(selectedLine, areaCode, equipment.id);
      toast.success(`Onderdeel ${equipment.value} verwijderd`);
    }
  };

  // Dialog handlers
  const openAddAreaDialog = () => {
    setNewAreaCode('');
    setNewAreaLabel('');
    setOpenAreaDialog(true);
  };

  const openAddEquipmentDialog = (areaCode: string) => {
    setSelectedArea(areaCode);
    setNewEquipmentCode('');
    setNewEquipmentLabelNL('');
    setNewEquipmentLabelEN('');
    setOpenEquipmentDialog(true);
  };

  // Edit area handlers
  const openEditAreaDialogHandler = (areaCode: string) => {
    setSelectedArea(areaCode);
    setNewAreaCode(areaCode);
    setNewAreaLabel(areaCode);
    setOpenEditAreaDialog(true);
  };

  const handleEditArea = () => {
    if (!selectedArea || !newAreaCode) {
      toast.error('Gebied code is verplicht');
      return;
    }
    if (selectedArea !== newAreaCode && lineEquipment[newAreaCode]) {
      toast.error('Deze gebiedscode bestaat al');
      return;
    }
    // Use context function directly
    contextEditEquipmentArea(selectedLine, selectedArea, newAreaCode, newAreaLabel);
    toast.success(`Gebied ${selectedArea} bijgewerkt`);
    setSelectedArea(null);
    setNewAreaCode('');
    setNewAreaLabel('');
    setOpenEditAreaDialog(false);
  };

  // Edit equipment handlers
  const openEditEquipmentDialogHandler = (areaCode: string, equipment: EquipmentEntry) => {
    setSelectedArea(areaCode);
    setSelectedEquipment(equipment);
    setNewEquipmentCode(equipment.value);
    setNewEquipmentLabelNL(equipment.label_nl);
    setNewEquipmentLabelEN(equipment.label_en);
    setOpenEditEquipmentDialog(true);
  };

  const handleEditEquipment = () => {
    if (!selectedArea || !selectedEquipment || !newEquipmentCode || !newEquipmentLabelNL) {
      toast.error('Vul alle verplichte velden in');
      return;
    }
    const exists = lineEquipment[selectedArea]?.some(
      eq => eq.value === newEquipmentCode && eq.id !== selectedEquipment.id
    );
    if (exists) {
      toast.error('Deze onderdeel code bestaat al');
      return;
    }
    const updatedEquipment: EquipmentEntry = {
      ...selectedEquipment,
      value: newEquipmentCode,
      label_nl: newEquipmentLabelNL,
      label_en: newEquipmentLabelEN || newEquipmentLabelNL
    };
    // Use context function directly
    contextEditEquipmentOption(selectedLine, selectedArea, selectedEquipment.id, updatedEquipment);
    toast.success(`Onderdeel ${newEquipmentCode} bijgewerkt`);
    setSelectedArea(null);
    setSelectedEquipment(null);
    setNewEquipmentCode('');
    setNewEquipmentLabelNL('');
    setNewEquipmentLabelEN('');
    setOpenEditEquipmentDialog(false);
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files.length > 0) {
      const file = event.target.files[0];

      // Enhanced security validation
      const maxSize = 5 * 1024 * 1024; // 5MB limit
      if (file.size > maxSize) {
        toast.error("Bestand te groot. Maximum 5MB toegestaan.");
        setSelectedFile(null);
        event.target.value = '';
        return;
      }

      if (file.size < 10) {
        toast.error("Bestand te klein. Minimum 10 bytes vereist.");
        setSelectedFile(null);
        event.target.value = '';
        return;
      }

      if ((file.type === 'text/csv' || file.type === 'application/csv') && file.name.endsWith('.csv')) {
        setSelectedFile(file);
      } else {
        toast.error("Selecteer a.u.b. een geldig .csv bestand.");
        setSelectedFile(null);
        event.target.value = ''; // Reset file input
      }
    } else {
      setSelectedFile(null);
    }
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault(); // Necessary to allow drop
    setIsDraggingOver(true);
  };

  const handleDragLeave = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    setIsDraggingOver(false);
  };

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    setIsDraggingOver(false);
    if (event.dataTransfer.files && event.dataTransfer.files.length > 0) {
      const file = event.dataTransfer.files[0];
       if (file.type === 'text/csv' || file.name.endsWith('.csv')) {
         setSelectedFile(file);
         toast.info(`Bestand geselecteerd: ${file.name}`);
       } else {
         toast.error("Ongeldig bestandstype. Sleep a.u.b. een .csv bestand.");
         setSelectedFile(null);
       }
      event.dataTransfer.clearData();
    }
  };

  // Basic CSV Parser (Assumes simple CSV, no commas within quotes)
  const parseCSV = (text: string): Record<string, string>[] => {
    const lines = text.trim().split(/\r?\n/);
    if (lines.length < 2) return []; // Need header + at least one data row

    const header = lines[0].split(',').map(h => h.trim());
    const data = [];

    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split(',').map(v => v.trim());
      if (values.length === header.length) {
        const row: Record<string, string> = {};
        header.forEach((col, index) => {
          row[col] = values[index];
        });
        data.push(row);
      }
    }
    return data;
  };

  const handleUpload = () => {
    if (!selectedFile) {
      toast.error("Selecteer eerst een bestand.");
      return;
    }

    setIsUploading(true);
    const reader = new FileReader();

    reader.onload = (event) => {
      const text = event.target?.result as string;
      if (!text) {
        toast.error("Kon het bestand niet lezen.");
        setIsUploading(false);
        return;
      }

      try {
        const parsedData = parseCSV(text);
        if (parsedData.length === 0) {
           toast.warning("CSV is leeg of kon niet geparsed worden.");
           setIsUploading(false);
           return;
        }

        // Validate header
        const requiredHeaders = ['Line', 'Area', 'EquipmentCode', 'EquipmentNameNL'];
        const header = Object.keys(parsedData[0]);
        const missingHeaders = requiredHeaders.filter(h => !header.includes(h));
        if (missingHeaders.length > 0) {
          toast.error(`CSV mist verplichte kolommen: ${missingHeaders.join(', ')}`);
          setIsUploading(false);
          return;
        }

        let addedCount = 0;
        let errorCount = 0;

        // Process each row
        parsedData.forEach((row, index) => {
          const line = row.Line as ProductionLine | undefined;
          const area = row.Area;
          const code = row.EquipmentCode; // This is the 'value'
          const labelNL = row.EquipmentNameNL;
          const labelEN = row.EquipmentNameEN || labelNL; // Use NL as fallback for EN

          if (!line || !area || !code || !labelNL) {
            console.warn(`Skipping row ${index + 2}: Missing required data.`);
            errorCount++;
            return; // Skip row if required data is missing
          }

          // Convert line to lowercase and trim whitespace for case-insensitive comparison
          const processedLine = line.trim().toLowerCase();

          if (!['tl1', 'tl2', 'p1', 'p2', 'p3'].includes(processedLine)) {
             console.warn(`Skipping row ${index + 2}: Invalid line '${line}'. Allowed: tl1, tl2, p1, p2, p3.`);
             errorCount++;
             return; // Skip row if line is invalid
          }

          try {
             // Check if area exists for the line, if not, add it
             // Note: addEquipmentArea expects (line, areaCode, areaLabel)
             // We use 'area' as both code and label here.
             if (!equipmentOptions[processedLine] || !equipmentOptions[processedLine][area]) {
                contextAddEquipmentArea(processedLine as ProductionLine, area, area); // Use processedLine
                console.log(`Area '${area}' toegevoegd aan lijn '${processedLine}'.`);
             }

             // Check if equipment code already exists in this area for this line
             const equipmentExists = equipmentOptions[processedLine]?.[area]?.some(eq => eq.value === code); // Use processedLine

             if (!equipmentExists) {
                 const newEquipment: EquipmentEntry = {
                    id: crypto.randomUUID(),
                    value: code,
                    label_nl: labelNL,
                    label_en: labelEN
                 };
                 contextAddEquipmentOption(processedLine as ProductionLine, area, newEquipment); // Use processedLine
                 addedCount++;
             } else {
                 console.warn(`Skipping row ${index + 2}: Equipment code '${code}' bestaat al in gebied '${area}' voor lijn '${processedLine}'.`);
                 errorCount++;
             }
          } catch (e) {
              console.error(`Error processing row ${index + 2}:`, e);
              errorCount++;
          }
        });

        if (addedCount > 0) {
           toast.success(`${addedCount} onderdeel/onderdelen succesvol toegevoegd.`);
        }
        if (errorCount > 0) {
           toast.warning(`${errorCount} rij(en) konden niet worden verwerkt (zie console voor details).`);
        }
        if (addedCount === 0 && errorCount === 0) {
             toast.info("Geen nieuwe onderdelen gevonden om toe te voegen.");
        }

      } catch (parseError) {
        console.error("Error parsing CSV:", parseError);
        toast.error("Fout bij het parsen van het CSV bestand.");
      } finally {
        setIsUploading(false);
        setSelectedFile(null);
        setUploadDialogOpen(false);
      }
    };

    reader.onerror = () => {
      toast.error("Fout bij het lezen van het bestand.");
      setIsUploading(false);
      setSelectedFile(null);
    };

    reader.readAsText(selectedFile);
  };

  // Function to reset upload state when dialog closes
  const handleUploadDialogChange = (open: boolean) => {
     setUploadDialogOpen(open);
     if (!open) {
         setSelectedFile(null);
         // Reset file input visually if possible (might need ref)
     }
  };

  return (
    <div className="p-6 space-y-6">
      {/* Removed Tabs wrapper and SettingsSubNav */}
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">Apparaat Beheer</h2>
        <div className="flex space-x-2">
          <Dialog open={uploadDialogOpen} onOpenChange={handleUploadDialogChange}>
             <DialogTrigger asChild>
               <Button variant="outline" size="sm" disabled={isFlyLocked}>
                 <Upload className="h-4 w-4 mr-1" /> Upload CSV
               </Button>
             </DialogTrigger>
             <DialogContent className="sm:max-w-2xl">
                <DialogHeader>
                   <DialogTitle>Upload Apparatuur CSV</DialogTitle>
                   <DialogDescription>
                      Upload een .csv bestand met kolommen: Line, Area, EquipmentCode, EquipmentNameNL, EquipmentNameEN (optioneel).
                      <a href="/equipment-template.csv" download className="text-blue-600 hover:underline ml-2 text-sm">Download Template</a>
                   </DialogDescription>
                </DialogHeader>
                <div
                   className={`mt-4 border-2 border-dashed rounded-lg p-8 text-center ${isDraggingOver ? 'border-blue-500 bg-blue-50' : 'border-gray-300'}`}
                   onDragOver={handleDragOver}
                   onDragLeave={handleDragLeave}
                   onDrop={handleDrop}
                 >
                   <input
                     type="file"
                     id="csv-upload"
                     accept=".csv"
                     onChange={handleFileChange}
                     className="hidden"
                   />
                   <label
                     htmlFor="csv-upload"
                     className="cursor-pointer flex flex-col items-center justify-center"
                   >
                     <FileText className="h-12 w-12 text-gray-400 mb-2" />
                     <span className="text-sm text-gray-600">
                       {selectedFile ? `Geselecteerd: ${selectedFile.name}` : 'Sleep bestand hier of klik om te selecteren'}
                     </span>
                     <span className="text-xs text-gray-500 mt-1">(.csv bestand)</span>
                   </label>
                 </div>
                 <DialogFooter>
                    <Button
                       onClick={handleUpload}
                       disabled={!selectedFile || isUploading || isFlyLocked}
                     >
                       {isUploading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Upload className="mr-2 h-4 w-4" />}
                       Uploaden en Verwerken
                    </Button>
                 </DialogFooter>
              </DialogContent>
           </Dialog>

          <Button size="sm" onClick={openAddAreaDialog} disabled={isFlyLocked}>
            <Plus className="h-4 w-4 mr-1" /> Nieuw Gebied
          </Button>
        </div>
      </div>

      <div className="p-4">
        {/* Corrected prop name and removed disabled prop */}
        <LineSelector selectedLine={selectedLine} onChange={setSelectedLine} />
      </div>

      <div className="p-4">
        {Object.keys(lineEquipment).length === 0 ? (
          <p className="text-gray-500 text-center">Geen gebieden of onderdelen gevonden voor {selectedLine.toUpperCase()}.</p>
        ) : (
          <Accordion type="multiple" className="space-y-4">
            {Object.entries(lineEquipment).map(([areaCode, equipmentList]: [string, EquipmentEntry[]]) => (
              <AccordionItem key={areaCode} value={areaCode} className="border rounded-lg">
                <AccordionTrigger className="px-4 py-3 flex justify-between items-center w-full hover:bg-gray-50">
                  <span className="font-medium">{areaCode}</span>
                  <div className="flex space-x-1">
                    <Button variant="ghost" size="icon" className="h-7 w-7" onClick={(e) => { e.stopPropagation(); openEditAreaDialogHandler(areaCode); }} disabled={isFlyLocked}>
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="icon" className="h-7 w-7 text-red-500 hover:text-red-600" onClick={(e) => { e.stopPropagation(); handleRemoveArea(areaCode); }} disabled={isFlyLocked}>
                      <Trash className="h-4 w-4" />
                    </Button>
                  </div>
                </AccordionTrigger>
                <AccordionContent className="px-4 py-2">
                  <div className="flex justify-end mb-2">
                    <Button variant="outline" size="sm" onClick={() => openAddEquipmentDialog(areaCode)} disabled={isFlyLocked}>
                      <Plus className="h-4 w-4 mr-1" /> Onderdeel Toevoegen
                    </Button>
                  </div>
                  {Array.isArray(equipmentList) && equipmentList.length === 0 ? (
                    <p className="text-gray-500 text-sm py-2">Geen onderdelen in dit gebied.</p>
                  ) : (
                      Array.isArray(equipmentList) && equipmentList.map((equipment) => (
                        <div key={equipment.id} className="flex justify-between items-center py-2 border-b last:border-b-0">
                          <div>
                            <span className="font-medium">{equipment.value}</span>
                            <span className="text-sm text-gray-600 ml-2">({equipment.label_nl} / {equipment.label_en || 'N/A'})</span>
                          </div>
                          <div className="flex justify-end space-x-1 mt-2">
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-6 w-6"
                              onClick={() => openEditEquipmentDialogHandler(areaCode, equipment)}
                              disabled={isFlyLocked}
                            >
                              <Edit className="h-3 w-3" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-6 w-6 text-red-500 hover:text-red-600"
                              onClick={() => handleRemoveEquipment(areaCode, equipment)}
                              disabled={isFlyLocked}
                            >
                              <Trash className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      ))
                  )}
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        )}
      </div>

      {/* Add Area Dialog */}
      <Dialog open={openAreaDialog} onOpenChange={setOpenAreaDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Nieuw Gebied Toevoegen</DialogTitle>
            <DialogDescription>
              Voeg een nieuw gebied toe aan productielijn {selectedLine.toUpperCase()}. De beschrijving wordt gebruikt als unieke code.
            </DialogDescription>
          </DialogHeader>
          <div>
            <Label htmlFor="area-label">Gebied Beschrijving</Label>
            <Input
              id="area-label"
              value={newAreaLabel}
              onChange={(e) => setNewAreaLabel(e.target.value)}
              placeholder="bv. Extruder"
            />
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setOpenAreaDialog(false)}>Annuleren</Button>
            <Button onClick={handleAddArea} disabled={isFlyLocked}>Toevoegen</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Add Equipment Dialog */}
      <Dialog open={openEquipmentDialog} onOpenChange={setOpenEquipmentDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Nieuw Onderdeel Toevoegen</DialogTitle>
            <DialogDescription>
              Voeg een nieuw onderdeel toe aan gebied "{selectedArea}" voor lijn {selectedLine.toUpperCase()}.
            </DialogDescription>
          </DialogHeader>
          <div>
            <Label htmlFor="equipment-code">Onderdeel Code</Label>
            <Input
              id="equipment-code"
              value={newEquipmentCode}
              onChange={(e) => setNewEquipmentCode(e.target.value)}
              placeholder="bv. EXTR-001"
            />
          </div>
          <div>
            <Label htmlFor="equipment-label-nl">Nederlandse Naam</Label>
            <Input
              id="equipment-label-nl"
              value={newEquipmentLabelNL}
              onChange={(e) => setNewEquipmentLabelNL(e.target.value)}
              placeholder="bv. Hoofdmotor"
            />
          </div>
          {/* Optional English Label Input */}
          {/* <div>
            <Label htmlFor="equipment-label-en">Engelse Naam (Optioneel)</Label>
            <Input
              id="equipment-label-en"
              value={newEquipmentLabelEN}
              onChange={(e) => setNewEquipmentLabelEN(e.target.value)}
              placeholder="e.g., Main Motor"
            />
          </div> */}
          <DialogFooter>
            <Button variant="outline" onClick={() => setOpenEquipmentDialog(false)}>Annuleren</Button>
            <Button onClick={handleAddEquipment} disabled={isAddingEquipment || isFlyLocked}>
              {isAddingEquipment ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
              Toevoegen
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Area Dialog */}
      <Dialog open={openEditAreaDialog} onOpenChange={setOpenEditAreaDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Bewerk Gebied</DialogTitle>
            <DialogDescription>
              Bewerk de code en beschrijving voor gebied "{selectedArea}" op lijn {selectedLine.toUpperCase()}.
            </DialogDescription>
          </DialogHeader>
          <div>
            <Label htmlFor="edit-area-code">Gebied Code (Uniek)</Label>
            <Input
              id="edit-area-code"
              value={newAreaCode}
              onChange={(e) => setNewAreaCode(e.target.value)}
            />
          </div>
          <div>
            <Label htmlFor="edit-area-label">Gebied Beschrijving</Label>
            <Input
              id="edit-area-label"
              value={newAreaLabel}
              onChange={(e) => setNewAreaLabel(e.target.value)}
            />
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setOpenEditAreaDialog(false)}>Annuleren</Button>
            <Button onClick={handleEditArea} disabled={isFlyLocked}>Opslaan</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Equipment Dialog */}
      <Dialog open={openEditEquipmentDialog} onOpenChange={setOpenEditEquipmentDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Bewerk Onderdeel</DialogTitle>
            <DialogDescription>
              Bewerk de details voor onderdeel "{selectedEquipment?.value}" in gebied "{selectedArea}" op lijn {selectedLine.toUpperCase()}.
            </DialogDescription>
          </DialogHeader>
          <div>
            <Label htmlFor="edit-equipment-code">Onderdeel Code (Uniek)</Label>
            <Input
              id="edit-equipment-code"
              value={newEquipmentCode}
              onChange={(e) => setNewEquipmentCode(e.target.value)}
            />
          </div>
          <div>
            <Label htmlFor="edit-equipment-label-nl">Nederlandse Naam</Label>
            <Input
              id="edit-equipment-label-nl"
              value={newEquipmentLabelNL}
              onChange={(e) => setNewEquipmentLabelNL(e.target.value)}
            />
          </div>
          <div>
            <Label htmlFor="edit-equipment-label-en">Engelse Naam (Optioneel)</Label>
            <Input
              id="edit-equipment-label-en"
              value={newEquipmentLabelEN}
              onChange={(e) => setNewEquipmentLabelEN(e.target.value)}
            />
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setOpenEditEquipmentDialog(false)}>Annuleren</Button>
            <Button onClick={handleEditEquipment} disabled={isFlyLocked}>Opslaan</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default EquipmentCodes;
