// src/components/layout/LocalLayout.tsx
import React, { useState } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { useLocalAuth } from '@/context/LocalAuthContext';
import { Button } from '@/components/ui/button';
import {
  LineChart,
  AlertCircle,
  ClipboardList,
  FileText,
  History,
  Menu,
  X,
  Settings,
  SlidersHorizontal,
  ShieldCheck,
  LogOut,
  LogIn,
  User,
  Wrench
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useIsMobile } from '@/hooks/use-mobile';
import { NavigationMenu, NavigationMenuItem, NavigationMenuList } from '@/components/ui/navigation-menu';
import SyncIndicator from '@/components/ui/SyncIndicator';
import LocalUserMenu from '@/components/auth/LocalUserMenu';
import DevModeWarning from '@/components/ui/DevModeWarning';

interface LayoutProps {
  children: React.ReactNode;
}

const LocalLayout: React.FC<LayoutProps> = ({ children }) => {
  const location = useLocation();
  const isMobile = useIsMobile();
  const navigate = useNavigate();
  const { user, signOut } = useLocalAuth();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [mobileSubMenuOpen, setMobileSubMenuOpen] = useState(false);

  const toggleMobileMenu = () => {
    setMobileMenuOpen(prev => !prev);
    if (mobileMenuOpen) {
       setMobileSubMenuOpen(false);
    }
  }

  const closeMobileMenu = () => {
    setMobileMenuOpen(false);
    setMobileSubMenuOpen(false);
  }

  const toggleMobileSubMenu = () => {
    setMobileSubMenuOpen(prev => !prev);
  };

  const handleLogout = async () => {
    try {
      await signOut();
      navigate('/auth');
      closeMobileMenu();
    } catch (error) {
      console.error('Error during logout:', error);
    }
  };

  const isSettingsPage = location.pathname.startsWith('/settings') || location.pathname === '/history';
  const isManagementPage = location.pathname.startsWith('/management');
  const isTdLogbookPage = location.pathname.startsWith('/td-logboek');
  const isActive = (path: string) => location.pathname === path;
  const isManager = user?.isManager || false;

  return (
    <div className="min-h-screen">
      <div className="container mx-auto py-4 px-4 md:px-6">
        <div className="bg-white rounded-2xl shadow-sm overflow-hidden">
          <header className="border-b border-gray-200">
            <div className="flex h-16 items-center justify-between px-4">
              {/* Logo */}
              <div className="flex items-center space-x-4">
                <img
                  src="/logo.png"
                  alt="Cirrec Logo"
                  className="h-10 w-auto drop-shadow-sm"
                />
              </div>

              {/* Desktop Navigation */}
              {!isMobile && (
                <div className="flex-grow flex justify-center">
                  <NavigationMenu>
                    <NavigationMenuList>
                      {/* Standard Links */}
                      <NavigationMenuItem>
                        <Link to="/safety-quality" className={cn("px-4 py-2 text-sm font-medium rounded-md transition-colors hover:bg-accent hover:text-accent-foreground", isActive('/safety-quality') && "bg-accent text-accent-foreground")}>Veiligheid & Kwaliteit</Link>
                      </NavigationMenuItem>
                      <NavigationMenuItem>
                        <Link to="/results" className={cn("px-4 py-2 text-sm font-medium rounded-md transition-colors hover:bg-accent hover:text-accent-foreground", isActive('/results') && "bg-accent text-accent-foreground")}>Dag Start</Link>
                      </NavigationMenuItem>
                      <NavigationMenuItem>
                        <Link to="/disruptions" className={cn("px-4 py-2 text-sm font-medium rounded-md transition-colors hover:bg-accent hover:text-accent-foreground", isActive('/disruptions') && "bg-accent text-accent-foreground")}>Storingen</Link>
                      </NavigationMenuItem>
                      <NavigationMenuItem>
                        <Link to="/overdracht" className={cn("px-4 py-2 text-sm font-medium rounded-md transition-colors hover:bg-accent hover:text-accent-foreground", isActive('/overdracht') && "bg-accent text-accent-foreground")}>Overdracht</Link>
                      </NavigationMenuItem>
                      <NavigationMenuItem>
                        <Link to="/td-logboek" className={cn("px-4 py-2 text-sm font-medium rounded-md transition-colors hover:bg-accent hover:text-accent-foreground", isTdLogbookPage && "bg-accent text-accent-foreground")}>
                          <Wrench className="h-4 w-4 mr-1 inline" /> TD Logboek
                        </Link>
                      </NavigationMenuItem>
                      <NavigationMenuItem>
                        <Link to="/settings" className={cn("px-4 py-2 text-sm font-medium rounded-md transition-colors hover:bg-accent hover:text-accent-foreground flex items-center gap-1", isSettingsPage && "bg-accent text-accent-foreground")}>
                          <Settings className="h-4 w-4" /> Instellingen
                        </Link>
                      </NavigationMenuItem>
                      {/* Management Link */}
                      {isManager && (
                        <NavigationMenuItem>
                          <Link to="/management" className={cn("px-4 py-2 text-sm font-medium rounded-md transition-colors hover:bg-accent hover:text-accent-foreground flex items-center gap-1", isManagementPage && "bg-accent text-accent-foreground")}>
                            <ShieldCheck className="h-4 w-4" /> Beheer
                          </Link>
                        </NavigationMenuItem>
                      )}
                    </NavigationMenuList>
                  </NavigationMenu>
                </div>
              )}

              {/* Auth Status / Actions */}
              <div className="flex items-center gap-4">
                {!isMobile && <SyncIndicator />}
                {!isMobile && <LocalUserMenu />}

                {/* Mobile Menu Toggle */}
                {isMobile && (
                  <Button variant="ghost" size="icon" onClick={toggleMobileMenu}>
                    {mobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
                  </Button>
                )}
              </div>
            </div>
          </header>

          {/* Mobile Menu */}
          {isMobile && mobileMenuOpen && (
            <div className="fixed inset-0 z-40 bg-black bg-opacity-50" onClick={closeMobileMenu}>
              <div className="fixed top-16 left-0 right-0 bg-white shadow-lg" onClick={(e) => e.stopPropagation()}>
                <nav className="flex flex-col p-4">
                  {/* Standard Mobile Links */}
                  <Link to="/safety-quality" className={cn("modern-nav-link mb-2", isActive('/safety-quality') ? "modern-nav-link-active" : "")} onClick={closeMobileMenu}>
                    <AlertCircle className="w-4 h-4 mr-2" /> Veiligheid & Kwaliteit
                  </Link>
                  <Link to="/" className={cn("modern-nav-link mb-2", isActive('/') || isActive('/results') ? "modern-nav-link-active" : "")} onClick={closeMobileMenu}>
                    <LineChart className="w-4 h-4 mr-2" /> Dashboard
                  </Link>
                  <Link to="/disruptions" className={cn("modern-nav-link mb-2", isActive('/disruptions') ? "modern-nav-link-active" : "")} onClick={closeMobileMenu}>
                    <ClipboardList className="w-4 h-4 mr-2" /> Lopende Storingen
                  </Link>
                  <Link to="/overdracht" className={cn("modern-nav-link mb-2", isActive('/overdracht') ? "modern-nav-link-active" : "")} onClick={closeMobileMenu}>
                    <FileText className="w-4 h-4 mr-2" /> Overdracht
                  </Link>
                  <Link to="/td-logboek" className={cn("modern-nav-link mb-2", isTdLogbookPage ? "modern-nav-link-active" : "")} onClick={closeMobileMenu}>
                    <Wrench className="w-4 h-4 mr-2" /> TD Logboek
                  </Link>

                  {/* Settings Link & Sub-menu */}
                  <button type="button" className={cn("modern-nav-link flex justify-between items-center w-full", (isSettingsPage || mobileSubMenuOpen) ? "modern-nav-link-active" : "")} onClick={toggleMobileSubMenu}>
                    <span className="flex items-center">
                      <Settings className="w-4 h-4 mr-2" /> Instellingen
                    </span>
                  </button>
                  {mobileSubMenuOpen && (
                    <div className="pl-4 mt-2 border-l-2 border-gray-100 ml-2">
                      <Link to="/settings/toggles" className={cn("modern-nav-link text-sm mb-2", isActive('/settings/toggles') ? "text-primary font-medium" : "text-gray-600")} onClick={closeMobileMenu}>
                        <SlidersHorizontal className="h-4 w-4 mr-2" /> Toggles
                      </Link>
                      <Link to="/history" className={cn("modern-nav-link text-sm", isActive('/history') ? "text-primary font-medium" : "text-gray-600")} onClick={closeMobileMenu}>
                        <History className="w-4 w-4 mr-2" /> Geschiedenis
                      </Link>
                    </div>
                  )}

                  {/* Management Link (Mobile) */}
                  {isManager && (
                    <Link to="/management" className={cn("modern-nav-link mb-2", isManagementPage ? "modern-nav-link-active" : "")} onClick={closeMobileMenu}>
                      <ShieldCheck className="w-4 h-4 mr-2" /> Beheer
                    </Link>
                  )}

                  {/* Auth Links (Mobile) */}
                  <div className="border-t mt-4 pt-4">
                    {user ? (
                      <>
                        <Link
                          to="/account"
                          className="flex items-center gap-1 text-sm px-2 mb-2 text-primary hover:underline"
                          onClick={closeMobileMenu}
                        >
                          <User className="h-4 w-4" />
                          <span>{user.email}</span>
                          {user.isManager && <span className="ml-1 text-xs bg-amber-100 text-amber-600 px-1 rounded">Beheerder</span>}
                          {user.team && <span className="ml-1 text-xs bg-blue-100 text-blue-600 px-1 rounded">{user.team}</span>}
                        </Link>
                        <button type="button" onClick={handleLogout} className="modern-nav-link w-full text-left text-red-600 hover:bg-red-50">
                          <LogOut className="w-4 h-4 mr-2" /> Uitloggen
                        </button>
                      </>
                    ) : (
                      <Link to="/auth" className="modern-nav-link" onClick={closeMobileMenu}>
                        <LogIn className="w-4 h-4 mr-2" /> Inloggen
                      </Link>
                    )}
                  </div>
                </nav>
              </div>
            </div>
          )}

          <main className="flex-grow p-6">
            <DevModeWarning />
            <div className="animate-fade-in h-full">
              {children}
            </div>
            {isMobile && (
              <div className="mt-6 flex justify-center">
                <SyncIndicator />
              </div>
            )}
          </main>
        </div>
      </div>
    </div>
  );
};

export default LocalLayout;
