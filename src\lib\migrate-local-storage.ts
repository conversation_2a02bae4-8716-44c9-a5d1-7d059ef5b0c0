/**
 * <PERSON><PERSON><PERSON> script om bestaande localStorage data naar Supabase te verplaatsen.
 * Dit script moet eenmalig worden uitgevoerd om alle lokale data naar Supabase te migreren.
 */
import { supabase } from './supabase-client';

// Tabel waarin we de localStorage data opslaan
const STORAGE_TABLE = 'local_storage_data';

/**
 * Controleert of de benodigde tabel bestaat in Supabase en maakt deze aan indien nodig.
 */
async function ensureTableExists() {
  // Controleer of de tabel al bestaat
  const { error } = await supabase
    .from(STORAGE_TABLE)
    .select('count')
    .limit(1)
    .single();

  if (error && error.code === 'PGRST116') {
    console.log(`Tabel '${STORAGE_TABLE}' bestaat nog niet, wordt aangemaakt...`);
    
    // Maak de tabel aan via SQL
    const { error: createError } = await supabase.rpc('create_local_storage_table');
    
    if (createError) {
      console.error('Fout bij aanmaken tabel:', createError);
      throw createError;
    }
    
    console.log(`Tabel '${STORAGE_TABLE}' succesvol aangemaakt.`);
  } else if (error) {
    console.error('Fout bij controleren of tabel bestaat:', error);
    throw error;
  } else {
    console.log(`Tabel '${STORAGE_TABLE}' bestaat al.`);
  }
}

/**
 * Migreert alle localStorage items naar Supabase.
 */
export async function migrateLocalStorageToSupabase() {
  try {
    console.log('Start migratie van localStorage naar Supabase...');
    
    // Zorg ervoor dat de tabel bestaat
    await ensureTableExists();
    
    // Verzamel alle localStorage sleutels
    const keys = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key) {
        keys.push(key);
      }
    }
    
    console.log(`${keys.length} items gevonden in localStorage.`);
    
    // Migreer elk item naar Supabase
    let successCount = 0;
    let errorCount = 0;
    
    for (const key of keys) {
      let parsedValue: any = null;
      try {
        // Probeer de waarde te parsen
        const rawValue = localStorage.getItem(key);
        if (rawValue === null) {
          console.warn(`Sleutel '${key}' gevonden maar geen waarde in localStorage. Wordt overgeslagen.`);
          continue; // Ga naar de volgende sleutel
        }
        parsedValue = JSON.parse(rawValue);
        
      } catch (parseError) {
        // Als parsen mislukt, log een waarschuwing en sla dit item over
        errorCount++;
        console.warn(`Waarde voor sleutel '${key}' is geen geldige JSON ('${localStorage.getItem(key)}'). Wordt overgeslagen. Fout: ${parseError}`);
        continue; // Ga naar de volgende sleutel
      }
      
      // Als parsen succesvol was, ga verder met migreren
      try {
        // Controleer of het item al bestaat in Supabase
        const { data: existingItem } = await supabase
          .from(STORAGE_TABLE)
          .select('id') // Selecteer alleen id, we hebben de waarde niet nodig
          .eq('key', key)
          .single();
        
        if (existingItem) {
          // Update bestaand item
          const { error } = await supabase
            .from(STORAGE_TABLE)
            .update({ 
              value: parsedValue, // Gebruik de geparsede waarde
              updated_at: new Date().toISOString() 
            })
            .eq('key', key);
          
          if (error) throw error;
        } else {
          // Voeg nieuw item toe
          const { error } = await supabase
            .from(STORAGE_TABLE)
            .insert({ 
              key, 
              value: parsedValue, // Gebruik de geparsede waarde
              created_at: new Date().toISOString() 
            });
          
          if (error) throw error;
        }
        
        successCount++;
        // console.log(`Item '${key}' succesvol gemigreerd.`); // Optioneel: minder verbose logging
      } catch (migrationError) {
        errorCount++; // Verhoog errorCount ook bij migratiefouten
        console.error(`Fout bij migreren van item '${key}' naar Supabase:`, migrationError);
      }
    }
    
    console.log(`Migratie voltooid: ${successCount} items succesvol gemigreerd, ${errorCount} items overgeslagen of mislukt.`);
    return { success: true, successCount, errorCount };
  } catch (error) {
    console.error('Fout bij migratie van localStorage naar Supabase:', error);
    return { success: false, error };
  }
}

// Voer de migratie uit
migrateLocalStorageToSupabase()
  .then(result => {
    console.log('Migratie resultaat:', result);
  })
  .catch(error => {
    console.error('Fout tijdens migratie:', error);
  });
