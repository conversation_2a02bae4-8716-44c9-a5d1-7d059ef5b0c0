import React, { useState, useEffect, useMemo } from 'react';
import { useProduction } from '@/context/ProductionContext';
import { AlertTriangle, Plus, Calendar, Trash } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import LineSelector from '@/components/common/LineSelector';
import { ProductionLine, ProductionRow, LineData, ShiftData, BreakdownEntry, EquipmentEntry } from '@/types/index';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { toast } from 'sonner';
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command"
import { Check, ChevronsUpDown } from "lucide-react"

// Target values per production line
const TARGETS: Record<ProductionLine, number> = {
  'tl1': 36000,
  'tl2': 54000,
  'p1': 21000,
  'p2': 34800,
  'p3': 60000,
};

// Equipment area options
const areaOptions = [
  { value: "2-010", label: "2-010 (Voeding & Initiële Sortering)" },
  { value: "2-020", label: "2-020 (Sortering & Detectie)" },
  { value: "2-030", label: "2-030/2-040 (Wassen & Malen)" },
  { value: "2-060", label: "2-060/2-070 (Heet Wassen, Scheiding & Buffer Silo's)" },
  { value: "2-080", label: "2-080 (Filtratie & Bandfilters)" },
  { value: "2-090", label: "2-090 (Vloksortering)" },
  { value: "2-100", label: "2-100 (Opslag & Verpakking)" },
];

// Helper function to generate initial empty ShiftData
const createInitialShiftData = (): ShiftData => ({
  production: 0,
  material: '',
  isTransition: false,
  yield: 0,
});

// Helper function to generate initial empty BreakdownEntry
const createInitialBreakdownEntry = (): BreakdownEntry => ({
  duration: 0,
  equipment: '',
  pareto: '',
});

const ReportPage: React.FC = () => {
  const { 
    productionData, 
    updateProductionRow,
    addProductionRow, 
    resetData, 
    equipmentOptions, 
    materialOptions,
    removeProductionRow,
  } = useProduction();
  const [selectedLine, setSelectedLine] = useState<ProductionLine>('tl1');
  const [showResetDialog, setShowResetDialog] = useState(false);
  const [activeRowIndex, setActiveRowIndex] = useState(0);
  const [showDeleteDayDialog, setShowDeleteDayDialog] = useState(false);
  const [dayToDelete, setDayToDelete] = useState<{ line: ProductionLine; date: string } | null>(null);
  const [popoverOpen, setPopoverOpen] = useState<Record<number, boolean>>({});
  
  const lineData = productionData[selectedLine];
  
  const [formData, setFormData] = useState<ProductionRow>(() => ({
    date: new Date().toISOString().split('T')[0],
    line: selectedLine,
    target: TARGETS[selectedLine],
    od: createInitialShiftData(),
    md: createInitialShiftData(),
    nd: createInitialShiftData(),
    breakdowns: [
      createInitialBreakdownEntry(),
      createInitialBreakdownEntry(),
      createInitialBreakdownEntry(),
    ],
  }));

  useEffect(() => {
    const currentRow: ProductionRow | undefined = lineData.rows?.[activeRowIndex];

    if (currentRow) {
      const currentBreakdowns = [
          currentRow.breakdowns?.[0] ?? createInitialBreakdownEntry(),
          currentRow.breakdowns?.[1] ?? createInitialBreakdownEntry(),
          currentRow.breakdowns?.[2] ?? createInitialBreakdownEntry(),
      ] as [BreakdownEntry, BreakdownEntry, BreakdownEntry];

      const newState: ProductionRow = {
        date: currentRow.date,
        line: selectedLine,
        target: currentRow.target ?? TARGETS[selectedLine],
        od: currentRow.od ?? createInitialShiftData(),
        md: currentRow.md ?? createInitialShiftData(),
        nd: currentRow.nd ?? createInitialShiftData(),
        breakdowns: currentBreakdowns,
        isDateLocked: currentRow.isDateLocked ?? false,
      };
      setFormData(newState);
    } else {
       const defaultState: ProductionRow = {
          date: new Date().toISOString().split('T')[0],
          line: selectedLine,
          target: TARGETS[selectedLine],
          od: createInitialShiftData(),
          md: createInitialShiftData(),
          nd: createInitialShiftData(),
          breakdowns: [
            createInitialBreakdownEntry(),
            createInitialBreakdownEntry(),
            createInitialBreakdownEntry(),
          ],
          isDateLocked: false,
       };
       setFormData(defaultState);
    }
  }, [lineData, activeRowIndex, selectedLine]);

  useEffect(() => {
    setActiveRowIndex(0);
    setFormData(prev => ({
      date: prev.date,
      line: selectedLine,
      target: TARGETS[selectedLine],
      od: createInitialShiftData(),
      md: createInitialShiftData(),
      nd: createInitialShiftData(),
      breakdowns: [
        createInitialBreakdownEntry(),
        createInitialBreakdownEntry(),
        createInitialBreakdownEntry(),
      ],
    }));
  }, [selectedLine]);

  const handleShiftChange = (shift: 'od' | 'md' | 'nd', field: keyof ShiftData, value: string | number | boolean) => {
    setFormData(prev => {
      let processedValue = value;
      if (field === 'yield' && typeof value === 'string') {
        const sanitizedValue = value.replace(',', '.');
        processedValue = sanitizedValue === '' ? 0 : parseFloat(sanitizedValue) || 0;
      }
      if (field === 'production' && typeof value === 'string') {
         processedValue = value === '' ? 0 : parseFloat(value) || 0;
      }
       if (field === 'isTransition' && typeof value !== 'boolean') {
         processedValue = value === 'true' || value === 'on';
      }

      return {
        ...prev,
        [shift]: {
          ...prev[shift],
          [field]: processedValue
        }
      };
    });
  };

  const handleBreakdownChange = (index: number, field: keyof BreakdownEntry, value: string | number) => {
     setFormData(prev => {
        const updatedBreakdowns = [...prev.breakdowns] as [BreakdownEntry, BreakdownEntry, BreakdownEntry];
        let processedValue = value;

        updatedBreakdowns[index] = {
            ...updatedBreakdowns[index],
            [field]: processedValue
        };
        return { ...prev, breakdowns: updatedBreakdowns };
     });
  };

  const handleSave = () => {
    if (updateProductionRow) {
       const finalBreakdowns: [BreakdownEntry, BreakdownEntry, BreakdownEntry] = [
            formData.breakdowns[0] || createInitialBreakdownEntry(),
            formData.breakdowns[1] || createInitialBreakdownEntry(),
            formData.breakdowns[2] || createInitialBreakdownEntry(),
        ];
        updateProductionRow(selectedLine, formData.date, { ...formData, breakdowns: finalBreakdowns });
    } else {
        console.error("updateProductionRow function not found in context");
    }
  };

  useEffect(() => {
    const timer = setTimeout(() => {
      handleSave();
    }, 1000);
    
    return () => clearTimeout(timer);
  }, [formData]);

  const handleAddDay = () => {
    addProductionRow(selectedLine);
    setActiveRowIndex(lineData.rows.length); 
  };

  const handleRowChange = (index: number) => {
    handleSave();
    setActiveRowIndex(index);
  };

  const handleDeleteDayClick = (line: ProductionLine, date: string, index: number, event: React.MouseEvent) => {
    event.stopPropagation();

    if (index === 0) {
      toast.error("De eerste dag kan niet worden verwijderd.");
      return;
    }

    if(lineData.rows.length <= 1) {
       toast.error("Kan de laatste dag niet verwijderen.");
       return;
    }
    setDayToDelete({ line, date });
    setShowDeleteDayDialog(true);
  };

  const handleDeleteDayConfirm = () => {
    if (dayToDelete) {
      removeProductionRow(dayToDelete.line, dayToDelete.date);
      if (lineData.rows.findIndex(r => r.date === dayToDelete.date) === activeRowIndex) {
        setActiveRowIndex(0);
      }
      setDayToDelete(null);
      setShowDeleteDayDialog(false);
    }
  };

  const handleResetConfirm = () => {
    resetData();
    setShowResetDialog(false);
    setActiveRowIndex(0);
  };

  const cn = (...classes: (string | undefined)[]) => {
    return classes.filter(Boolean).join(' ');
  };

  const combinedEquipmentOptions = useMemo(() => {
    const options: { value: string; label: string; group: string }[] = [];
    const lineEquipment = equipmentOptions?.[selectedLine];
    if (!lineEquipment) return options;

    Object.entries(lineEquipment).forEach(([areaCode, subOptions]) => {
       const areaLabel = areaOptions.find(opt => opt.value === areaCode)?.label || areaCode;
       options.push({ value: `area-${areaCode}`, label: `${areaLabel} (Hoofdgebied)`, group: areaLabel });
       subOptions.forEach(subOpt => {
          options.push({ value: subOpt.value, label: `${areaLabel} - ${subOpt.label_nl}`, group: areaLabel });
       });
    });
    options.sort((a, b) => a.group.localeCompare(b.group) || a.label.localeCompare(b.label));
    return options;
  }, [equipmentOptions, selectedLine]);

  const currentMaterialOptions = useMemo(() => {
      return materialOptions?.[selectedLine] || [];
  }, [materialOptions, selectedLine]);

  return (
    <div className="animate-fade-in">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <h1 className="text-2xl md:text-3xl font-semibold text-faerch-blue mb-4 md:mb-0">
          Productie Rapport
        </h1>
        <div className="flex flex-col sm:flex-row gap-3">
          <Button 
            variant="gradient" 
            className="btn-flashy shadow-md hover:shadow-lg transition-all animate-gradient"
            onClick={() => setShowResetDialog(true)}
          >
            Reset
          </Button>
        </div>
      </div>

      <div className="mb-6">
        <LineSelector selectedLine={selectedLine} onChange={setSelectedLine} />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
        
        <div className="dashboard-card p-4 space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-lg font-semibold">Dag Selectie</h2>
            <Button onClick={handleAddDay} variant="outline" size="sm">
              Nieuwe Dag
            </Button>
          </div>
          <div className="space-y-2 max-h-96 overflow-y-auto pr-2">
            {lineData.rows && lineData.rows.map((row, index) => (
              <Button
                key={row.date + '-' + index}
                variant={index === activeRowIndex ? "default" : "outline"}
                className="w-full justify-start text-left h-auto py-2 flex items-center justify-between group"
                onClick={() => handleRowChange(index)}
              >
                <span>
                   {new Date(row.date).toLocaleDateString('nl-NL', {
                      year: 'numeric',
                      month: 'short',
                      day: 'numeric'
                   })}
                </span>
                {index > 0 && (
                    <Button 
                       variant="ghost" 
                       size="icon" 
                       className="h-7 w-7 opacity-0 group-hover:opacity-100 transition-opacity text-red-500 hover:bg-red-100"
                       onClick={(e) => handleDeleteDayClick(selectedLine, row.date, index, e)}
                    >
                       <Trash className="h-4 w-4"/>
                    </Button>
                )}
              </Button>
            ))}
          </div>
        </div>

        <div className="lg:col-span-2 space-y-6">
          {(['od', 'md', 'nd'] as const).map((shift) => {
            const shiftLabels = { od: 'Ochtenddienst', md: 'Middagdienst', nd: 'Nachtdienst' };
            return (
              <div key={shift} className="dashboard-card p-4">
                <h2 className="text-lg font-semibold mb-4 border-b pb-2">{shiftLabels[shift]}</h2>
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
                   <div>
                      <Label htmlFor={`${shift}-production`}>Productie (kg)</Label>
                      <Input
                        id={`${shift}-production`}
                        type="number"
                        inputMode="decimal" 
                        value={formData[shift].production}
                        onChange={(e) => handleShiftChange(shift, 'production', e.target.value)}
                        className="mt-1"
                        step="any"
                      />
                   </div>
                   <div>
                      <Label htmlFor={`${shift}-yield`}>Yield (%)</Label>
                      <Input
                        id={`${shift}-yield`}
                        type="text"
                        inputMode="decimal"
                        value={formData[shift].yield}
                        onChange={(e) => handleShiftChange(shift, 'yield', e.target.value)}
                        className="mt-1"
                        step="0.1"
                      />
                   </div>
                    <div>
                      <Label htmlFor={`${shift}-material`}>Materiaal</Label>
                      <Select
                        value={formData[shift].material}
                        onValueChange={(value) => handleShiftChange(shift, 'material', value)}
                      >
                        <SelectTrigger id={`${shift}-material`} className="mt-1">
                          <SelectValue placeholder="Selecteer materiaal..." />
                        </SelectTrigger>
                        <SelectContent>
                          {currentMaterialOptions.map((mat) => (
                            <SelectItem key={mat} value={mat}>
                              {mat}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                   <div className="flex items-end pb-1">
                      <div className="flex items-center space-x-2">
                         <Checkbox
                           id={`${shift}-isTransition`}
                           checked={formData[shift].isTransition}
                           onCheckedChange={(checked) => handleShiftChange(shift, 'isTransition', !!checked)}
                         />
                         <Label htmlFor={`${shift}-isTransition`} className="cursor-pointer">
                           Overgang
                         </Label>
                       </div>
                   </div>
                </div>
              </div>
            );
          })}
        </div>

      </div>

      <div className="dashboard-card p-4 mt-6">
         <h2 className="text-lg font-semibold mb-4 border-b pb-2">Storingen / Downtime</h2>
         <div className="space-y-4">
            {formData.breakdowns.map((breakdown, index) => (
                <div key={index} className="grid grid-cols-1 md:grid-cols-4 gap-4 items-start border rounded p-3 bg-gray-50/50">
                   <div>
                      <Label htmlFor={`breakdown-${index}-duration`}>Downtime Duur (HH:MM)</Label>
                       <Input
                         id={`breakdown-${index}-duration`}
                         type="time"
                         value={breakdown.duration}
                         onChange={(e) => handleBreakdownChange(index, 'duration', e.target.value)}
                         className="mt-1"
                         step="60"
                       />
                   </div>
                   <div className="md:col-span-2">
                      <Label htmlFor={`breakdown-${index}-pareto`}>Pareto / Oorzaak</Label>
                       <Textarea
                         id={`breakdown-${index}-pareto`}
                         value={breakdown.pareto}
                         onChange={(e) => handleBreakdownChange(index, 'pareto', e.target.value)}
                         className="mt-1"
                         rows={2}
                       />
                   </div>
                   <div>
                      <Label htmlFor={`breakdown-${index}-equipment`}>Apparatuur</Label>
                           <div>
      <Label htmlFor={`breakdown-${index}-equipment`}>Apparatuur</Label>
      <Popover open={popoverOpen[index]} onOpenChange={(isOpen) => setPopoverOpen(prev => ({ ...prev, [index]: isOpen }))}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={popoverOpen[index]}
            className="w-full justify-between mt-1"
            id={`breakdown-${index}-equipment`}
          >
            {breakdown.equipment
              ? combinedEquipmentOptions.find((opt) => opt.value === breakdown.equipment)?.label
              : "Selecteer apparatuur..."}
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[--radix-popover-trigger-width] p-0">
          <Command>
            <CommandInput placeholder="Zoek apparatuur..." />
            <CommandEmpty>Geen apparatuur gevonden.</CommandEmpty>
            {/* Wrap the groups in a scrollable container */}
            <div className="max-h-60 overflow-y-auto"> 
              {areaOptions.map((area) => (
                <CommandGroup key={area.value} heading={area.label}>
                  {combinedEquipmentOptions
                    .filter((opt) => opt.group === area.label) // Filter options per area
                    .map((option) => (
                      <CommandItem
                        key={option.value}
                        value={option.label} // Use label for searching
                        onSelect={() => {
                          handleBreakdownChange(index, 'equipment', option.value);
                          setPopoverOpen(prev => ({ ...prev, [index]: false }));
                        }}
                      >
                        <Check
                          className={cn(
                            "mr-2 h-4 w-4",
                            breakdown.equipment === option.value ? "opacity-100" : "opacity-0"
                          )}
                        />
                        {/* Display only the sub-label part */}
                        {option.label.includes(" (Hoofdgebied)") ? option.label : option.label.replace(area.label + " - ", "")}
                      </CommandItem>
                    ))}
                </CommandGroup>
              ))}
            </div>
          </Command>
        </PopoverContent>
           </Popover>
            </div>   {/* Closing div voor Apparatuur veld */}
          </div>     {/* Closing div voor breakdown entry grid */}
        ))}         {/* Closing breakdowns map */}
      </div>       {/* Closing space-y-4 div */}
    </div>        {/* Closing dashboard-card div */}

    <Dialog open={showResetDialog} onOpenChange={setShowResetDialog}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Reset Rapport</DialogTitle>
          <DialogDescription>
            Weet je zeker dat je het hele rapport wilt resetten? Deze actie kan niet ongedaan worden gemaakt.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button variant="outline" onClick={() => setShowResetDialog(false)}>Annuleren</Button>
          <Button variant="destructive" onClick={handleResetConfirm}>Resetten</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>

    <Dialog open={showDeleteDayDialog} onOpenChange={setShowDeleteDayDialog}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Dag Verwijderen?</DialogTitle>
          <DialogDescription>
            Weet je zeker dat je de gegevens voor de dag 
            {dayToDelete && 
               ` ${new Date(dayToDelete.date).toLocaleDateString('nl-NL', { year: 'numeric', month: 'short', day: 'numeric'})} `
            }
            wilt verwijderen? Deze actie kan niet ongedaan worden gemaakt.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button variant="outline" onClick={() => setShowDeleteDayDialog(false)}>Annuleren</Button>
          <Button variant="destructive" onClick={handleDeleteDayConfirm}>Verwijderen</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>

  </div>
);

export default ReportPage;
