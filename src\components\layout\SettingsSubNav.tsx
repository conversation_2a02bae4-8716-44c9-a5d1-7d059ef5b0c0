import React from 'react';
import { useIsMobile } from '@/hooks/use-mobile'; // Import useIsMobile
import { useLocation, useNavigate } from 'react-router-dom';
import { TabsList, TabsTrigger } from '@/components/ui/tabs';
import { History, SlidersHorizontal } from 'lucide-react'; // Removed Target, Package, Construction, Info, Clock, Settings
import { cn } from '@/lib/utils';
import { useProduction } from '@/context/ProductionContext'; // Import context hook
import { Switch } from "@/components/ui/switch"; // Import Switch
import { Label } from "@/components/ui/label"; // Import Label

export const SettingsSubNav = ({ className }: { className?: string }) => {
  const location = useLocation();
  const navigate = useNavigate();
  // Get Fly Lock state and setter from context
  const { isFlyLocked, setIsFlyLocked } = useProduction();
  const isMobile = useIsMobile(); // Get mobile status

  // Bepaal actieve tab op basis van huidige pad
  // Bepaal actieve tab op basis van huidige pad
  let activeTab = 'toggles'; // Default to toggles now
  if (location.pathname.startsWith('/settings/toggles')) {
    activeTab = 'toggles';
  } else if (location.pathname.startsWith('/history')) { // Check history route
    activeTab = 'history'; // Use 'history' value for consistency with HistoryV2 page
  } else if (location.pathname.startsWith('/settings')) { // Check algemeen /settings
     activeTab = 'toggles'; // Toggles is now the default for /settings
  }

  // Functie om naar de juiste settings-subpagina of history te navigeren
  const handleNavigate = (value: string) => {
    if (value === 'history') {
      navigate('/history');
    } else {
      navigate(`/settings/${value}`);
    }
  };

  return (
    <div className="flex items-center justify-between border-b pb-2 mb-4">
      <nav className="flex items-center space-x-4 lg:space-x-6">
        {/* Only show TabsList on non-mobile */}
        {!isMobile && (
          <TabsList className={cn("grid w-full grid-cols-2 mb-6", className)}> {/* Reduced to 2 columns */}
          {/* Removed Targets, Materials, Equipment Tabs */}
          <TabsTrigger 
            value="history" 
            className="flex items-center gap-2 data-[state=active]:bg-white data-[state=active]:shadow-sm"
            onClick={() => handleNavigate('history')}
            data-state={activeTab === 'history' ? 'active' : 'inactive'}
          >
            <History className="h-4 w-4" />
            Geschiedenis
          </TabsTrigger>
          <TabsTrigger 
            value="toggles" 
            className="flex items-center gap-2 data-[state=active]:bg-white data-[state=active]:shadow-sm"
            onClick={() => handleNavigate('toggles')}
            data-state={activeTab === 'toggles' ? 'active' : 'inactive'}
          >
            <SlidersHorizontal className="h-4 w-4" />
            Toggles
          </TabsTrigger>
          </TabsList>
        )}
      </nav>
      {/* Fly Lock Switch verwijderen omdat deze nu in de Toggles pagina staat */}
      <div className="flex items-center space-x-2">
      </div>
    </div>
  );
}; 