// src/pages/management/ManagementLayout.tsx
import React, { useState, useEffect } from 'react';
import { Outlet, Link, useLocation, Navigate } from 'react-router-dom';
import { supabase } from '@/lib/supabase-client';
import { User } from '@supabase/supabase-js';
import { cn } from '@/lib/utils';
import { Target, Package, Construction, UserPlus, ShieldAlert } from 'lucide-react'; // Added Construction icon

const ManagementLayout: React.FC = () => {
  const location = useLocation();
  const [isAuthorizedManager, setIsAuthorizedManager] = useState<boolean | null>(null); // null indicates loading state
  const [currentUser, setCurrentUser] = useState<User | null>(null);

  useEffect(() => {
    const checkAuth = async (user: User | null) => {
      if (user) {
        setCurrentUser(user);
        try {
          const { data, error } = await supabase
            .from('authorized_managers')
            .select('user_id')
            .eq('user_id', user.id)
            .maybeSingle();

          if (error) {
            console.error('Error checking authorization in ManagementLayout:', error);
            setIsAuthorizedManager(false);
          } else {
            setIsAuthorizedManager(!!data);
          }
        } catch (err) {
          console.error('Exception during authorization check in ManagementLayout:', err);
          setIsAuthorizedManager(false);
        }
      } else {
        setCurrentUser(null);
        setIsAuthorizedManager(false);
      }
    };

    supabase.auth.getSession().then(({ data: { session } }) => {
      checkAuth(session?.user ?? null);
    });

    const { data: authListener } = supabase.auth.onAuthStateChange((_event, session) => {
      checkAuth(session?.user ?? null);
    });

    return () => {
      authListener?.subscription.unsubscribe();
    };
  }, []);

  const isActive = (pathSuffix: string) => location.pathname === `/management${pathSuffix}`;

  // Show loading state while checking authorization
  if (isAuthorizedManager === null) {
    return <div className="p-4 text-center">Autorisatie controleren...</div>;
  }

  // If not authorized, redirect to home or show an error message
  if (!isAuthorizedManager) {
    // Option 1: Redirect (more user-friendly)
    // return <Navigate to="/" replace />;

    // Option 2: Show error message
     return (
       <div className="p-6 bg-red-100 border border-red-400 text-red-700 rounded-md flex items-center gap-3">
         <ShieldAlert className="h-6 w-6" />
         <div>
           <h2 className="font-semibold">Geen Toegang</h2>
           <p>U bent niet geautoriseerd om deze sectie te bekijken.</p>
         </div>
       </div>
     );
  }

  // If authorized, show the management layout and sub-navigation
  return (
    <div className="flex flex-col md:flex-row gap-6">
      <aside className="w-full md:w-64 flex-shrink-0">
        <h2 className="text-xl font-semibold mb-4 border-b pb-2">Beheer Menu</h2>
        <nav className="flex flex-col space-y-1">
          <Link
            to="/management/targets"
            className={cn(
              "flex items-center gap-3 px-3 py-2 rounded-md text-sm font-medium transition-colors",
              isActive('/targets')
                ? "bg-primary/10 text-primary"
                : "text-muted-foreground hover:bg-accent hover:text-accent-foreground"
            )}
          >
            <Target className="h-4 w-4" />
            Target & Yields
          </Link>
          <Link
            to="/management/materials"
            className={cn(
              "flex items-center gap-3 px-3 py-2 rounded-md text-sm font-medium transition-colors",
              isActive('/materials')
                ? "bg-primary/10 text-primary"
                : "text-muted-foreground hover:bg-accent hover:text-accent-foreground"
            )}
          >
            <Package className="h-4 w-4" />
            Materialen
          </Link>
          <Link
            to="/management/equipment"
            className={cn(
              "flex items-center gap-3 px-3 py-2 rounded-md text-sm font-medium transition-colors",
              isActive('/equipment')
                ? "bg-primary/10 text-primary"
                : "text-muted-foreground hover:bg-accent hover:text-accent-foreground"
            )}
          >
            <Construction className="h-4 w-4" /> {/* Changed Icon */}
            Apparaat beheer
          </Link>
          <Link
            to="/management/register"
            className={cn(
              "flex items-center gap-3 px-3 py-2 rounded-md text-sm font-medium transition-colors",
              isActive('/register')
                ? "bg-primary/10 text-primary"
                : "text-muted-foreground hover:bg-accent hover:text-accent-foreground"
            )}
          >
            <UserPlus className="h-4 w-4" />
            Nieuwe Gebruiker Registreren
          </Link>
        </nav>
      </aside>
      <main className="flex-grow">
        <Outlet /> {/* Nested routes will render here */}
      </main>
    </div>
  );
};

export default ManagementLayout;