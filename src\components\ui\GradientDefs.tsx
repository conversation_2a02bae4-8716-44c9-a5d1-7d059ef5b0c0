import React from 'react';

/**
 * Component that adds SVG gradient definitions to the document
 * These gradients can be referenced by other components
 */
const GradientDefs: React.FC = () => {
  return (
    <svg width="0" height="0" style={{ position: 'absolute', overflow: 'hidden' }}>
      <defs>
        {/* Button text gradient */}
        <linearGradient id="btn-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" stopColor="#1f2937" />
          <stop offset="50%" stopColor="#4b5563" />
          <stop offset="100%" stopColor="#9ca3af" />
        </linearGradient>

        {/* Navigation gradient */}
        <linearGradient id="nav-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" stopColor="#2563eb" />
          <stop offset="50%" stopColor="#3b82f6" />
          <stop offset="100%" stopColor="#60a5fa" />
        </linearGradient>

        {/* Blue team gradient */}
        <linearGradient id="blue-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" stopColor="#1e40af" />
          <stop offset="50%" stopColor="#3b82f6" />
          <stop offset="100%" stopColor="#60a5fa" />
        </linearGradient>

        {/* Green team gradient */}
        <linearGradient id="green-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" stopColor="#166534" />
          <stop offset="50%" stopColor="#22c55e" />
          <stop offset="100%" stopColor="#4ade80" />
        </linearGradient>

        {/* Yellow team gradient */}
        <linearGradient id="yellow-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" stopColor="#854d0e" />
          <stop offset="50%" stopColor="#eab308" />
          <stop offset="100%" stopColor="#facc15" />
        </linearGradient>

        {/* Red team gradient */}
        <linearGradient id="red-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" stopColor="#991b1b" />
          <stop offset="50%" stopColor="#ef4444" />
          <stop offset="100%" stopColor="#f87171" />
        </linearGradient>

        {/* White team gradient */}
        <linearGradient id="white-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" stopColor="#525252" />
          <stop offset="50%" stopColor="#737373" />
          <stop offset="100%" stopColor="#a3a3a3" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export default GradientDefs;
