import React from 'react';
import { Package, Target, Percent } from 'lucide-react';
import { Progress } from "@/components/ui/progress";
import { cn } from '@/lib/utils'; // Import cn utility
interface DaySummaryProps {
  totalProduction: number;
  target: number;
  averageYield: number;
  yieldTarget: number;
}

const DaySummary: React.FC<DaySummaryProps> = ({
  totalProduction,
  target,
  averageYield,
  yieldTarget
}) => {
  // const percentage = target === 0 ? 0 : Math.max(0, Math.min(150, (totalProduction / target) * 100)); // Cap op 150% voor weergave
  // const percentageDisplay = target === 0 ? 0 : (totalProduction / target) * 100;

  // Kleur functie voor productie percentage (Not used anymore)
  // const getProductionPercentageColorClass = (perc: number): string => {
  //   if (perc >= 100) return 'text-green-600';
  //   if (perc >= 80) return 'text-yellow-600';
  //   return 'text-red-600';
  // };

  // Kleur functie voor yield percentage
  const getYieldColorClass = (yieldValue: number, yieldTgt: number): string => {
    if (yieldValue >= yieldTgt) return 'text-green-600';
    // Voeg eventueel meer gradaties toe hier
    return 'text-yellow-600'; // Geel als het lager is dan target
  };

  return (
    // Changed grid to 2 columns
    <div className="grid grid-cols-1 gap-2"> {/* Removed sm:grid-cols-2 to stack vertically */}
      {/* Removed Card 1: Totale Productie */}

      {/* Card 2: Changed to show Total Production */}
      <div className="dashboard-card p-3 flex flex-col justify-between items-center text-center bg-white rounded-lg shadow hover:shadow-md transition-shadow duration-300 h-full">
        <div className="flex items-center text-gray-500 mb-1">
           <Package className="h-4 w-4 mr-1" /> {/* Changed Icon */}
           <span className="text-xs font-medium">Totale Productie</span> {/* Changed Label */}
        </div>
        <div
           className={cn(
             "text-2xl font-bold animate-fade-in-fast",
             totalProduction >= target ? 'text-green-600' : 'text-red-600' // Conditional color
           )}
           key={`prod-${totalProduction}`}
        >
            {totalProduction.toLocaleString('nl-NL')} <span className="text-base font-normal">kg</span>
        </div>
         <span className="text-2xs text-gray-500 mt-0.5">Doel: {target.toLocaleString('nl-NL')} kg</span> {/* Show target below */}
      </div>

      {/* Card 3: Gemiddelde Yield */}
      <div className="dashboard-card p-3 flex flex-col justify-between items-center text-center bg-white rounded-lg shadow hover:shadow-md transition-shadow duration-300 h-full">
        <div className="flex items-center text-gray-500 mb-1">
           <Percent className="h-4 w-4 mr-1" />
           <span className="text-xs font-medium">Gem. Yield</span>
        </div>
        <div
          className={`text-xl font-bold ${getYieldColorClass(averageYield, yieldTarget)} animate-fade-in-fast`}
          key={`yield-${averageYield.toFixed(1)}%`}
        >
           {averageYield.toFixed(1)}%
        </div>
        <span className="text-2xs text-gray-500 mt-0.5">Doel: {yieldTarget}%</span>
      </div>

      <style>{`
        @keyframes fadeInFast {
          from { opacity: 0.5; transform: translateY(5px); }
          to { opacity: 1; transform: translateY(0); }
        }
        .animate-fade-in-fast {
          animation: fadeInFast 0.4s ease-out;
        }
      `}</style>
    </div>
  );
};

export default DaySummary;
