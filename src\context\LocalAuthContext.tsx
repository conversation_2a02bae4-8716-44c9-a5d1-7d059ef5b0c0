// src/context/LocalAuthContext.tsx
import React, { createContext, useState, useContext, useEffect, useCallback } from 'react';
import { useToast } from '@/hooks/use-toast';

// Define types
interface User {
  id: string;
  email: string;
  isManager: boolean;
  team: string | null;
}

interface AuthContextType {
  user: User | null;
  loading: boolean;
  signIn: (credentials: { email: string; password: string }) => Promise<void>;
  signOut: () => Promise<void>;
}

// Create context
const LocalAuthContext = createContext<AuthContextType | undefined>(undefined);

// Create provider
export const LocalAuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  // Check for existing session on load
  useEffect(() => {
    const storedUser = localStorage.getItem('localAuthUser');
    if (storedUser) {
      try {
        setUser(JSON.parse(storedUser));
      } catch (error) {
        console.error('Error parsing stored user:', error);
        localStorage.removeItem('localAuthUser');
      }
    }
    setLoading(false);
  }, []);

  // Sign in function - DISABLED FOR SECURITY
  const signIn = useCallback(async (credentials: { email: string; password: string }) => {
    setLoading(true);
    try {
      // Local authentication is disabled for security reasons
      // Use proper Supabase authentication instead
      throw new Error('Local authentication is disabled. Please use the main authentication system.');
    } catch (error: any) {
      toast.error('Inloggen mislukt', {
        description: error.message || 'Local authentication is not available.',
      });
      throw error;
    } catch (error) {
      console.error('Login error:', error);
      toast.error('Inloggen mislukt', {
        description: error instanceof Error ? error.message : 'Controleer uw e-mailadres en wachtwoord.',
      });
      throw error;
    } finally {
      setLoading(false);
    }
  }, [toast]);

  // Sign out function
  const signOut = useCallback(async () => {
    try {
      localStorage.removeItem('localAuthUser');
      setUser(null);
      toast.success('Uitgelogd', {
        description: 'U bent succesvol uitgelogd.',
      });
    } catch (error) {
      console.error('Logout error:', error);
      toast.error('Uitloggen mislukt', {
        description: 'Er is een fout opgetreden bij het uitloggen.',
      });
      throw error;
    }
  }, [toast]);

  // Context value
  const value = {
    user,
    loading,
    signIn,
    signOut,
  };

  return (
    <LocalAuthContext.Provider value={value}>
      {children}
    </LocalAuthContext.Provider>
  );
};

// Create hook
export const useLocalAuth = () => {
  const context = useContext(LocalAuthContext);
  if (context === undefined) {
    throw new Error('useLocalAuth must be used within a LocalAuthProvider');
  }
  return context;
};
