// Corrected src/pages/Settings.tsx
import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useIsMobile } from '@/hooks/use-mobile'; // Import useIsMobile
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Edit2, Save, X, Clock, History as HistoryIcon, DownloadIcon, Archive, // Added Archive
  ChevronDown, Filter, Calendar, AlertCircle, Package, Info, Plus, Trash2, Target, RotateCcw,
  Calendar as CalendarIcon,
  Download,
  TrendingUp,
  Activity,
  FileSpreadsheet,
  BarChart3,
  ListFilter,
  ArrowDownToLine,
  PieChart,
  AlertTriangle,
  PlusCircle,
  Pencil,
  BrainCircuit, // Added AI icon
  Settings as SettingsIcon // Added Settings icon
} from 'lucide-react';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useProduction } from '@/context/ProductionContext';
import toast from '@/hooks/use-toast';
import { format } from 'date-fns';
import { nl } from 'date-fns/locale';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { loadItem, saveItem } from '@/lib/local-storage';
import { ProductionLine, EquipmentEntry } from '@/types';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Link } from 'react-router-dom';
import { useNavigate } from 'react-router-dom';
import { SettingsSubNav } from '@/components/layout/SettingsSubNav';
import { Label } from "@/components/ui/label";
import { Textarea } from '@/components/ui/textarea'; // Import Textarea
import AiSettingsDialog from '@/components/settings/AiSettingsDialog'; // Import AI Settings Dialog

// Removed TargetState and TargetsState types as they are no longer used in this component

type LineFilter = ProductionLine | 'all';
type HistoryLineFilter = ProductionLine | 'all';

// Nieuwe types voor GeschiedenisV2
type ViewMode = 'table' | 'calendar' | 'chart' | 'timeline';
type ChartType = 'production' | 'yield' | 'disruptions';

// AI Settings Type
type AiSettings = {
  enabled: boolean;
  apiKey: string;
  prompt: string;
};

const Settings: React.FC = () => {
  const {
    // Removed target-related context values: updateTargets, updateYieldTargets, TARGETS, YIELD_TARGETS
    historyData,
    productionData,
    materialOptions,
    addMaterialOption,
    removeMaterialOption,
    equipmentOptions,
    addEquipmentArea,
    removeEquipmentArea,
    addEquipmentOption,
    removeEquipmentOption,
    isFlyLocked,
    setIsFlyLocked,
    archiveAndClearIncidents,
  } = useProduction();
  const navigate = useNavigate();
  const isMobile = useIsMobile(); // Get mobile status
  const [activeTab, setActiveTab] = useState('toggles'); // Default to 'toggles'
  // Removed activeLineTab state as it's no longer needed here
  // Removed editMode state as it's no longer needed here

  // Nieuwe state voor Geschiedenis2
  const [selectedLine, setSelectedLine] = useState<LineFilter>('all');
  const [showOnlyDisruptions, setShowOnlyDisruptions] = useState(false);
  const [dateFilter, setDateFilter] = useState<'7d' | '30d' | '90d' | 'all'>('30d');
  const [historySelectedLine, setHistorySelectedLine] = useState<HistoryLineFilter>('all');
  const [showOnlyBelowTarget, setShowOnlyBelowTarget] = useState(false);
  // Nieuwe state voor saveVK toggle
  const [saveVK, setSaveVK] = useState<boolean>(() => {
    return localStorage.getItem('saveVK') === 'true';
  });

  // State for AI Settings
  const [aiSettings, setAiSettings] = useState<AiSettings>(() => {
    return loadItem<AiSettings>('aiSettings', { enabled: false, apiKey: '', prompt: '' }) || { enabled: false, apiKey: '', prompt: '' };
  });
  const [aiSettingsDialogOpen, setAiSettingsDialogOpen] = useState(false); // State for AI settings dialog

  // Effect om saveVK in localStorage op te slaan wanneer het verandert
  useEffect(() => {
    localStorage.setItem('saveVK', saveVK.toString());
  }, [saveVK]);

  // Effect to save AI settings to localStorage
  useEffect(() => {
    saveItem('aiSettings', aiSettings);
  }, [aiSettings]);

  // Removed state and effects related to targets (targets, tempTargets)

  const productionLines: ProductionLine[] = ['tl1', 'tl2', 'p1', 'p2', 'p3']; // Keep if needed elsewhere, e.g., history filters

  // Removed target edit/save/cancel handlers (handleEdit, handleCancel, handleSave)

  const handleExportDataToJson = () => {
    // Corrected: Use historyData directly
    const jsonStr = JSON.stringify(historyData, null, 2);
    const blob = new Blob([jsonStr], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `production-history-${new Date().toISOString().slice(0, 10)}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
    toast.success("Geschiedenis geëxporteerd als JSON");
  };

  // Voeg nieuwe functie toe om geschiedenis te groeperen per lijn
  const historyByLine = useMemo(() => {
    const result: Record<ProductionLine, typeof historyData> = {
      tl1: [], tl2: [], p1: [], p2: [], p3: [], Yard: [], Koeling: [], Gebouw: [], Overige: []
    };

    historyData.forEach(entry => {
      if (entry.line) {
        result[entry.line].push(entry);
      }
    });

    Object.keys(result).forEach(line => {
      result[line as ProductionLine].sort((a, b) =>
        new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
      );
    });

    return result;
  }, [historyData]);

  // Nieuwe state voor Geschiedenis2
  const [filteredHistory, setFilteredHistory] = useState<typeof historyData>([]);

  // Nieuwe functie voor het filteren van geschiedenis op datum
  const getDateFilter = (filter: '7d' | '30d' | '90d' | 'all'): Date | null => {
    if (filter === 'all') return null;

    const now = new Date();
    const days = filter === '7d' ? 7 : filter === '30d' ? 30 : 90;
    return new Date(now.setDate(now.getDate() - days));
  };

  const filteredHistoryData = useMemo(() => {
    let filtered = [...historyData];

    // Filter op productielijn
    if (historySelectedLine !== 'all') {
      filtered = filtered.filter(entry => {
        // Check zowel production als breakdowns data in het nieuwe formaat
        const lineData = entry.data[historySelectedLine];
        return lineData && (lineData.rows.length > 0 || lineData.disruptions.length > 0);
      });
    }

    // Filter op datum
    const dateFilterStart = getDateFilter(dateFilter);
    if (dateFilterStart) {
      filtered = filtered.filter(entry => {
        const entryDate = new Date(entry.timestamp);
        return entryDate >= dateFilterStart;
      });
    }

    // Filter op storingen
    if (showOnlyDisruptions) {
      filtered = filtered.filter(entry => {
        if (historySelectedLine !== 'all') {
          const lineData = entry.data[historySelectedLine];
          return lineData?.disruptions && lineData.disruptions.length > 0;
        } else {
          // Check alle lijnen voor storingen
          return productionLines.some(line => {
            const lineData = entry.data[line];
            return lineData?.disruptions && lineData.disruptions.length > 0;
          });
        }
      });
    }

    // Sorteer op datum (nieuwste eerst)
    filtered.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

    return filtered;
  }, [historyData, historySelectedLine, dateFilter, showOnlyDisruptions, productionLines]);

  // Statistieken berekeningen
  const calculateStatistics = useMemo(() => {
    const initialStats = {
      totalProduction: 0,
      yieldCounts: [] as number[],
      totalDowntime: 0
    };

    return filteredHistoryData.reduce((stats, entry) => {
      const relevantLines = historySelectedLine === 'all' ? productionLines : [historySelectedLine];

      if (!entry || !entry.data) {
        console.warn("Skipping history entry with missing or undefined data:", entry);
        return stats;
      }

      relevantLines.forEach(line => {
        const lineData = entry.data[line];
        if (lineData) {
          lineData.rows?.forEach(row => {
            stats.totalProduction +=
              (Number(row.od.production) || 0) +
              (Number(row.md.production) || 0) +
              (Number(row.nd.production) || 0);

            if (row.od.yield) stats.yieldCounts.push(Number(row.od.yield));
            if (row.md.yield) stats.yieldCounts.push(Number(row.md.yield));
            if (row.nd.yield) stats.yieldCounts.push(Number(row.nd.yield));
          });

          lineData.disruptions?.forEach(disruption => {
            const [hours, minutes] = (disruption.duration || "0:00").split(":").map(Number);
            stats.totalDowntime += hours * 60 + minutes;
          });
        }
      });

      return stats;
    }, initialStats);
  }, [filteredHistoryData, historySelectedLine, productionLines]);

  // State voor de dialogs
  const [showMaterialDialog, setShowMaterialDialog] = useState(false);
  const [showSectionDialog, setShowSectionDialog] = useState(false);
  const [showEquipmentDialog, setShowEquipmentDialog] = useState(false);

  // State voor nieuwe items in dialogs
  const [newMaterial, setNewMaterial] = useState('');
  const [selectedLines, setSelectedLines] = useState<ProductionLine[]>([]);
  const [materialTypes, setMaterialTypes] = useState<string[]>([]);
  const [newSectionName, setNewSectionName] = useState('');
  const [newEquipmentName, setNewEquipmentName] = useState('');
  const [selectedSectionName, setSelectedSectionName] = useState<string>('');

  // Helper om sectienamen voor de ACTIEVE lijn te krijgen
  // Removed activeLineSections as it's no longer needed here

  // Functies voor materiaal beheer
  const handleAddMaterial = useCallback(() => {
    if (!newMaterial.trim()) {
      toast.error("Materiaal naam kan niet leeg zijn");
      return;
    }

    if (selectedLines.length === 0) {
      toast.error("Selecteer ten minste één productielijn");
      return;
    }

    // Eerste letter hoofdletter, rest kleine letters
    const formattedMaterial = newMaterial.charAt(0).toUpperCase() + newMaterial.slice(1).toLowerCase();

    // Speciale logica voor TL1 en TL2
    const hasTL1 = selectedLines.includes('tl1');
    const hasTL2 = selectedLines.includes('tl2');
    const hasS = materialTypes.includes('S');
    const hasT = materialTypes.includes('T');

    // Filter andere lijnen dan TL1 en TL2
    const otherLines = selectedLines.filter(line => line !== 'tl1' && line !== 'tl2');

    // Voeg materialen toe aan TL1 en TL2 volgens de regels
    if (hasTL1 && hasS) {
      addMaterialOption('tl1', `${formattedMaterial} S`);
    }
    if (hasTL2 && hasT) {
      addMaterialOption('tl2', `${formattedMaterial} T`);
    }

    // Voor andere lijnen, voeg materialen toe zoals normaal
    if (otherLines.length > 0) {
      otherLines.forEach(line => {
        if (materialTypes.length === 0) {
          addMaterialOption(line, formattedMaterial);
        } else {
          materialTypes.forEach(type => {
            addMaterialOption(line, `${formattedMaterial} ${type}`);
          });
        }
      });
    }

    // Reset form
    setNewMaterial('');
    setSelectedLines([]);
    setMaterialTypes([]);
    setShowMaterialDialog(false);

    toast.success(`Materiaal "${formattedMaterial}" is toegevoegd aan de geselecteerde lijnen`);
  }, [addMaterialOption, newMaterial, selectedLines, materialTypes]);

  const handleDeleteMaterial = useCallback((line: ProductionLine, material: string) => {
    removeMaterialOption(line, material);
  }, [removeMaterialOption]);

  // Functies voor sectie beheer
  // Removed handleAddSection, handleDeleteSection as they are no longer needed here

  // Functies voor apparaat beheer
  // Removed handleAddEquipment, handleDeleteEquipment as they are no longer needed here

  const handleLineChange = (value: string) => {
    if (value === 'all' || value === 'tl1' || value === 'tl2' || value === 'p1' || value === 'p2' || value === 'p3') {
      setSelectedLine(value as LineFilter);
    }
  };

  const handleHistoryLineChange = (value: string) => {
    if (value === 'all' || value === 'tl1' || value === 'tl2' || value === 'p1' || value === 'p2' || value === 'p3') {
      setHistorySelectedLine(value);
    }
  };

  // Removed handleActiveLineChange as it's no longer needed here

  // Handler for AI settings changes
  const handleAiSettingChange = (field: keyof AiSettings, value: string | boolean) => {
    const newSettings = { ...aiSettings, [field]: value };
    setAiSettings(newSettings);
    // If enabling AI and API key is missing, open the dialog
    if (field === 'enabled' && value === true && !newSettings.apiKey) {
      setAiSettingsDialogOpen(true);
    }
  };

  // Handler for saving AI settings from the dialog
  const handleSaveAiSettings = (newSettings: AiSettings) => {
    setAiSettings(newSettings);
    // Optionally add a toast message
    toast.success("AI instellingen opgeslagen.");
  };


  return (
    <div className="animate-fade-in">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold text-faerch-blue">Instellingen</h1>
      </div>

      {/* Use "toggles" as the value and defaultValue */}
      <Tabs defaultValue="toggles" value="toggles" className="space-y-4">
        <SettingsSubNav />
        {/* Removed TabsContent for "targets", "materials", "equipment" */}

        {/* --- Toggles Section --- */}
        <TabsContent value="toggles" className="space-y-6">
          {/* Fly Lock Card */}
          <Card>
            <CardHeader>
              <CardTitle>Fly Lock Modus</CardTitle>
              <CardDescription>Voorkom wijzigingen in productiegegevens tijdens actieve shifts.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <Label htmlFor="fly-lock-switch" className="flex flex-col space-y-1">
                  <span>Fly Lock Actief</span>
                  <span className="font-normal leading-snug text-muted-foreground">
                    Indien actief, kunnen productie targets en yield targets niet worden gewijzigd.
                  </span>
                </Label>
                <Switch
                  id="fly-lock-switch"
                  checked={isFlyLocked}
                  onCheckedChange={setIsFlyLocked}
                />
              </div>
              {/* Save VK Card */}
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <Label htmlFor="save-vk-switch" className="flex flex-col space-y-1">
                  <span>Veiligheid & Kwaliteit Opslaan</span>
                  <span className="font-normal leading-snug text-muted-foreground">
                    Sla de ingevulde Veiligheid & Kwaliteit gegevens op voor de volgende keer.
                  </span>
                </Label>
                <Switch
                  id="save-vk-switch"
                  checked={saveVK}
                  onCheckedChange={setSaveVK}
                />
              </div>
            </CardContent>
          </Card>

          {/* AI Settings Card */}
          <Card>
            <CardHeader>
              <CardTitle>AI Assistent Instellingen</CardTitle>
              <CardDescription>Configureer de AI assistent voor het genereren van overdrachtsrapporten.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-2">
                  <Label htmlFor="ai-enabled-switch">AI Assistent Inschakelen</Label>
                   {aiSettings.enabled && (
                     <Button variant="outline" size="sm" onClick={() => setAiSettingsDialogOpen(true)}>
                       <SettingsIcon className="h-4 w-4 mr-1" /> Configureren
                     </Button>
                   )}
                 </div>
                 <Switch
                   id="ai-enabled-switch"
                   checked={aiSettings.enabled}
                   onCheckedChange={(checked) => handleAiSettingChange('enabled', checked)}
                 />
               </div>
             </CardContent>
           </Card>
           {/* Archive Card */}
           <Card>
             <CardHeader>
               <CardTitle>Incidenten Archiveren</CardTitle>
               <CardDescription>Archiveer alle huidige lopende incidenten en maak de lijst leeg.</CardDescription>
             </CardHeader>
             <CardContent>
               <Button
                 variant="destructive"
                 onClick={() => {
                   if (window.confirm("Weet u zeker dat u alle lopende incidenten wilt archiveren? Dit kan niet ongedaan worden gemaakt.")) {
                     archiveAndClearIncidents();
                     toast.success("Alle lopende incidenten zijn gearchiveerd.");
                   }
                 }}
               >
                 <Archive className="mr-2 h-4 w-4" /> Archiveer Alle Incidenten
               </Button>
             </CardContent>
           </Card>
         </TabsContent>
         {/* --- End Toggles Section --- */}

       </Tabs>

       {/* Removed Dialogs for Material, Section, Equipment */}
       <AiSettingsDialog
         open={aiSettingsDialogOpen}
         onOpenChange={setAiSettingsDialogOpen} // Use onOpenChange and pass the setter directly
         initialSettings={aiSettings} // Corrected prop name
         onSave={handleSaveAiSettings}
       />
     </div>
   );
 };

 export default Settings;
