// src/pages/management/SimpleRegisterPage.tsx
import React, { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase-client';
import { useAuth } from '@/context/AuthContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { toast } from 'sonner';
import { UserPlus, Loader2, ShieldAlert, Users } from 'lucide-react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

const SimpleRegisterPage: React.FC = () => {
  const { user } = useAuth();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isManager, setIsManager] = useState(false);
  const [selectedTeam, setSelectedTeam] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [isAuthorized, setIsAuthorized] = useState(false);
  const [isCheckingAuth, setIsCheckingAuth] = useState(true);

  // Beschikbare ploegen
  const teams = ['Blauw', 'Wit', 'Geel', 'Groen', 'Rood'];

  // Check if current user is authorized to register new users
  useEffect(() => {
    const checkAuthorization = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession();

        if (!session) {
          setIsAuthorized(false);
          setIsCheckingAuth(false);
          return;
        }

        const { data: authData, error: authError } = await supabase
          .from('authorized_managers')
          .select('user_id')
          .eq('user_id', session.user.id)
          .single();

        if (authError) {
          console.error('Autorisatiefout:', authError);
          setIsAuthorized(false);
        } else {
          console.log('Autorisatie resultaat:', authData);
          setIsAuthorized(true);
        }
      } catch (error) {
        console.error('Fout bij controleren autorisatie:', error);
        setIsAuthorized(false);
      } finally {
        setIsCheckingAuth(false);
      }
    };

    checkAuthorization();
  }, []);

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();

    // Controleer of wachtwoorden overeenkomen
    if (password !== confirmPassword) {
      toast.error('Wachtwoorden komen niet overeen');
      return;
    }

    setIsLoading(true);
    setSuccessMessage('');

    try {
      // 1. Registreer de gebruiker met de standaard Supabase signup methode
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            registered_by: user?.email || 'admin',
            registration_date: new Date().toISOString()
          }
        }
      });

      if (authError) {
        throw new Error(`Fout bij registreren: ${authError.message}`);
      }

      if (!authData.user) {
        throw new Error('Gebruiker kon niet worden aangemaakt');
      }

      // 2. Bevestig de e-mail van de gebruiker handmatig via SQL
      const { data: confirmData, error: confirmError } = await supabase
        .rpc('confirm_user_email_v3', {
          user_email: email
        });

      if (confirmError) {
        console.warn('Kon e-mail niet handmatig bevestigen:', confirmError);
        // We gaan door, omdat de gebruiker wel is aangemaakt
      } else {
        console.log('E-mail bevestiging resultaat:', confirmData);
      }

      // 3. Als isManager is aangevinkt, voeg de gebruiker toe aan de authorized_managers tabel
      if (isManager) {
        const { error: managerError } = await supabase
          .from('authorized_managers')
          .insert([{ user_id: authData.user.id }]);

        if (managerError) {
          console.warn('Kon gebruiker niet als beheerder instellen:', managerError);
        }
      }

      // 4. Als een ploeg is geselecteerd (en niet 'none'), voeg de gebruiker toe aan de user_teams tabel
      if (selectedTeam && selectedTeam !== 'none') {
        const { error: teamError } = await supabase
          .from('user_teams')
          .insert([{ user_id: authData.user.id, team: selectedTeam }]);

        if (teamError) {
          console.warn('Kon gebruiker niet aan ploeg toewijzen:', teamError);
        }
      }

      // 5. We hoeven de gebruiker niet uit te loggen, want de admin blijft ingelogd
      // De nieuwe gebruiker wordt niet automatisch ingelogd omdat we signUp gebruiken
      // en niet signIn

      // Succes!
      let successMsg = `Gebruiker ${email} succesvol geregistreerd`;
      if (isManager) successMsg += ' als beheerder';
      if (selectedTeam && selectedTeam !== 'none') successMsg += ` in ploeg ${selectedTeam}`;
      successMsg += '! De gebruiker kan direct inloggen zonder e-mailbevestiging.';

      setSuccessMessage(successMsg);
      toast.success('Gebruiker succesvol geregistreerd');

      // Reset het formulier
      setEmail('');
      setPassword('');
      setConfirmPassword('');
      setIsManager(false);
      setSelectedTeam('');
    } catch (error) {
      console.error('Registratiefout:', error);
      toast.error(error instanceof Error ? error.message : 'Er is een fout opgetreden bij het registreren');
    } finally {
      setIsLoading(false);
    }
  };

  if (isCheckingAuth) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
        <p className="ml-2">Autorisatie controleren...</p>
      </div>
    );
  }

  if (!isAuthorized) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4">
        <ShieldAlert className="h-16 w-16 text-red-500 mb-4" />
        <h1 className="text-2xl font-bold mb-2">Geen toegang</h1>
        <p className="text-center mb-4">
          U heeft geen toestemming om deze pagina te bekijken. Alleen beheerders kunnen nieuwe gebruikers registreren.
        </p>
        <Button onClick={() => window.history.back()}>Terug</Button>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <div className="flex items-center mb-6">
        <UserPlus className="h-6 w-6 mr-2" />
        <h1 className="text-2xl font-bold">Nieuwe gebruiker registreren (Eenvoudig)</h1>
      </div>

      <Card className="max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle>Registratieformulier</CardTitle>
          <CardDescription>
            Maak een nieuwe gebruiker aan voor het Dagstart systeem
          </CardDescription>
        </CardHeader>

        {successMessage && (
          <div className="mx-6 mb-4 p-4 bg-green-50 text-green-700 rounded-md">
            {successMessage}
          </div>
        )}

        <form onSubmit={handleRegister}>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">E-mailadres</Label>
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="<EMAIL>"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="password">Wachtwoord</Label>
              <Input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Wachtwoord"
                required
                minLength={6}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="confirmPassword">Bevestig wachtwoord</Label>
              <Input
                id="confirmPassword"
                type="password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                placeholder="Bevestig wachtwoord"
                required
                minLength={6}
              />
            </div>

            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="isManager"
                  checked={isManager}
                  onCheckedChange={(checked) => {
                    const isChecked = checked === true;
                    setIsManager(isChecked);
                    // Als beheerder wordt aangevinkt, zet team automatisch op 'none'
                    if (isChecked) {
                      setSelectedTeam('none');
                    }
                  }}
                />
                <Label htmlFor="isManager" className="cursor-pointer">Gebruiker is beheerder</Label>
              </div>

              <div className="space-y-2">
                <Label htmlFor="team" className="flex items-center gap-1">
                  <Users className="h-4 w-4" />
                  Ploeg
                </Label>
                <Select value={selectedTeam} onValueChange={setSelectedTeam}>
                  <SelectTrigger id="team" className="w-full">
                    <SelectValue placeholder="Selecteer een ploeg" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">{isManager ? "Geen specifieke ploeg (beheerder)" : "Selecteer een ploeg"}</SelectItem>
                    {teams.map(team => (
                      <SelectItem key={team} value={team}>{team}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground">
                  {isManager ?
                    "Beheerders hebben toegang tot alle ploegen. Selectie van een ploeg is optioneel." :
                    "Gebruikers kunnen alleen acties uitvoeren voor hun eigen ploeg"}
                </p>
              </div>
            </div>
          </CardContent>

          <CardFooter className="flex justify-between">
            <Button type="button" variant="outline" onClick={() => {
              setEmail('');
              setPassword('');
              setConfirmPassword('');
              setIsManager(false);
              setSelectedTeam('');
            }}>
              Annuleren
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Bezig met registreren...
                </>
              ) : (
                'Registreren'
              )}
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
};

export default SimpleRegisterPage;
