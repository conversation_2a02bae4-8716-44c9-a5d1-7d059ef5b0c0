import React from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ProductionLine } from '@/types';

export type FilterBarProps = {
  selectedLine: ProductionLine | 'all';
  setSelectedLine: (line: ProductionLine | 'all') => void;
  dateFilter: '7d' | '14d' | '30d' | '90d' | 'all';
  setDateFilter: (filter: '7d' | '14d' | '30d' | '90d' | 'all') => void;
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  showOnlyDisruptions: boolean;
  setShowOnlyDisruptions: (show: boolean) => void;
  showOnlyBelowTarget: boolean;
  setShowOnlyBelowTarget: (show: boolean) => void;
  onAddDisruptionClick: () => void;
};

export const FilterBar: React.FC<FilterBarProps> = ({
  selectedLine,
  setSelectedLine,
  dateFilter,
  setDateFilter,
  searchQuery,
  setSearchQuery,
  showOnlyDisruptions,
  setShowOnlyDisruptions,
  showOnlyBelowTarget,
  setShowOnlyBelowTarget,
  onAddDisruptionClick,
}) => {
  return (
    <Card>
      <CardContent className="pt-6">
        <div className="flex flex-col md:flex-row gap-4 items-start md:items-center">
          <div className="flex-1 flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <Select
                value={selectedLine}
                onValueChange={(value) => setSelectedLine(value as ProductionLine | 'all')}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecteer productielijn" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Alle lijnen</SelectItem>
                  <SelectItem value="tl1">TL1</SelectItem>
                  <SelectItem value="tl2">TL2</SelectItem>
                  <SelectItem value="p1">P1</SelectItem>
                  <SelectItem value="p2">P2</SelectItem>
                  <SelectItem value="p3">P3</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex-1">
              <Select 
                value={dateFilter} 
                onValueChange={(value) => setDateFilter(value as '7d' | '14d' | '30d' | '90d' | 'all')}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecteer periode" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7d">Laatste 7 dagen</SelectItem>
                  <SelectItem value="14d">Laatste 14 dagen</SelectItem>
                  <SelectItem value="30d">Laatste 30 dagen</SelectItem>
                  <SelectItem value="90d">Laatste 90 dagen</SelectItem>
                  <SelectItem value="all">Alle geschiedenis</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex-1">
              <Input
                placeholder="Zoeken..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full"
              />
            </div>
          </div>
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <Switch
                id="show-disruptions"
                checked={showOnlyDisruptions}
                onCheckedChange={setShowOnlyDisruptions}
              />
              <label htmlFor="show-disruptions" className="text-sm">
                Alleen storingen
              </label>
            </div>
            <Button onClick={onAddDisruptionClick} size="sm">
              Voeg storing toe
            </Button>
            <div className="flex items-center gap-2">
              <Switch
                id="show-below-target"
                checked={showOnlyBelowTarget}
                onCheckedChange={setShowOnlyBelowTarget}
              />
              <label htmlFor="show-below-target" className="text-sm">
                Onder target
              </label>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}; 